import _ from 'lodash';
import { Notify } from 'quasar';
import { useGlobal } from 'stores/global';
import { api } from './axios';
import keycloak from './keycloak';

const SG_DOMAIN = process.env.SG_DOMAIN;

async function fetchUser(api_global, global) {
  if (global.user) return null;

  try {
    const { status, data } = await api_global.get('/api/usuario', {
      params: {
        filters: {
          keycloak_id: {
            filterType: 'EQUALS',
            filterValue: keycloak.subject
          }
        }
      }
    });

    if (status >= 200 && status <= 204) return data.shift();
  } catch (error) {
    throw new Error(error);
  }
}

async function fetchCompany(api, global, domain) {
  if (global.company) return null;
  try {
    // Faz uma requisição GET para a API global para obter os dados da empresa
    const { status, data } = await api.get('/api/empresa', {
      params: {
        filters: {
          'empresaWithEmpresaBD.dominio': {
            filterType: 'EQUALS',
            filterValue: domain
          }
        },
        relationships: [
          'empresaWithAdicional',
          'empresaWithAdicional.planoAdicional',
          'empresaWithCreatedBy',
          'empresaWithEmpresaBD',
          'empresaWithEmpresaContato',
          'empresaWithEmpresaEndereco',
          'empresaWithEmpresaFiscal',
          'empresaWithEmpresaFiscal.empresaFiscalWithFaixaTributacao',
          'empresaWithEmpresaLicenca',
          'empresaWithEmpresaLicencaValor',
          'empresaWithEmpresaUsuario',
          'empresaWithParceiro',
          'empresaWithPlano',
          'empresaWithSolicitacao'
        ]
      }
    });
    if (status >= 200 && status <= 204) {
      const result = data?.data ? data.data.shift() : data.shift();
      return result;
    }
  } catch (error) {
    throw new Error(error);
  }
}

export async function fetchRolesData(router) {
  try {
    // Faz uma requisição GET para a API global para obter os dados das roles permitidas da empresa
    const resp_roles = await api.get(`/api/empresa/informacoes-gerais`);
    const { data: dataRoles } = await api.get(`/api/permissao/`);
    const roles = dataRoles.map((role) => role.nome);

    const response = {
      ...resp_roles,
      data: { ...resp_roles.data, clientRoles: roles }
    };

    // Retorna os dados das roles
    return response.data;
  } catch (error) {
    router.push('/erro-servidor');
  }
}

async function fetchFirstSteps(api, company, router) {
  try {
    // Faz uma requisição GET para a API para obter os dados dos primeiros passos da empresa
    const { status, data } = await api.get(
      `/api/primeiro-passo/${company.controle}`
    );

    if (status >= 200 && status <= 204) return data;
  } catch (error) {
    router.push('/erro-servidor');
  }
}

async function fetchPixContract(api) {
  try {
    // Faz uma requisição GET para a API para obter os dados do contrato pix da empresa
    const { status } = await api.get(`/api/pix/contrato/aceito`);
    return status;
  } catch (error) {
    Notify.create({
      message:
        'Erro no servidor de contrato PIX, não foi possível realizar verificação do contrato',
      color: 'warning',
      position: 'top',
      timeout: 3500,
      icon: 'warning'
    });
    return 500;
  }
}

export async function fetchGeneralInfo() {
  try {
    const { status, data } = await api.get(`/api/empresa/informacoes-gerais`);
    if (status >= 200 && status <= 204) return data;
  } catch (error) {
    throw new Error(error);
  }
}

export async function fetchRoles() {
  try {
    const { status, data } = await api.get(`/api/permissao/`);
    if (status >= 200 && status <= 204) {
      const roles = data?.roles?.length
        ? data.roles
        : data?.map((role) => role.nome);
      return roles;
    }
  } catch (error) {
    throw new Error(error);
  }
}

export async function fetchCompanyConfig() {
  try {
    const { status, data } = await api.get(`/api/empresa/configuracoes`);
    if (status >= 200 && status <= 204) return data;
  } catch (error) {
    throw new Error(error);
  }
}

async function fetchAllData(api, api_global, global, router, domain) {
  try {
    const userPromise = global.user
      ? Promise.resolve(global.user)
      : fetchUser(api_global, global);

    const companyPromise = global.company
      ? Promise.resolve(global.company)
      : fetchCompany(api, global, domain);

    const rolesPromise = global.roles.length
      ? Promise.resolve(global.roles)
      : fetchRoles();

    const generalInfoPromise = global.tempoParaExpirar
      ? Promise.resolve(true)
      : fetchGeneralInfo();

    const pixPromise =
      global.hasPixContract === null
        ? fetchPixContract(api)
        : Promise.resolve(global.hasPixContract);

    const companyConfig = global.companyConfig
      ? Promise.resolve(global.companyConfig)
      : fetchCompanyConfig();

    const [user, company, roles, generalInfo] = await Promise.all([
      userPromise,
      companyPromise,
      rolesPromise,
      generalInfoPromise
    ]);

    if (user) {
      global.user = user;
    }

    if (company) {
      global.company = company;
      global.activatedCompany = company.ativada;
      global.plan = company?.empresaWithPlano?.descricao;
    }

    if (roles) global.roles = roles;

    if (generalInfo && typeof generalInfo == 'object') {
      global.empresaExpirada = generalInfo.empresaExpirada;
      global.tempoParaExpirar = generalInfo.tempoParaExpirar;
      global.deveAlertarSobreExpiracao = generalInfo.deveAlertarSobreExpiracao;
      global.pagamentoAntecipadoDisponivel =
        generalInfo.pagamentoAntecipadoDisponivel;
      global.pagamentoDisponivel = generalInfo.pagamentoDisponivel;
      global.planoConfirmado = generalInfo.planoConfirmado;
      global.perfil = generalInfo.usuarioAutenticado?.perfil?.descricao ?? '';
      global.status = true;
      global.isAdmIncluded =
        generalInfo.usuarioAutenticado?.perfil?.descricao?.includes(
          'ADMINISTRADOR'
        );
    }

    if (!global.empresaExpirada) {
      const [pixStatus, companyConfigData] = await Promise.all([
        pixPromise,
        companyConfig
      ]);

      // Se a requisição foi feita, pixStatus será um número inteiro. Caso contrário, será true ou false (valor do hasPixContract).
      if (Number.isInteger(pixStatus)) {
        global.hasPixContract = pixStatus === 200;
        const showPixContractWarning = localStorage.getItem(
          'showPixContractWarning'
        );
        if (showPixContractWarning === null) {
          localStorage.setItem(
            'showPixContractWarning',
            !global.hasPixContract
          );
        }
      }

      if (companyConfigData?.[0]) {
        global.companyConfig = companyConfigData[0];
        global.qtdCasasDecimais = Number(companyConfigData[0].qtdCasasDecimais);
      }
    }
  } catch (error) {
    router.push('/erro-servidor');
  }
}

export async function routerGuard(to, from, next, api, api_global, router) {
  // Obtém o estado global do aplicativo
  const global = useGlobal();
  // Obtém o domínio da URL atual
  const domain = window.location.hostname.split('.').shift();

  // Verifica se a rota requer autenticação
  if (to.matched.some((route) => route.meta.requiresAuth)) {
    // Verifica se o usuário não está autenticado
    if (!keycloak.authenticated) {
      // Redireciona para a página de login do Keycloak
      window.location.replace(keycloak.createLoginUrl());
      return;
    }

    // Define a função de logout do Keycloak na variável global 'logout' do navegador
    window.logout = keycloak.logout;
    // Busca todos os dados de usuário, empresa e informações gerais.
    await fetchAllData(api, api_global, global, router, domain);

    if (
      global?.company?.ativada == false &&
      to.path.includes('/acesso-bloqueado') == false
    ) {
      router.push('/acesso-bloqueado');

      return;
    } else if (
      global?.company?.ativada &&
      to.path.includes('/acesso-bloqueado')
    ) {
      router.push('/');
    }

    // Verifica se o usuário está tentando acessar a rota '/usuario' ou '/primeiros-passos' e não possui o plano 'ADMINISTRADOR'
    if (
      (to.path === '/usuario' || to.path === '/primeiros-passos') &&
      !global.perfil?.includes('ADMINISTRADOR')
    ) {
      // Redireciona para a rota '/dashboard'
      global.roles.includes('DASHBOARD:MENU')
        ? router.push('/dashboard')
        : router.push('/bem-vindo');
      return;
    }

    // Verifica se os dados da empresa não foram encontrados
    if (!global.company) {
      // Redireciona para a página de empresas
      window.location.replace(
        `${
          Number(process.env.HTTPS) ? 'https://' : 'http://'
        }${SG_DOMAIN}/empresas`
      );
      return;
    }

    // Verifica se os dados da empresa não estão preenchidos e se é a primeira vez que os primeiros passos estão sendo buscados
    if (!global.isCompanyDataFilled && global.isFirstStepFetched === 0) {
      // Obtém os dados dos primeiros passos da empresa através da API
      const firstStepsData = await fetchFirstSteps(api, global.company, router);

      if (!global.empresaExpirada) {
        // Verifica se os dados da empresa estão presentes nos primeiros passos
        if (firstStepsData.empresa) {
          // Define que os dados da empresa estão preenchidos no estado global
          global.isCompanyDataFilled = true;
        } else {
          // Redireciona para a rota '/dados-empresa' para preencher os dados da empresa
          router.push('/dados-empresa');
        }
      } else {
        router.push('/expirado');
      }

      // Define que os primeiros passos foram buscados no estado global
      global.isFirstStepFetched = 1;
    }

    // Verifica se o plano expirou, se a rota não é '/expirado' ou '/plano', e se os dados da empresa estão preenchidos
    if (
      global.empresaExpirada &&
      !to.path.includes('/expirado') &&
      !to.path.includes('/plano')
    ) {
      // Redireciona para a rota '/expirado'
      router.push('/expirado');
    } else if (
      // Verifica se o alerta de expiração pode ser exibido e se a rota atual inclui '/expirado'
      !global.deveAlertarSobreExpiracao &&
      to.path.includes('/expirado') &&
      global.isCompanyDataFilled
    ) {
      // Redireciona para a rota '/dashboard' se o alerta de expiração não puder ser exibido e a rota atual for '/expirado'
      global.roles?.includes('DASHBOARD:MENU')
        ? router.push('/dashboard')
        : router.push('/bem-vindo');
    } else if (to.meta.requiresRole) {
      // Verifica se as roles do usuário não possuem interseção com as roles exigidas pela rota
      if (
        to.meta.roles.length &&
        _.intersection(global.roles, to.meta.roles).length === 0
      ) {
        // Redireciona para a rota '/erro-servidor' se o usuário não possuir as roles necessárias
        router.push({ path: '/nao-autorizado' });
      }
    }
  }

  // Chama a próxima função do ciclo de navegação do Vue Router
  next();
}
