<template>
  <div
    v-if="data.length == 0"
    class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center"
  >
    <q-img src="/no-table-data.svg" style="max-width: auto; height: auto" />
    <p class="tw-text-xs tw-font-bold">Nenhum registro encontrado</p>
  </div>
  <div v-else class="tw-size-full tw-overflow-visible tw-px-1 tw-py-2">
    <!-- Gráfico -->
    <div class="tw-w-full">
      <apexchart
        type="donut"
        :options="chartOptions"
        :series="series"
        width="100%"
        height="128"
        class="tw-justify-self-center"
      />
    </div>

    <!-- Legendas -->
    <div class="tw-flex tw-w-full tw-flex-col tw-items-start">
      <div
        v-for="(item, index) in data"
        :key="index"
        class="tw-flex tw-w-full tw-flex-wrap tw-items-center tw-justify-between tw-border-b tw-px-3 tw-py-1"
      >
        <span class="tw-truncate tw-text-[9px]">
          <q-icon
            name="circle"
            :style="{ color: colors[index] }"
            :size="'8px'"
          />
          {{ item[labelKey] ?? '' }}
        </span>
        <TooltipCustom :text-tooltip="item[labelKey]" />
        <span class="tw-text-[10px]" v-if="formatMoney"
          >R${{ currencyFormat(item[valueKey], 2) ?? '' }}</span
        >
        <span v-else>{{
          (Number(item[valueKey]) || 0).toFixed(4).replace('.', ',')
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, defineProps, toRefs } from 'vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { QIcon } from 'quasar';
import { currencyFormat } from 'components/utils/currencyFormat';

const props = defineProps({
  data: { type: Array, required: true, default: () => [] },
  colors: {
    type: Array,
    required: false,
    default: () => ['#FFC700', '#0059DF', '#00B090', '#E06621', '#8DBBFF']
  },
  // title: { type: String, required: true },
  labelKey: { type: String, required: true },
  valueKey: { type: String, required: true },
  formatMoney: { type: Boolean, required: false, default: false }
});

const { colors } = toRefs(props);
// Calcula os valores da série com base na chave especificada (valueKey)
const series = computed(() =>
  props.data.map((item) => parseInt(item[props.valueKey]))
);

const chartOptions = computed(() => ({
  chart: {
    type: 'donut',

    toolbar: {
      show: false
    }
  },
  plotOptions: {
    pie: {
      donut: {
        size: '40%'
      }
    }
  },
  colors: props.colors,
  legend: {
    show: false
  },
  dataLabels: {
    enabled: false
  },

  tooltip: {
    enabled: true,

    custom: ({ seriesIndex, w }) => {
      const label = props.data[seriesIndex]?.[props.labelKey] || '';
      const value = w.globals.series[seriesIndex] || 0;
      return props.formatMoney
        ? `<div style="padding: 1px; font-size: 8px;  z-index: 9999;">
            ${label}: <br> R$${currencyFormat(value, 2)}
         </div>`
        : `<div style="padding: 1px; font-size: 8px;  z-index: 9999;">
            ${label}: <br> ${value}
         </div>`;
    }
  },
  responsive: [
    {
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        }
      }
    }
  ]
}));
</script>
