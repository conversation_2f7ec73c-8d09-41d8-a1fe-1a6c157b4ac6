<template>
  <div class="tw-flex tw-flex-col">
    <div
      class="tw-relative tw-mb-1 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-justify-center tw-py-2"
    >
      <q-icon
        v-if="!props.readonly && $q.screen.gt.sm"
        name="info"
        color="primary"
        size="18px"
        dense
        unelevated
        flat
        padding="2px 2px"
        class="tw-absolute -tw-top-3 tw-right-0"
      >
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -5]"
          text-tooltip="Digite '/' antes do texto para pesquisar por nome."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -40]"
          text-tooltip="Ao digitar R25*1 e pressionar 'Enter', será adicionado 25 reais do produto de código 1."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -90]"
          text-tooltip="Ao digitar 25*1 e pressionar 'Enter', será adicionado 25 unidades do produto de código 1."
        />
      </q-icon>
      <ProductSelect
        v-if="!props.readonly"
        ref="productSearchRef"
        scope="register-shopping-register-page"
        class="productInput tw-w-full"
        :store="stock"
        :loading="false"
        placeholder="Buscar Item"
        :filters="stockFilters"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'nome']"
        :value="stockEditValue"
        default-search="1"
        @searching="(isSearching) => (isTableLoading = isSearching)"
        @imported-products="handleImportedProducts"
        @product-found="handleAddItem"
      />
    </div>
    <div
      class="customScroll tw-relative tw-flex !tw-max-h-[800px] tw-flex-col tw-items-start tw-justify-start tw-gap-2 tw-overflow-auto tw-pb-4 md:!tw-max-h-[300px]"
      ref="itemsScrollContainerRef"
    >
      <div
        class="customGrid tw-sticky tw-top-0 tw-z-[3] tw-w-full tw-min-w-fit tw-items-end tw-justify-start tw-gap-customGridGapCards tw-bg-SGBRGrayBG tw-px-2 tw-py-1 tw-pb-2 md:tw-grid"
        v-if="$q.screen.gt.sm"
      >
        <p
          class="tw-pl-[10px] tw-text-textsSGBR-gray"
          v-for="item in todoLabels"
          :key="item.id"
        >
          {{ item.replace('*', '')
          }}<span v-if="item.indexOf('*') != -1" class="require">*</span>
        </p>
      </div>

      <div ref="itemsContainerRef">
        <q-virtual-scroll
          :items="inputs.data"
          class="!tw-min-w-full tw-overflow-clip"
          v-slot="{ item, index }"
          :virtual-scroll-slice-size="8"
          v-if="inputs.data.length > 0"
        >
          <div
            v-if="!inputs.data[index]?.deletar"
            class="customGrid purchaseInputs tw-flex tw-w-full tw-flex-col tw-gap-customGridGapCards tw-gap-y-0 md:tw-grid md:tw-bg-transparent"
          >
            <q-input
              type="text"
              label="Produto/Serviço"
              :model-value="item.selectData?.descricaoCodigo"
              dense
              outlined
              readonly
              stack-label
            >
              <TooltipCustom :text-tooltip="item.selectData?.descricaoCodigo" />
            </q-input>
            <Money
              v-model="item.qtdeCompra"
              outlined
              dense
              label="Qtde. Compra"
              :max="9999999.9999"
              stack-label
              :key="item.usaSerial && item.usaGrade"
              :readonly="props.readonly || item.usaSerial || item.usaGrade"
              :text-tooltip="
                item.usaSerial || item.usaGrade
                  ? 'Quantidade controlada pelo serial ou grade'
                  : null
              "
              :decimals-quantity="item.usaSerial ? 0 : 4"
              label-slot
              class="tw-bg-white"
              required
              :rules="[
                (val) => (val && val != 0 ? true : 'O campo é obrigatório')
              ]"
            />
            <SelectDefault
              class="tw-col-span-none"
              :options="['*', '/']"
              outlined
              bordered
              stack-label
              label="Fator"
              dense
              behavior="menu"
              :info-tooltip="
                fiscal
                  ? 'Operação'
                  : 'Utilizado em compras associadas a um documento fiscal'
              "
              :model-value="item.operadorFatorConversaoUsado"
              @update:model-value="
                (data) => {
                  item.operadorFatorConversaoUsado = data;
                }
              "
              :disabled="props.readonly || !fiscal"
              :readonly="props.readonly || !fiscal"
            />
            <Money
              v-model="item.valorConversorUsado"
              outlined
              required
              dense
              :decimals-quantity="4"
              label="Valor conversão"
              class="tw-bg-white"
              :text-tooltip="
                fiscal
                  ? 'Valor para base de cálculo'
                  : 'Utilizado em compras associadas a um documento fiscal'
              "
              :disabled="props.readonly || !fiscal"
            />
            <Money
              v-model="item.qtde"
              outlined
              dense
              label="Qtde."
              :max="9999999.9999"
              stack-label
              readonly
              :text-tooltip="
                fiscal
                  ? 'Quantidade após fator de conversão'
                  : 'Utilizado em compras associadas a um documento fiscal'
              "
              :decimals-quantity="4"
              class="tw-bg-white"
              :disabled="props.readonly || !fiscal"
              @change="
                (value) => {
                  if (value && value > 0) {
                    item.valorUnitario = calcValorUnitario(item, index);
                  }
                }
              "
            />
            <Money
              type="text"
              v-model="item.valorUnitario"
              :decimals-quantity="4"
              outlined
              required
              dense
              label="Valor unitário"
              class="tw-bg-white"
              :rules="[
                (val) =>
                  val && toFloat(val) != 0 ? true : 'O campo é obrigatório'
              ]"
              :disabled="props.readonly"
            />
            <Money
              v-model="item.valorTotal"
              :decimals-quantity="4"
              :max="*********.99"
              outlined
              readonly
              dense
              label="Total"
              class="tw-bg-white"
              :disabled="props.readonly"
            />
            <div
              class="tw-flex tw-flex-col tw-items-end tw-justify-start md:tw-flex-row"
            >
              <q-btn
                v-if="item.selectData"
                @click="() => openRegisterTaxes(item, index)"
                :color="errorItems.includes(index) ? 'negative' : 'primary'"
                icon="find_in_page"
                unelevated
                dense
                class="tw-relative tw-my-auto !tw-min-h-[26px] tw-w-full tw-justify-center tw-bg-gray-300 tw-leading-5 md:!tw-min-h-full md:tw-w-fit md:tw-max-w-[30px] md:tw-bg-transparent"
                flat
                size="15px"
                padding="0px 5px"
              >
                <TooltipCustom
                  v-if="!errorItems.includes(index)"
                  text-tooltip="Detalhes do item"
                />
                <TooltipCustom
                  class="tw-bg-SGBRRedBrown"
                  v-if="errorItems.includes(index)"
                  text-tooltip="Informações obrigatórias ainda não preenchidas"
                />
              </q-btn>
              <q-btn
                v-if="!props.readonly"
                @click="() => removeItem(index, item?.controle)"
                color="red-5"
                icon="delete"
                :label="$q.screen.gt.sm ? '' : 'Remover item'"
                unelevated
                dense
                class="tw-my-auto !tw-min-h-[26px] tw-w-full tw-bg-gray-300 md:!tw-min-h-full md:tw-w-fit md:tw-max-w-[30px] md:tw-bg-transparent"
                flat
                size="15px"
                padding="0px 5px"
              >
                <TooltipCustom text-tooltip="Remover item" />
              </q-btn>
            </div>
          </div>
        </q-virtual-scroll>
      </div>

      <div
        v-if="inputs.data.filter((item) => !item.deletar).length < 1"
        class="tw-flex tw-w-full tw-flex-row tw-flex-wrap tw-items-center tw-justify-center"
      >
        <span class="tw-my-4 tw-font-bold tw-text-SGBRGray">
          Nenhum item adicionado
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import ProductSelect from 'components/generic/input/ProductSelect.vue';
import SelectDefault from 'components/generic/input/Select/default.vue';
import Taxes from 'components/modal/purchase/Taxes.vue';
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import _ from 'lodash';
import { useQuasar } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import round from 'src/components/utils/round';
import { toFloat } from 'src/services/utils';
import { useStock } from 'stores/api/stock';
import useEventsBus from 'stores/eventBus';
import { reactive, ref, toRaw, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import Money from '../input/Money.vue';

const stockEditValue = ref();

const { emit: emitBus } = useEventsBus();
const props = defineProps({
  dataItems: { type: Array, default: () => [] },
  btnAddSelect: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  fiscal: {
    type: Boolean,
    required: false,
    default: false
  }
});

const emit = defineEmits(['updatedTodo', 'propsDone']);

const $q = useQuasar();
const route = useRoute();
const { params } = route;

const inputs = reactive({ data: [] });
const selectInputRef = ref();

const itemsContainerRef = ref();

const itemsScrollContainerRef = ref();

// Select de produto
const productSearchRef = ref();
const stock = useStock();
const stockFilters = {
  deleted_at: {
    filterType: 'NULL'
  }
};

const todoLabels = [
  'Produtos/Serviços*',
  'Qtde. Compra',
  'Fator',
  'Valor conversão',
  'Qtde.',
  'Valor unitário',
  'Total (R$)'
];

const openRegisterTaxes = async (item, index = null) => {
  function scrollToTheFinalOfList() {
    nextTick(() => {
      // Precisei de setTimeout porque não mandava o valor atual element height (componente atualiza nesse momento)
      setTimeout(() => {
        const elementHeight = itemsContainerRef.value?.getBoundingClientRect()?.height;

        if (elementHeight && itemsScrollContainerRef.value) {
          itemsScrollContainerRef.value?.scrollTo(0, elementHeight);
        }
      }, 100);
    });
  }

  const openRegisterTaxesPromise = await new Promise((resolve) => {
    $q.dialog({
      component: DialogModalLayout,
      componentProps: {
        componentRef: Taxes,
        data: item,
        scope: 'taxas-' + item?.codProduto,
        readonly: props.readonly
      }
    })
      .onOk(async (data) => {
        if (!data.controle && !index && index !== 0) {
          data.qtdeTributavelCompra = data.qtdeTributavel;
          inputs.data.push(data);

          scrollToTheFinalOfList();
          return;
        }

        inputs.data[index] = { ...inputs.data[index], ...data };

        scrollToTheFinalOfList();

        resolve(true);
      })
      .onCancel(async () => {
        resolve(false);
      });
  });

  return openRegisterTaxesPromise;
};

const handleImportedProducts = async (items) => {
  for await (const item of items) {
    await handleAddItem(item);
  }
};

const handleAddItem = async (product) => {
  const addingItem = {};
  addingItem.operadorFatorConversaoUsado =
    product?.produtoWithCaracteristica?.operadorFatorConversaoCompra || '*';
  addingItem.valorConversorUsado =
    product?.produtoWithCaracteristica?.valorConversorCompra || 1;
  addingItem.qtdeCompra = 1;
  addingItem.valorUnitario = product?.produtoWithEstoque.precoCusto;
  addingItem.valorUnitarioCompra = product?.produtoWithEstoque.precoCusto;
  addingItem.valorTotal = 0;
  addingItem.codProduto = product?.controle;
  addingItem.codCompra = params.controle;
  addingItem.selectData = product;
  addingItem.usaLote = product?.produtoWithCaracteristica?.usaLote;
  addingItem.usaGrade = product?.produtoWithCaracteristica?.usaGrade;
  addingItem.usaSerial = product?.produtoWithCaracteristica?.usaSerial;
  addingItem.codCompra = product?.codCompra ?? null;
  addingItem.ncmEditValue =
    product?.produtoWithFiscal?.produtoFiscalWithNcmNew ??
    product.ncmEditValue ??
    null;

  productSearchRef?.value.clearText();

  await openRegisterTaxes(addingItem);
};

const removeItem = (index, controle = null) => {
  if (!controle) inputs.data.splice(index, 1);
  else inputs.data[index].deletar = true;
  updateRequiredInputs();
};

const reset = () => {
  inputs.data.length = 0;
};

const updateRequiredInputs = () => {
  setTimeout(() => {
    emitBus('verifyRequiredInputs');
  }, 1500);
};

defineExpose({ reset });

watch(
  () => props.dataItems,
  (newData) => {
    if (newData.length > 0) {
      inputs.data.length = 0;
      selectInputRef.value?.forEach((item) => item.reset());

      for (let i = 0; i < newData.length; i++) {
        inputs.data.push({
          ...newData[i],
          codProduto: newData[i].codProduto,
          codCompra: newData[i].codCompra,
          controle: newData[i].controle,
          qtdeCompra: parseFloat(newData[i].qtdeCompra) || '1.00',
          qtde: parseFloat(newData[i].qtde),
          valorUnitario: newData[i].valorUnitario,
          valorTotal: newData[i].valorTotal,
          selectData: newData[i].selectData,
          operadorFatorConversaoUsado: newData[i].operadorFatorConversaoUsado,
          valorConversorUsado: newData[i].valorConversorUsado
        });
      }
    }
  },
  { immediate: true }
);

const errorItems = ref([]);

watch(
  inputs.data,
  (newForms) => {
    const itemsData = _.cloneDeep(newForms);
    itemsData.forEach((item) => {
      item.valorTotal = parseFloat(item.valorTotal || 0);

      delete item.id;
    });

    updateRequiredInputs();
    emit('updatedTodo', toRaw(itemsData));
    emit(
      'propsDone',
      newForms.map((item) =>
        [
          !!item.codProduto,
          parseFloat(item.qtdeCompra) > 0,
          item.valorUnitario > 0
        ].every((item) => item)
      )
    );
  },
  { deep: true, immediate: true }
);

const calculateTotal = (item) => {
  if (item.codProduto) {
    return round(
      Number(item.qtdeCompra) *
        (props.fiscal
          ? Number(item.valorUnitarioCompra)
          : Number(item.valorUnitario)),
      4
    );
  } else {
    return 0;
  }
};

const valorUnitarioInicial = ref([]);

/**
 * Calcula o valor unitário baseado na alteração do valor de conversão.
 * @param item
 */
const calcValorUnitario = (item, index) => {
  const {
    operadorFatorConversaoUsado,
    valorConversorUsado,
    valorUnitarioCompra,
    valorUnitario
  } = item;

  let valorUnitarioConsiderado = props.fiscal
    ? Number(valorUnitarioCompra) || Number(valorUnitario)
    : Number(valorUnitario);

  if (!valorUnitarioInicial.value[index])
    valorUnitarioInicial.value[index] = valorUnitarioConsiderado;

  valorUnitarioConsiderado =
    valorUnitarioInicial.value[index] > valorUnitarioConsiderado
      ? valorUnitarioInicial.value[index]
      : valorUnitarioConsiderado;

  if (operadorFatorConversaoUsado === '*') {
    return round(valorUnitarioConsiderado / Number(valorConversorUsado), 4);
  } else {
    return round(valorUnitarioConsiderado * Number(valorConversorUsado), 4);
  }
};

/**
 * Calcula a quantidade baseada nos fatores de conversão
 * @param item
 */
const calculateQuantity = (item) => {
  const { operadorFatorConversaoUsado, qtdeCompra, valorConversorUsado } = item;
  if (operadorFatorConversaoUsado === '/') {
    return round(Number(qtdeCompra) / Number(valorConversorUsado), 4);
  } else {
    return round(Number(qtdeCompra) * Number(valorConversorUsado), 4);
  }
};

const calculateQuantityTributavel = (item) => {
  const {
    operadorFatorConversaoUsado,
    qtdeTributavelCompra,
    valorConversorUsado
  } = item;
  if (operadorFatorConversaoUsado === '/') {
    return round(Number(qtdeTributavelCompra) / Number(valorConversorUsado), 4);
  } else {
    return round(Number(qtdeTributavelCompra) * Number(valorConversorUsado), 4);
  }
};

watch(
  inputs.data,
  (newForms) => {
    newForms.forEach((item) => {
      item.qtde = calculateQuantity(item);
      item.valorTotal = calculateTotal(item);
      item.qtdeTributavel = calculateQuantityTributavel(item);
    });
  },
  { deep: true, immediate: true }
);
</script>

<style scoped>
@screen md {
  .customGrid {
    grid-template-columns: minmax(170px, 220px) 140px 78px repeat(3, 140px) 120px 55px;
  }
}
</style>
