<template>
  <div class="tw-flex tw-flex-col">
    <div
      v-if="!isDevolucao"
      class="tw-relative tw-mb-1 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-justify-center tw-py-2"
    >
      <q-icon
        v-if="$q.screen.gt.sm"
        name="info"
        color="primary"
        size="18px"
        dense
        unelevated
        flat
        padding="2px 2px"
        class="tw-absolute -tw-top-3 tw-right-0"
      >
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -5]"
          text-tooltip="Digite '/' antes do texto para pesquisar por nome."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -40]"
          text-tooltip="Ao digitar R25*1 e pressionar 'Enter', será adicionado 25 reais do produto de código 1."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -90]"
          text-tooltip="Ao digitar 25*1 e pressionar 'Enter', será adicionado 25 unidades do produto de código 1."
        />
      </q-icon>
      <ProductSelect
        :disabled="disabled"
        :tooltip="
          disabled
            ? 'Selecione um Destinatário/remetente para adicionar itens'
            : ''
        "
        ref="productSearchRef"
        scope="register-sessao-emissao-nf"
        class="productInput tw-w-full"
        :store="stock"
        :loading="false"
        placeholder="Buscar Item"
        :filters="stockFilters"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'nome']"
        :value="stockEditValue"
        :default-search="Number(nfeConfigData?.[0]?.buscaPadrao || '1')"
        @searching="(isSearching) => (isTableLoading = isSearching)"
        @imported-products="handleImportedProducts"
        @product-found="handleAddItem"
      />
    </div>
    <!----- INÍCIO Q-TABLE ----->
    <q-table
      ref="nfeTableRef"
      :rows="rows.filter((row) => !row?.deletar)"
      :columns="columns"
      :wrap-cells="true"
      :loading="loading"
      :hide-pagination="true"
      :selected-rows-label="() => ''"
      :rows-per-page-options="[0]"
      class="tw-mt-3 tw-grow"
      :table-class="
        rows.filter((item) => !item.deletar).length > 0
          ? 'pdv-table customScroll tw-text-textsSGBR-gray tw-z-20 relative tw-h-full'
          : 'customScroll'
      "
      table-header-class="tw-font-bold tw-text-xl tw-bg-SGBRGrayBG tw-rounded-xl"
      selection="none"
      row-key="index"
      flat
      dense
    >
      <template #no-data>
        <div
          class="tw-flex tw-w-full tw-items-center tw-justify-center tw-pb-0 tw-pt-7"
        >
          <span class="tw-font-bold tw-text-SGBRGray"
            >Nenhum item adicionado</span
          >
        </div>
      </template>
      <template #header-cell-actions="data">
        <q-th
          :style="{ padding: '0.3rem 0.5rem' }"
          :class="{
            'sticky-table-column-header-with-line !tw-right-0 !tw-w-[40px] !tw-min-w-[40px] tw-text-center':
              data.col.name == 'actions' && !data.col.headerClasses
          }"
          :props="data"
        >
          {{ data.col.label }}
        </q-th>
      </template>

      <template #header-cell-codCfop="data">
        <q-th :props="data">
          {{ data.col.label }} <span class="text-negative">*</span>
        </q-th>
      </template>

      <template #header-cell-codCstCsosn="data">
        <q-th :props="data">
          {{ data.col.label }} <span class="text-negative">*</span>
        </q-th>
      </template>

      <!----- INÍCIO CÉLULA CFOP ----->
      <template #body-cell-codCfop="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="flex-row tw-flex tw-min-w-[60px] tw-overflow-hidden !tw-text-ellipsis tw-whitespace-normal"
          >
            <SelectInput
              :store="cfopStore"
              :value="data.row.cfopEditValue"
              store-key="descricaoCodigo"
              :filter-or="['descricao', 'codcfop']"
              :order-by="{ codCfop: 'asc' }"
              :borderless="false"
              :outlined="false"
              label-select=""
              hide-bottom-space
              :loading="false"
              :hide-dropdown-icon="true"
              :clearable="false"
              display-prop="codCfop"
              class="pdv-select -tw-mt-3 tw-flex tw-w-full !tw-min-w-[70px] tw-flex-row tw-items-center tw-justify-center !tw-p-0 tw-text-textsSGBR-gray"
              input-class="tw-text-[12px]"
              popup-content-class="!tw-w-[250px] xl:!tw-w-[300px]"
              @value="
                async (value) => {
                  const validacaoCstCsosnCfop = validarCstCsosnCfop({
                    cfop: value?.codCfop,
                    cstCsosn: data.row?.fiscal?.codCstCsosn,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  const validacaoCstCsosnCfopAnterior = validarCstCsosnCfop({
                    cfop: data.row?.fiscal?.cfop,
                    cstCsosn: data.row?.fiscal?.codCstCsosn,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  if (validacaoCstCsosnCfopAnterior && !validacaoCstCsosnCfop) {
                    const response = await abrirModalAlteracaoDadosFiscais();

                    if (!response) {
                      data.row.cfopEditValue = {
                        controle: data.row?.cfopEditValue?.controle,
                        codCfop: data.row?.cfopEditValue?.codCfop,
                        descricaoCodigo:
                          data.row?.cfopEditValue?.descricaoCodigo
                      };
                      return;
                    }
                  } else if (
                    !validacaoCstCsosnCfopAnterior &&
                    validacaoCstCsosnCfop
                  ) {
                    data.row.fiscal.fetchImpostos = true;
                  }

                  data.row.cfopEditValue = value;

                  data.row.fiscal = {
                    ...data.row.fiscal,
                    codCfop: value?.controle,
                    cfop: value?.codCfop
                  };
                }
              "
            >
            </SelectInput>
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA CFOP ----->

      <!----- INÍCIO CÉLULA CFOP ----->
      <template #body-cell-codCstCsosn="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[60px] tw-overflow-hidden !tw-text-clip tw-whitespace-normal"
          >
            <SelectInput
              v-if="
                global.company?.empresaWithEmpresaFiscal?.crt === '1' ||
                global.company?.empresaWithEmpresaFiscal?.crt === '5'
              "
              :store="csosnStore"
              :value="data.row.cstCsosnEditValue"
              store-key="descricaoCodigo"
              :filter-or="['descricao', 'codigo']"
              :order-by="{ codigo: 'asc' }"
              :borderless="false"
              :outlined="false"
              :loading="false"
              label-select=""
              hide-bottom-space
              :hide-dropdown-icon="true"
              :clearable="false"
              display-prop="codigo"
              class="pdv-select -tw-mt-3 tw-flex tw-w-full !tw-min-w-[70px] tw-flex-row tw-items-center tw-justify-center !tw-p-0 tw-text-textsSGBR-gray"
              input-class="tw-text-[12px]"
              popup-content-class="!tw-w-[250px] xl:!tw-w-[300px]"
              @value="
                async (value) => {
                  const validacaoCstCsosnCfop = validarCstCsosnCfop({
                    cfop: data.row?.fiscal?.cfop,
                    cstCsosn: value?.codigo,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  const validacaoCstCsosnCfopAnterior = validarCstCsosnCfop({
                    cfop: data.row?.fiscal?.cfop,
                    cstCsosn: data.row?.fiscal?.codCstCsosn,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  if (validacaoCstCsosnCfopAnterior && !validacaoCstCsosnCfop) {
                    const response = await abrirModalAlteracaoDadosFiscais();

                    if (!response) {
                      data.row.cstCsosnEditValue = {
                        codigo: data.row?.cstCsosnEditValue?.codigo,
                        descricaoCodigo:
                          data.row?.cstCsosnEditValue?.descricaoCodigo
                      };

                      return;
                    }
                  } else if (
                    !validacaoCstCsosnCfopAnterior &&
                    validacaoCstCsosnCfop
                  ) {
                    data.row.fiscal.fetchImpostos = true;
                  }

                  data.row.cstCsosnEditValue = value;
                  data.row.fiscal = {
                    ...data.row.fiscal,
                    codCstCsosn: value?.codigo
                  };
                }
              "
            >
            </SelectInput>
            <SelectInput
              v-else
              :store="cstStore"
              :value="data.row.cstCsosnEditValue"
              store-key="descricaoCodigo"
              :filter-or="['descricao', 'codigo']"
              :order-by="{ codigo: 'asc' }"
              :borderless="false"
              :outlined="false"
              label-select=""
              :loading="false"
              hide-bottom-space
              :hide-dropdown-icon="true"
              :clearable="false"
              display-prop="codigo"
              class="pdv-select -tw-mt-3 tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-center !tw-p-0 tw-text-textsSGBR-gray"
              input-class="tw-text-[12px]"
              popup-content-class="!tw-w-[250px] xl:!tw-w-[300px]"
              @value="
                async (value) => {
                  const validacaoCstCsosnCfop = validarCstCsosnCfop({
                    cfop: data.row?.fiscal?.cfop,
                    cstCsosn: value?.codigo,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  const validacaoCstCsosnCfopAnterior = validarCstCsosnCfop({
                    cfop: data.row?.fiscal?.cfop,
                    cstCsosn: data.row?.fiscal?.codCstCsosn,
                    finalidadeNaturezaOperacao:
                      operationNature.value?.finalidadeOperacao
                  });

                  if (validacaoCstCsosnCfopAnterior && !validacaoCstCsosnCfop) {
                    const response = await abrirModalAlteracaoDadosFiscais();

                    if (!response) {
                      data.row.cstCsosnEditValue = {
                        codigo: data.row?.cstCsosnEditValue?.codigo,
                        descricaoCodigo:
                          data.row?.cstCsosnEditValue?.descricaoCodigo
                      };

                      return;
                    }
                  } else if (
                    !validacaoCstCsosnCfopAnterior &&
                    validacaoCstCsosnCfop
                  ) {
                    data.row.fiscal.fetchImpostos = true;
                  }

                  data.row.cstCsosnEditValue = value;
                  data.row.fiscal = {
                    ...data.row.fiscal,
                    codCstCsosn: value?.codigo,
                    motivoIcmsDeson: null,
                    codBeneficio: null
                  };
                }
              "
            >
            </SelectInput>
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA CFOP ----->

      <!----- INÍCIO CÉLULA QUANTIDADE ----->
      <template #body-cell-qtde="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <Money
              ref="qtdeRef"
              v-model="data.row.qtde"
              :max="99999999.9999"
              :decimals-quantity="4"
              dense
              :readonly="data.row.controle && props.disableDeleteItem"
              hide-bottom-space
              class="pdv-money tw-m-0 -tw-mt-3 tw-flex tw-flex-row tw-items-center tw-justify-center !tw-p-0 tw-text-textsSGBR-gray"
              input-class="tw-min-w-[90px] tw-max-w-[100px] tw-text-[12px] tw-text-center tw-text-textsSGBR-gray tw-m-0 tw-ml-2 -tw-mb-2 !tw-p-0"
              @blur="handleQtdeBlur($event, data)"
              @update:model-value="
                () => {
                  data.row.valorBruto = calcValorBruto(data.row);
                  data.row.fiscal.valorIcmsDeson = calcValorIcmsDeson(data.row);
                }
              "
            />
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA QUANTIDADE ----->

      <!----- INÍCIO CÉLULA VALOR UNITÁRIO ----->
      <template #body-cell-valorUnitario="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <Money
              ref="valorUnitarioRef"
              v-model="data.row.valorUnitario"
              :max="99999999.99999"
              :decimals-quantity="global.qtdCasasDecimais"
              dense
              :readonly="data.row.controle && props.disableDeleteItem"
              hide-bottom-space
              class="pdv-money tw-m-0 -tw-mt-3 tw-flex tw-flex-row tw-items-center tw-justify-center !tw-p-0 tw-text-textsSGBR-gray"
              input-class="tw-min-w-[90px] tw-max-w-[100px] tw-text-[12px] tw-text-center tw-text-textsSGBR-gray tw-m-0 tw-ml-2 -tw-mb-2 !tw-p-0"
              @blur="handleValorUnitarioBlur($event, data)"
              @update:model-value="
                (value) => {
                  if (value) {
                    data.row.valorBruto = calcValorBruto(data.row);
                    data.row.fiscal.valorIcmsDeson = calcValorIcmsDeson(
                      data.row
                    );
                  }
                }
              "
            />
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA VALOR UNITÁRIO ----->

      <!----- INÍCIO CÉLULA VALOR TOTAL ----->
      <template #body-cell-valorBruto="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <span>
              {{ currencyFormat(data.row.valorBruto, 2) }}
            </span>
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA VALOR TOTAL ----->

      <!----- INÍCIO CÉLULA VALOR LÍQUIDO ----->
      <template #body-cell-valorLiquido="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            {{
              currencyFormat(
                data.row.valorLiquido ??
                  (data.row.valorUnitario ?? 0) * (data.row.qtde ?? 0),
                2
              )
            }}
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA VALOR LÍQUIDO ----->

      <!----- INÍCIO CÉLULA AÇÕES ----->
      <template #body-cell-actions="data">
        <q-td
          :props="data"
          :class="{
            'sticky-table-column-with-line !tw-right-0 !tw-w-[40px] !tw-min-w-[40px] !tw-px-2 tw-text-center':
              !data.col.classes
          }"
        >
          <div
            class="tw-flex tw-flex-row tw-items-center tw-justify-center tw-gap-2"
          >
            <div
              v-for="action in getFilteredActions(data.row)"
              class="tw-flex tw-flex-row tw-items-center"
              :key="action.name"
            >
              <template v-if="!action.isMenu">
                <component
                  v-if="iconComponents[action.icon]"
                  :is="iconComponents[action.icon]"
                  @click="action.action(data.row, data)"
                  :class="{
                    'is-disabled':
                      action?.condition && !action.condition(data.row, data)
                  }"
                >
                  <TooltipCustom
                    :text-tooltip="action.tooltip"
                    class="tw-bg-textsSGBR-gray"
                  />
                </component>
              </template>
              <template v-else>
                <q-icon
                  :name="action.icon"
                  color="primary"
                  size="16px"
                  class="tw-h-[0.9rem] tw-w-[0.9rem] tw-cursor-pointer tw-rounded-[0.3rem] tw-border-2 tw-border-SGBRGrayLighten"
                  @mouseenter="menuOver[data.rowIndex] = true"
                  @mouseleave="menuOver[data.rowIndex] = false"
                >
                  <q-menu
                    :offset="[0, 5]"
                    v-model="menu[data.rowIndex]"
                    @click="
                      menuOver[data.rowIndex] = false;
                      listOver[data.rowIndex] = false;
                    "
                    @mouseenter="listOver[data.rowIndex] = true"
                    @mouseleave="listOver[data.rowIndex] = false"
                    class="tw-rounded-md"
                  >
                    <q-list style="min-width: 120px">
                      <q-item
                        class="tw-px-2 tw-py-2"
                        v-for="menuItem in action.menuItems"
                        :key="menuItem.name"
                        @click="menuItem.action(data.row, data.rowIndex)"
                        clickable
                        v-close-popup
                      >
                        <q-item-section side>
                          <component :is="iconComponents[menuItem.icon]">
                            <TooltipCustom
                              :text-tooltip="menuItem.tooltip"
                              class="tw-bg-textsSGBR-gray"
                            />
                          </component>
                        </q-item-section>
                        <q-item-section> {{ menuItem.label }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-icon>
              </template>
            </div>
          </div>
        </q-td>
      </template>
      <!----- FIM CÉLULA AÇÕES ----->

      <!----- Seleção ----->

      <!----- LOADER ----->
      <template #loading>
        <q-inner-loading showing color="primary" />
      </template>
    </q-table>
    <!----- FIM Q-TABLE ----->
  </div>
</template>

<script setup>
import { ref, toRefs, watch, shallowRef, nextTick, toRaw } from 'vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import TrashIcon from 'components/icons/TrashIcon.vue';
import Money from 'src/components/generic/input/Money.vue';
import { useStock } from 'stores/api/stock';
import { useGlobal } from 'src/stores/global';
import DiscountIcon from 'components/icons/DiscountIcon.vue';
import notify from 'src/components/utils/notify';
import SelectInput from 'components/generic/input/Select/index.vue';
import { useCfopData } from 'src/stores/api/cfop';
import { useCst } from 'src/stores/api/cst';
import { useCsosn } from 'src/stores/api/csosn';
import { currencyFormat } from 'components/utils/currencyFormat';
import round from 'src/components/utils/round';
import SerialIcon from 'components/icons/SerialIcon.vue';
import GradeIcon from 'components/icons/GradeIcon.vue';
import LoteIcon from 'components/icons/LoteIcon.vue';
import { debounce, useQuasar } from 'quasar';
import { storeToRefs } from 'pinia';
import ProductSelect from 'src/modules/vendas/nfe/components/ProductSelect.vue';
import _ from 'lodash';
import GradeVenda from 'components/modal/dav/GradeVenda.vue';
import InformacoesProduto from 'components/modal/nfe/InformacoesProduto.vue';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import { useRoute } from 'vue-router';
import useEventsBus from 'stores/eventBus';
import { useCompanyData } from 'stores/api/company/index';
import EditProductIcon from 'src/components/icons/EditProductIcon.vue';
import { computed } from 'vue';
import validarCstCsosnCfop from 'src/pages/nf/composables/useValidarCstCsosnCfop.js';
import { useNfeConfigStore } from 'src/modules/vendas/nfe/store/useNfeConfigStore';

// Props do componente.
const props = defineProps({
  loading: { type: Boolean, default: false, required: true },
  readonly: {
    type: Boolean,
    default: false
  },
  isDevolucao: {
    type: Boolean,
    default: false
  },
  operationNature: {
    type: Object,
    required: false,
    default: () => {}
  },
  client: {
    type: Object,
    required: false,
    default: () => {}
  },
  cfopKey: {
    type: Number,
    required: false,
    default: 0
  },
  getPayload: {
    type: Function,
    required: true
  },
  disableDeleteItem: { type: Boolean, default: false }
});
// cfopKey
const { isDevolucao, operationNature, client } = toRefs(props);
const rows = defineModel({ type: Array, required: true });

// Emits do componente.
defineEmits([
  'deleteItem',
  'openDiscountModal',
  'updatedSelected',
  'openSerialGradeLote',
  'openGradeTable',
  'updatedTodo'
]);

const { emit: emitBus } = useEventsBus();
const $q = useQuasar();
const route = useRoute();
const { params } = route;

// const cfopSaleFilter = {
//   codCfop: {
//     filterType: 'VALUE_RANGE',
//     filterValue: { min: 5000, max: 5999 }
//   }
// };

// Cfop
const cfopStore = useCfopData();
// Cst
const cstStore = useCst();
// Csosn
const csosnStore = useCsosn();

// Trava o scroll no fim da tabela, se true.
// const lockVirtualScroll = ref(true);
const qtdeRef = ref();
const valorUnitarioRef = ref();

const nfeConfigStore = useNfeConfigStore();
const { data: nfeConfigData } = storeToRefs(nfeConfigStore);

// Dados da empresa
const company = useCompanyData();
const { data: companyData } = storeToRefs(company);

nextTick(async () => {
  await company.get();
  await nfeConfigStore.get({ deletedAt: false });

  bloquearVendaEstoqueNegativoOuZerado.value =
    nfeConfigData.value[0]?.bloquearVendaEstoqueNegativoOuZerado;
});

const handleQtdeBlur = (ev, data) => {
  if (
    (!ev.target.value || ev.target.value === '0,0000') &&
    !naturezaOperacaoDeAjusteOuComplementar(props.operationNature)
  ) {
    data.row.qtde = 1;
    calcValorUnitario(data.row);
    notify('Não é possível zerar a quantidade de um item');
  }
};

const handleValorUnitarioBlur = (ev, data) => {
  if (
    (!ev.target.value || ev.target.value === '0,0000') &&
    !naturezaOperacaoDeAjusteOuComplementar(props.operationNature)
  ) {
    data.row.valorUnitario =
      data.row.itemWithProduto?.produtoWithEstoque?.precoVenda ?? 1;
    notify('Não é possível zerar o valor unitário de um item');
  }
};

const calcValorDescontoItem = (produto) => {
  if (!produto.valorDesconto || !produto.valorBruto) return 0;

  if (produto.tipoValorDesconto === 0) {
    return round(produto.valorDesconto, 2);
  } else {
    return round((produto.valorBruto * produto.valorDesconto) / 100, 2);
  }
};

const calcValorIcmsDeson = (produto) => {
  const desconto = calcValorDescontoItem(produto);

  const valorIcmsDeson = round(
    (produto.valorBruto - desconto) * (produto.fiscal.aliqIcmsDeson / 100),
    2
  );

  return valorIcmsDeson;
};

const calcValorUnitario = (row) => {
  let valorUnitario = round(round(row.valorBruto, 4) / round(row.qtde, 4), 4);
  if (!valorUnitario) {
    valorUnitario = row.itemWithProduto?.produtoWithEstoque.precoVenda;
  }
  return valorUnitario;
};

const calcValorBruto = (row) => {
  let valorBruto = round(row.valorUnitario * row.qtde, 4);
  if (!valorBruto) {
    valorBruto = round(
      row.itemWithProduto?.produtoWithEstoque.precoVenda * row.qtde,
      4
    );
  }
  return round(valorBruto, 4);
};

const nfeTableRef = ref();
const global = useGlobal();

const disabled = computed(() => {
  return props.readonly || !client.value;
});

const { loading } = toRefs(props);

// Select de produto
const productSearchRef = ref();
const stock = useStock();
const stockFilters = {
  deleted_at: {
    filterType: 'NULL'
  }
};

function updateOrCreate(item, update = false) {
  if (update) {
    const existingIndex = rows.value.findIndex((i) => {
      return i.codProduto == item.codProduto;
    });

    if (existingIndex === -1) {
      rows.value.push(_.cloneDeep(item));
    } else {
      rows.value[existingIndex].grade = [...item.grade];
      rows.value[existingIndex].qtde = item.qtde;
    }
  } else {
    item.controle = null;
    rows.value.push(item);
  }
  updateRequiredInputs();
}

const updateRequiredInputs = () => {
  setTimeout(() => {
    emitBus('verifyRequiredInputs');
  }, 1500);
};

const handleImportedProducts = async (items) => {
  for (const item of items) {
    await handleAddItem(item);
  }
};

const clearText = () => productSearchRef?.value?.clearText();

// Abrir tela de cadastro de impostos ao abrir um item, descomentar quando nescessário

const openInformacoesProduto = async (product, index = null) => {
  const isEditing = index || index === 0;
  const modalPromise = new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: InformacoesProduto,
        hasCancel: false,
        hasSave: false,
        titleClass: 'tw-px-2',
        title: 'Dados do produto',
        width: '768px',
        cardClass: '!tw-max-w-full tw-max-w-[768px]',
        scope: `informacoes do ${product?.nome || product.selectData?.nome}}`,
        dataModal: {
          isEditing,
          naturezaOperacao: operationNature.value,
          client: props.client,
          produto: _.cloneDeep(product),
          isDevolucao: isDevolucao.value,
          readonly: props.readonly,
          getPayload: props.getPayload,
          isOSParcial: props.disableDeleteItem
        }
      }
    })
      .onOk((payload) => {
        if (!isEditing) {
          resolve(payload);
        } else {
          rows.value[index] = {
            ...rows.value[index],
            ...payload,
            qtde: parseInt(payload?.qtde),
            nItemPed: parseInt(payload?.nItemPed) || null
          };
        }
      })
      .onCancel(() => resolve(false));
  });
  return await modalPromise;
};

const naturezaOperacaoDeAjusteOuComplementar = (natureza) => {
  return (
    natureza?.finalidadeOperacao === '2' || natureza?.finalidadeOperacao === '3'
  );
};

const fetchOperationNatureCfop = (product) => {
  const field = isDevolucao.value ? 'vendaItemWithFiscal' : 'produtoWithFiscal';
  if (!operationNature.value)
    return isDevolucao.value
      ? product[field]?.vendaItemWithFiscalvendaItemFiscalWithCfop
      : product[field]?.produtoFiscalWithCfop;

  // CST e CSOSN com substituição
  const CST_CSOSN_COM_ST = ['201', '202', '203', '500', '10', '30', '70', '60'];
  const clientState = client.value?.pessoaWithEndereco?.uf;
  const companyState = companyData.value?.empresaWithEmpresaEndereco?.uf;

  if (
    operationNature.value?.finalidadeOperacao == 1 &&
    (clientState ? clientState == companyState : true)
  ) {
    return product[field]?.codCfop;
  }

  if (clientState && clientState != companyState) {
    return CST_CSOSN_COM_ST.includes(product[field]?.codCstCsosn)
      ? operationNature.value.naturezaOperacaoWithCfopForaSt
      : operationNature.value.naturezaOperacaoWithCfopFora;
  }

  return CST_CSOSN_COM_ST.includes(product[field]?.codCstCsosn)
    ? operationNature.value.naturezaOperacaoWithCfopDentroSt
    : operationNature.value.naturezaOperacaoWithCfopDentro;
};

/**
 * Verifica se há estoque para um determinado produto.
 * @param {string | number} qtde
 */
const verifyStock = (product) => {
  if (bloquearVendaEstoqueNegativoOuZerado.value) {
    let estoqueZeradoOuNegativo =
      product.produtoWithEstoque?.estoqueZeradoOuNegativo;

    if (estoqueZeradoOuNegativo) {
      notify('Venda bloqueada para estoque negativo ou zerado');
      clearText();
      return false;
    }
  }
  return true;
};

const handleAddItem = async (product) => {
  if (!verifyStock(product)) return;

  // Buscou por codBarras da grade.
  if (product.grade?.length === 1) {
    const grade = _.cloneDeep(product.grade[0]);
    grade.quantidade = product.produtoWithEstoque?.qtde;
    grade.codProdutoGrade = grade.controle;
    delete grade.controle;
    product.grade = [grade];
  }

  const comecarQuantidadeEValorUninatioComValorZero =
    naturezaOperacaoDeAjusteOuComplementar(props.operationNature);

  let addingItem = {};

  addingItem.index = rows.value?.length;
  addingItem.nome = product?.nome;

  addingItem.qtde = comecarQuantidadeEValorUninatioComValorZero
    ? 0
    : Number(product.qtde) || 1; // Manipulável pelo usuário.
  addingItem.qtdeEstoque = Number(product?.produtoWithEstoque?.qtde); // Em estoque no cadastro de produtos.
  addingItem.qtdeReal = Number(product?.produtoWithEstoque?.qtdeReal); // Considerando reservas em vendas abertas.
  addingItem.qtdeInicial = Number(product.qtde) || 1; // Inicial não manipulável.
  addingItem.valorUnitario = comecarQuantidadeEValorUninatioComValorZero
    ? 0
    : product?.produtoWithEstoque?.precoVenda;
  addingItem.adicionandoItem = true;
  addingItem.valorAcrescimo = 0;
  addingItem.tipoValorAcrescimo = 0;
  addingItem.valorDesconto = 0;
  addingItem.tipoValorDesconto = 0;
  addingItem.xPed = product?.xPed;
  addingItem.nItemPed = parseInt(product?.nItemPed) || null;
  addingItem.codProduto = product?.controle;
  addingItem.codVenda = params.controle;
  addingItem.selectData = product;
  addingItem.usaGrade = product?.produtoWithCaracteristica?.usaGrade || false;
  addingItem.usaSerial = false;
  addingItem.usaLote = false;
  addingItem.grade = product.grade ?? [];
  addingItem.produtoWithGrade = product?.produtoWithGrade;
  addingItem.valorBruto = calcValorBruto(addingItem);
  addingItem.fiscal = {
    codBeneficio: product?.produtoWithFiscal?.codBeneficio,
    valorIcmsDeson: null,
    aliqIcmsDeson: product?.produtoWithFiscal?.aliqIcmsDeson,
    motivoIcmsDeson: product?.produtoWithFiscal?.motivoIcmsDeson,
    infosAdicionais: product?.produtoWithFiscal?.infosAdicionais,
    percReducaoBcIcms: product?.produtoWithFiscal?.percReducBc
  };

  addingItem.fiscal.valorIcmsDeson = calcValorIcmsDeson(addingItem);

  const cfop = fetchOperationNatureCfop(product);
  addingItem.fiscal.codCstCsosn = product?.produtoWithFiscal?.codCstCsosn;

  addingItem.fiscal.codCfop =
    product?.produtoWithFiscal?.produtoFiscalWithCfop?.controle ??
    cfop?.controle;

  addingItem.fiscal.cfop =
    product?.produtoWithFiscal?.produtoFiscalWithCfop?.codCfop ?? cfop?.codCfop;

  // Produto foi encontrado por cod barras da grade
  let grades = addingItem.grade;
  const GRADE_POR_CODBARRAS = addingItem.grade.length;
  if (GRADE_POR_CODBARRAS) {
    // Busca grades quando a busca por codBarras da grade
    grades = fetchAddedGrades(addingItem, true);
    addingItem.qtde = await calcGradeQuantidade(addingItem.qtde, grades);
  } else {
    // Verifica se produto possui grade e não possui grades disponiveis
    if (!addingItem.produtoWithGrade?.length && addingItem.usaGrade) {
      notify('Não há grades em estoque para este item');
      return clearText();
    }

    // Verifica grades e abre modal para seleção de grade.
    const updatedProduct = await verifyGrades(addingItem);

    if (!updatedProduct) return clearText();
    addingItem = updatedProduct;
    grades = addingItem.grade;
  }

  // Mostra modal de confirmação do produto.
  addingItem = await showProductInfo(addingItem);
  if (!addingItem) return clearText();

  // Reatribui grades após modal de confirmação, impedindo a inserção de grades caso cancelar o modal.
  addingItem.grade = grades;

  updateOrCreate(addingItem, addingItem.grade.length > 0);
  clearText();

  // Scroll para baixo quando adiciona item
  nextTick(() => {
    const childrenHeightOfChildren =
      nfeTableRef.value?.$el?.children?.[0]?.children?.[0]?.getBoundingClientRect()
        ?.height;

    if (childrenHeightOfChildren) {
      nfeTableRef.value?.$el?.children?.[0]?.scrollTo(
        0,
        childrenHeightOfChildren
      );
    }
  });
};

async function showProductInfo(product) {
  const response = await openInformacoesProduto(product);
  if (!response) return false;

  product.cfopEditValue = response?.cfopEditValue;
  product.cstCsosnEditValue = response?.cstCsosnEditValue;
  product.nome = response?.nome;
  product.fiscal = response?.fiscal;
  product.qtde = parseFloat(response?.qtde);
  product.valorUnitario = response?.valorUnitario;
  product.tipoValorDesconto = response?.tipoValorDesconto;
  product.tipoValorAcrescimo = response?.tipoValorAcrescimo;
  product.valorDesconto = response?.valorDesconto;
  product.valorAcrescimo = response?.valorAcrescimo;
  product.xPed = response?.xPed;
  product.nItemPed = parseInt(response?.nItemPed) || null;

  return product;
}

const gradesRemovidas = ref([]);

/**
 * Verifica a existência de grade serial e lote para o produto adicionado
 * @param product
 * @returns {Promise<boolean>}
 */
const verifyGrades = async (product) => {
  let updatedProduct = product;

  // Verifica se já existe esse item com esta grade adicionado
  if (product && product.usaGrade) {
    product.grade = fetchAddedGrades(product);
    // Seleção de grades.
    updatedProduct = await openGradeModal(product);
  }

  product.qtde = calcGradeQuantidade(product.qtde, product.grade);
  return updatedProduct;
};

function fetchAddedGrades(product, adding = false) {
  let grades = product.grade ?? [];

  const equalItem = _.cloneDeep(
    rows.value.find(
      (item) => item.codProduto == (product.controle ?? product.codProduto)
    )
  );

  if (equalItem && equalItem?.grade?.length) {
    const mergedGrades = equalItem.grade?.reduce((acc, existingGrade) => {
      if (grades.length && adding) {
        const index = grades.findIndex(
          (g) =>
            g.tamanho == existingGrade.tamanho && g.cor == existingGrade.cor
        );

        if (index !== -1) {
          /*
            Se houver quantidade máxima (bloqueia estoque negativo true)
            verifica se a soma das quantidades ultrapassa o limite e ajusta,
            caso contrário, soma normalmente
          */
          const qtde = Number(existingGrade.quantidade);
          if (
            grades[index]?.quantidadeMaxima &&
            qtde + Number(grades[index]?.quantidade) >
              grades[index]?.quantidadeMaxima
          ) {
            // A soma da adição + existente é superior à máxima
            existingGrade.quantidade +=
              Number(grades[index]?.quantidadeMaxima) - Number(qtde);
          } else {
            existingGrade.quantidade += Number(grades[index]?.quantidade);
          }
        }
      }

      return acc.concat(existingGrade);
    }, []);

    grades = mergedGrades;
  }
  return grades;
}

// Retorna true caso a venda esteja bloqueada para estoque negativo ou zerado.
const bloquearVendaEstoqueNegativoOuZerado = ref(false);

const openGradeModal = async (product) => {
  const productRef = ref(product);
  // Modal de grade
  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: GradeVenda,
        hasSave: false,
        hasCancel: false,
        scope: 'grandeVenda',
        dataModal: {
          isPdv: true,
          bloquearEstoqueNegativo: bloquearVendaEstoqueNegativoOuZerado.value,
          vendaItem: productRef.value,
          addCallback: (res) => {
            productRef.value.grade = [...productRef.value.grade, res];
          },
          updateCallback: ({ payload, index }) => {
            productRef.value.grade.splice(index, 1, payload);
          },
          removeCallback: (index) => {
            if (productRef.value.grade[index].controle) {
              productRef.value.grade[index].deletar = true;
              gradesRemovidas.value.push(productRef.value.grade[index]);
            }
            productRef.value.grade.splice(index, 1);
          }
        }
      }
    })
      .onOk(() => {
        productRef.value.qtde = calcGradeQuantidade(
          productRef.value.qtde,
          productRef.value.grade
        );
        resolve(toRaw(productRef.value));
      })
      .onCancel(() => resolve(false));
  });
};

const calcGradeQuantidade = (productQty, grade) => {
  let qtde = productQty;

  if (grade?.length) {
    qtde = grade?.reduce((acc, grade) => {
      acc += Number(grade.quantidade);
      return acc;
    }, 0);
  }

  return qtde;
};

const abrirModalAlteracaoDadosFiscais = async () => {
  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        scope: 'client-change',
        title: 'Alteração de dados fiscais',
        classCardSection: 'lg:tw-w-[500px]',
        confirmLabel: 'SIM',
        cancelLabel: 'NÃO',
        description:
          'Essa alteração removerá a possibilidade de edição das informações tributárias, deseja continuar?'
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const removeItem = (item) => {
  const index = rows.value.findIndex((el) => item === el);

  if (!item?.controle) rows.value.splice(index, 1);
  else rows.value[index].deletar = true;

  updateRequiredInputs();
};
// --------------- Table ACTIONS ---------------
const actions = ref([
  {
    name: 'delete',
    icon: 'delete',
    tooltip: 'Excluir item',
    disableOnCondition: true,
    condition: (row) => {
      return !props.disableDeleteItem || row.controle == null;
    },
    action: (item) => {
      removeItem(item);
    }
  },
  {
    name: 'more',
    icon: 'more_vert',
    tooltip: '',
    isMenu: true,
    menuItems: [
      {
        name: 'editProduct',
        icon: 'editProduct',
        label: 'Detalhes produto',
        action: (row, index) => {
          rows.value[index].adicionandoItem = false;
          openInformacoesProduto(rows.value[index], index);
        }
      },
      {
        name: 'grade',
        icon: 'grade',
        label: 'Grades',
        condition: (row) => row?.usaGrade,
        action: (row) => verifyGrades(row)
      }
    ]
  }
]);

const menu = ref(Array(1000).fill(false));
const menuOver = ref(Array(1000).fill(false));
const listOver = ref(Array(1000).fill(false));

watch(
  () => menuOver.value,
  debounce(() => {
    menu.value = menuOver.value.map(
      (over, index) => over || listOver.value[index]
    );

    document?.activeElement?.blur();
  }, 40),
  { deep: true }
);

watch(
  () => listOver.value,
  debounce(() => {
    menu.value = menuOver.value.map(
      (over, index) => over || listOver.value[index]
    );
  }, 40),
  { deep: true }
);

const iconComponents = shallowRef({
  discount: DiscountIcon,
  delete: TrashIcon,
  serial: SerialIcon,
  grade: GradeIcon,
  lote: LoteIcon,
  editProduct: EditProductIcon
});

const getFilteredActions = (row) => {
  return actions.value
    ?.filter((action) =>
      action.disableOnCondition
        ? true
        : !action.condition || action.condition(row)
    )
    .map((action) => {
      const newAction = { ...action };

      if (newAction.isMenu) {
        newAction.menuItems = action.menuItems.filter(
          (menuItem) => !menuItem.condition || menuItem.condition(row)
        );
      }

      return newAction;
    });
};

function calcValorTotal(item) {
  const bruto = Number(item.qtde) * Number(item.valorUnitario);

  let desconto = 0;
  let acrescimo = 0;

  if (item.tipoValorDesconto === 0) {
    desconto = round(item.valorDesconto ?? 0, 2);
  } else {
    desconto = round((bruto * item.valorDesconto) / 100, 2);
  }

  if (item.tipoValorAcrescimo === 0) {
    acrescimo = round(item.valorAcrescimo, 2);
  } else {
    acrescimo = round((bruto * item.valorAcrescimo) / 100, 2);
  }

  return round(bruto - desconto + acrescimo, 2);
}

// Objeto que estrutura as colunas da tabela.
const columns = ref([
  {
    name: 'codProduto',
    required: true,
    label: 'Cód.',
    align: 'center',
    field: 'codProduto',
    sortable: false
  },
  {
    name: 'nome',
    align: 'center',
    label: 'Produto/Serviço',
    field: (row) => row?.nome || row.selectData?.nome,
    tooltip: (row) => row?.nome || row.selectData?.nome,
    sortable: false
  },
  {
    name: 'codCfop',
    label: 'CFOP',
    align: 'center'
  },
  {
    name: 'codCstCsosn',
    label:
      global.company?.empresaWithEmpresaFiscal?.crt === '1' ||
      global.company?.empresaWithEmpresaFiscal?.crt === '5'
        ? 'CSOSN'
        : 'CST',
    align: 'center',
    field: (row) => row.codCstCsosn ?? ''
  },
  {
    name: 'qtde',
    label: 'Qtde',
    align: 'center'
  },
  {
    name: 'valorUnitario',
    label: 'Valor unitário',
    align: 'center',
    sortable: false,
    field: (row) => row.valorUnitario
  },

  {
    name: 'valorTotal',
    label: '(R$) Total',
    align: 'center',
    sortable: false,
    field: (row) => currencyFormat(calcValorTotal(row))
  },

  {
    name: 'actions',
    field: 'actions',
    required: true,
    label: 'Ações',
    align: 'center'
  }
]);

// Funções expostas do componente.
defineExpose({ nfeTableRef, handleAddItem });
</script>
