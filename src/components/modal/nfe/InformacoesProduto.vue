<template>
  <div
    :class="` ${
      mostrarCardInfoTributaria ? 'tw-h-[67vh] ' : ''
    } tw-items-center tw-overflow-hidden`"
  >
    <div
      :class="`customScroll tw-flex tw-px-2 ${
        mostrarCardInfoTributaria ? 'tw-h-[67vh]' : ''
      } tw-flex-col tw-overflow-y-auto tw-pb-2`"
    >
      <h3 class="-tw-mt-1 tw-ml-0.5">
        Descrição:
        <span>
          {{ props.dataModal.produto.codProduto }} -
          {{
            props.dataModal.produto?.nome ||
            props.dataModal.produto?.selectData?.nome
          }}</span
        >
      </h3>
      <div
        class="tw-flex tw-flex-row tw-flex-wrap-reverse tw-items-center tw-justify-center tw-text-textsSGBR-gray"
      >
        <q-card-secction v-if="loading" class="!tw-p-0">
          <q-skeleton
            style="margin: 1rem 0 2rem"
            animation="blink"
            height="4rem"
          />
          <div class="tw-my-4 tw-flex tw-justify-end tw-gap-3">
            <q-skeleton animation="blink" width="20rem" height="5rem" />
            <q-skeleton animation="blink" width="20rem" height="5rem" />
          </div>
          <div class="tw-my-4 tw-flex tw-justify-end tw-gap-3">
            <q-skeleton animation="blink" width="20rem" height="5rem" />
            <q-skeleton animation="blink" width="20rem" height="5rem" />
          </div>
        </q-card-secction>

        <q-card-section v-else class="tw-w-full !tw-p-0">
          <SGCard
            title="Valores"
            id="dados-emitente"
            remove-gap
            :cols="12"
            :has-circle="false"
            container-class="!tw-mt-2 !tw-py-0 tw-gap-y-1"
            class="!tw-mb-0 !tw-px-0 !tw-pb-0 !tw-shadow-none"
          >
            <Money
              hide-bottom-space
              v-model="forms.qtde"
              @update:model-value="
                () => {
                  resetInfoTributaria();

                  fetchImpostos();

                  forms.fiscal.valorIcmsDeson = calcValorIcmsDeson(forms);
                }
              "
              outlined
              dense
              label="Qtde."
              :max="9999999.9999"
              stack-label
              :decimals-quantity="4"
              :required="!naturezaOperacaoDeAjusteOuComplementar"
              class="tw-col-span-4"
              :readonly="
                dataModal.isDevolucao ||
                dataModal.readonly ||
                forms.usaGrade ||
                (dataModal.isOSParcial && !!dataModal.produto.controle)
              "
              :rules="[
                (val) => {
                  if (naturezaOperacaoDeAjusteOuComplementar) {
                    return true;
                  } else {
                    return val && val != 0 ? true : 'O campo é obrigatório';
                  }
                }
              ]"
            />

            <Money
              hide-bottom-space
              type="text"
              v-model="forms.valorUnitario"
              @update:model-value="
                () => {
                  forms.fiscal.valorIcmsDeson = calcValorIcmsDeson(forms);
                }
              "
              :decimals-quantity="global.qtdCasasDecimais"
              outlined
              :required="!naturezaOperacaoDeAjusteOuComplementar"
              dense
              label="Valor unitário"
              class="tw-col-span-4 tw-bg-white"
              :readonly="
                dataModal.isDevolucao ||
                dataModal.readonly ||
                (dataModal.isOSParcial && !!dataModal.produto.controle)
              "
              :rules="[
                (val) => {
                  if (naturezaOperacaoDeAjusteOuComplementar) {
                    return true;
                  } else {
                    return val && toFloat(val) > 0
                      ? true
                      : 'O campo é obrigatório';
                  }
                }
              ]"
            />

            <Money
              hide-bottom-space
              :model-value="
                round(
                  forms.qtde * forms.valorUnitario -
                    (forms.tipoValorDesconto == '0'
                      ? forms.valorDesconto
                      : (forms.valorDesconto / 100) *
                        (forms.valorUnitario * forms.qtde)) +
                    (forms.tipoValorAcrescimo == '0'
                      ? forms.valorAcrescimo
                      : (forms.valorAcrescimo / 100) *
                        (forms.valorUnitario * forms.qtde)),
                  4
                )
              "
              outlined
              :decimals-quantity="4"
              readonly
              dense
              label="Total"
              class="tw-col-span-4 tw-bg-white"
            />
            <InputPriceOrPercent
              label="Acréscimo"
              hide-bottom-space
              :model-value="forms.valorAcrescimo"
              :toggle-type="forms.tipoValorAcrescimo"
              outlined
              stack-label
              dense
              :readonly="dataModal.isDevolucao || dataModal.readonly"
              class="tw-col-span-6"
              @update:model-value="
                (value) => {
                  if (value === forms.valorAcrescimo) return;

                  if (mostrarCardInfoTributaria) {
                    debounceTimeout(() => {
                      abrirDesejaRecalcularModal();
                    });
                  }

                  forms.valorAcrescimo = value;
                }
              "
              @toggle-click="
                (toggleType) => {
                  if (mostrarCardInfoTributaria && forms.valorAcrescimo > 0) {
                    debounceTimeout(() => {
                      abrirDesejaRecalcularModal();
                    });
                  }

                  forms.valorAcrescimo = 0.0;
                  forms.tipoValorAcrescimo = toggleType.value;
                }
              "
            />

            <InputPriceOrPercent
              label="Desconto"
              hide-bottom-space
              :key="forms.valorUnitario"
              :model-value="forms.valorDesconto"
              :toggle-type="forms.tipoValorDesconto"
              outlined
              stack-label
              dense
              :readonly="
                dataModal.isDevolucao ||
                dataModal.readonly ||
                (dataModal.isOSParcial && !!dataModal.produto.controle)
              "
              class="tw-col-span-6"
              @update:model-value="
                (value) => {
                  if (value === forms.valorDesconto) return;

                  if (mostrarCardInfoTributaria) {
                    debounceTimeout(() => {
                      abrirDesejaRecalcularModal();
                    });
                  }

                  forms.valorDesconto = value;
                  forms.fiscal.valorIcmsDeson = calcValorIcmsDeson(forms);
                }
              "
              :percentage-rules="descontoPercentageRules"
              :money-rules="[(val) => descontoMoneyRules(val, forms)]"
              @toggle-click="
                (toggleType) => {
                  if (mostrarCardInfoTributaria && forms.valorDesconto > 0) {
                    debounceTimeout(() => {
                      abrirDesejaRecalcularModal();
                    });
                  }

                  forms.valorDesconto = 0.0;
                  forms.tipoValorDesconto = toggleType.value;
                  forms.fiscal.valorIcmsDeson = calcValorIcmsDeson(forms);
                }
              "
            />
          </SGCard>
          <SGCard
            title="Dados fiscais"
            id="dados-emitente"
            remove-gap
            :cols="12"
            :has-circle="false"
            container-class=" !tw-mt-2 !tw-mx-0 !tw-py-0 tw-gap-y-1"
            class="!tw-mx-0 !tw-px-0 !tw-pb-0 !tw-shadow-none"
          >
            <div
              class="tw-col-span-full tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2"
            >
              <div
                class="tw-col-span-6 !tw-min-w-60 tw-flex-1"
                v-if="
                  companyData.empresaWithEmpresaFiscal?.crt == 1 ||
                  companyData.empresaWithEmpresaFiscal?.crt == 5
                "
              >
                <SelectInput
                  hide-bottom-space
                  label-select="Cód. CSOSN"
                  class="tw-col-span-4"
                  :deleted-at="false"
                  :clearable="false"
                  :store="csosnStore"
                  store-key="descricaoCodigo"
                  required
                  :filter-or="['descricao', 'codigo']"
                  :order-by="{ codigo: 'asc' }"
                  @value="
                    async (data) => {
                      const mostrarCardInfoTributariaAnterior =
                        mostrarCardInfoTributaria;

                      const validacaoCstCsosnCfop = validarCstCsosnCfop({
                        cfop: forms.fiscal?.cfop,
                        cstCsosn: data?.codigo,
                        finalidadeNaturezaOperacao:
                          props.dataModal.naturezaOperacao.finalidadeOperacao
                      });

                      const mostrarCard = await handleMostrarCardInfoTributaria(
                        validacaoCstCsosnCfop
                      );

                      if (!mostrarCard) {
                        csosnEditValue = {
                          codigo: csosnEditValue.codigo,
                          descricaoCodigo: csosnEditValue.descricaoCodigo
                        };
                        return;
                      }

                      if (
                        mostrarCardInfoTributaria &&
                        !mostrarCardInfoTributariaAnterior
                      ) {
                        csosnEditValue = data;
                        forms.fiscal.codCstCsosn = data?.codigo;

                        await fetchImpostos();
                      } else {
                        csosnEditValue = data;
                        forms.fiscal.codCstCsosn = data?.codigo;
                      }
                    }
                  "
                  :value="csosnEditValue"
                />
              </div>
              <div v-else class="tw-col-span-6 !tw-min-w-60 tw-flex-1">
                <SelectInput
                  label-select="Cód. CST"
                  :deleted-at="false"
                  hide-bottom-space
                  :store="cstStore"
                  :clearable="false"
                  required
                  store-key="descricaoCodigo"
                  :filter-or="['descricao', 'codigo']"
                  :order-by="{ codigo: 'asc' }"
                  :disabled="dataModal.readonly"
                  @value="
                    async (data) => {
                      const mostrarCardInfoTributariaAnterior =
                        mostrarCardInfoTributaria;

                      const validacaoCstCsosnCfop = validarCstCsosnCfop({
                        cfop: forms.fiscal?.cfop,
                        cstCsosn: data?.codigo,
                        finalidadeNaturezaOperacao:
                          props.dataModal.naturezaOperacao.finalidadeOperacao
                      });

                      const mostrarCard = await handleMostrarCardInfoTributaria(
                        validacaoCstCsosnCfop
                      );

                      if (!mostrarCard) {
                        csosnEditValue = {
                          codigo: csosnEditValue.codigo,
                          descricaoCodigo: csosnEditValue.descricaoCodigo
                        };
                        return;
                      }

                      if (
                        mostrarCardInfoTributaria &&
                        !mostrarCardInfoTributariaAnterior
                      ) {
                        cstEditValue = data;
                        forms.fiscal.codCstCsosn = data?.codigo;
                        forms.fiscal.motivoIcmsDeson = null;
                        forms.fiscal.codBeneficio = null;
                        forms.fiscal.codBeneficioRbc = null;

                        await fetchImpostos();
                      } else {
                        cstEditValue = data;
                        forms.fiscal.codCstCsosn = data?.codigo;
                        forms.fiscal.motivoIcmsDeson = null;
                        forms.fiscal.codBeneficio = null;
                        forms.fiscal.codBeneficioRbc = null;
                      }
                    }
                  "
                  :value="cstEditValue"
                />
              </div>
              <!-- class="tw-mb-[1.1rem] md:tw-max-w-[13.4rem]" -->

              <InputSelect
                v-if="estadoComBeneficioFiscal"
                hide-bottom-space
                v-model="forms.fiscal.codBeneficio"
                v-bind="{
                  ...ModelInputsSelectSearch,
                  params: {
                    apiRoute: '/api/beneficio-fiscal',
                    central: true,
                    filterOr: ['descricao', 'codBeneficio'],
                    filters: {
                      uf: {
                        filterType: 'EQUALS',
                        filterValue: companyData?.empresaWithEmpresaEndereco?.uf
                      },
                      'tributacoes.codcstcsosn': {
                        filterType: 'EQUALS',
                        filterValue: forms.fiscal?.codCstCsosn
                      }
                    }
                  }
                }"
                option-label="codBeneficio"
                option-value="codBeneficio"
                label="Cód. benefício fiscal"
                popup-content-class="lg:!tw-w-[160px]"
                class="!tw-min-w-60 tw-flex-1"
                :preload="true"
                :readonly="dataModal.readonly"
              />
              <div
                v-if="mostrarBeneficioFiscalRBC"
                class="custom-required !tw-min-w-60 tw-flex-1"
              >
                <InputSelect
                  hide-bottom-space
                  v-model="forms.fiscal.codBeneficioRbc"
                  v-bind="{
                    ...ModelInputsSelectSearch,
                    params: {
                      apiRoute: '/api/beneficio-fiscal',
                      central: true,
                      filterOr: ['descricao', 'codBeneficio'],
                      filters: {
                        uf: {
                          filterType: 'EQUALS',
                          filterValue:
                            companyData?.empresaWithEmpresaEndereco?.uf
                        },
                        'tributacoes.codcstcsosn': {
                          filterType: 'EQUALS',
                          filterValue: '20'
                        }
                      }
                    }
                  }"
                  option-label="codBeneficio"
                  option-value="codBeneficio"
                  label="Cód. benefício fiscal RBC"
                  required
                  popup-content-class="lg:!tw-w-[160px]"
                  :preload="true"
                  :readonly="dataModal.readonly"
                />
              </div>

              <Money
                v-if="LISTA_CST_DESONERACAO.includes(forms.fiscal?.codCstCsosn)"
                dense
                outlined
                hide-bottom-space
                v-model="forms.fiscal.valorIcmsDeson"
                @update:model-value="
                  (value) => {
                    if (!value || value <= 0) {
                      forms.fiscal.motivoIcmsDeson = null;
                    }
                  }
                "
                label="Valor desoneração ICMS"
                stack-label
                class="!tw-min-w-60 tw-flex-1"
              />

              <div
                v-if="LISTA_CST_DESONERACAO.includes(forms.fiscal?.codCstCsosn)"
                :class="`${
                  forms.fiscal.valorIcmsDeson > 0 ? 'custom-required' : ''
                } !tw-min-w-60 tw-flex-1`"
              >
                <InputSelect
                  :key="DESONERACAO_ICMS_OPTIONS"
                  v-model="forms.fiscal.motivoIcmsDeson"
                  v-bind="{
                    ...ModelInputsSelectSearch,
                    params: {
                      optionsStatic: DESONERACAO_ICMS_OPTIONS
                    }
                  }"
                  label="Motivo da desoneração do ICMS"
                  :disable="
                    !forms.fiscal.valorIcmsDeson ||
                    forms.fiscal.valorIcmsDeson <= 0
                  "
                  :required="forms.fiscal.valorIcmsDeson > 0"
                  :clearable="false"
                />
              </div>

              <div class="!tw-min-w-60 tw-flex-1">
                <SelectInput
                  hide-bottom-space
                  :key="cfopKey"
                  :store="cfopStore"
                  :value="cfopEditValue"
                  :filters="cfopFilters"
                  store-key="descricaoCodigo"
                  :filter-or="['descricao', 'codcfop']"
                  :order-by="{ codCfop: 'asc' }"
                  label-select="CFOP"
                  required
                  :loading="false"
                  :clearable="false"
                  @value="
                    async (data) => {
                      const mostrarCardInfoTributariaAnterior =
                        mostrarCardInfoTributaria;

                      const validacaoCstCsosnCfop = validarCstCsosnCfop({
                        cfop: data?.codCfop,
                        cstCsosn: forms.fiscal.codCstCsosn,
                        finalidadeNaturezaOperacao:
                          props.dataModal.naturezaOperacao.finalidadeOperacao
                      });

                      const mostrarCard = await handleMostrarCardInfoTributaria(
                        validacaoCstCsosnCfop
                      );

                      if (!mostrarCard) {
                        cfopEditValue = {
                          controle: cfopEditValue.controle,
                          codCfop: cfopEditValue.codCfop,
                          descricaoCodigo: cfopEditValue.descricaoCodigo
                        };
                        return;
                      }

                      if (
                        mostrarCardInfoTributaria &&
                        !mostrarCardInfoTributariaAnterior
                      ) {
                        cfopEditValue = data;
                        forms.fiscal.codCfop = data?.controle;
                        forms.fiscal.cfop = data?.codCfop || null;

                        await fetchImpostos();
                      } else {
                        cfopEditValue = data;
                        forms.fiscal.codCfop = data?.controle;
                        forms.fiscal.cfop = data?.codCfop || null;
                      }
                    }
                  "
                >
                </SelectInput>
              </div>

              <q-input
                dense
                outlined
                hide-bottom-space
                v-model="forms.xPed"
                label="Nº pedido"
                stack-label
                class="!tw-min-w-60 tw-flex-1"
                maxlength="15"
                type="text"
                :readonly="dataModal.readonly"
              />
              <q-input
                dense
                outlined
                hide-bottom-space
                v-model="forms.nItemPed"
                label="Nº item do pedido"
                stack-label
                class="!tw-min-w-60 tw-flex-1"
                mask="######"
                input-class="text-right"
                :readonly="dataModal.readonly"
              />

              <div class="custom-required tw-col-span-6 !tw-min-w-60 tw-flex-1">
                <InputSelect
                  v-model="forms.fiscal.codNcmNew"
                  @update:model-value="onChangeNcm"
                  :clearable="false"
                  v-bind="{
                    ...ModelInputsSelectSearch,
                    params: {
                      apiRoute: '/api/ncm/new',
                      central: true,
                      filterOr: ['descricao', 'ncm'],
                      filtersV2: []
                    }
                  }"
                  :option-label="(row) => row?.descricaoCodigo"
                  option-value="controle"
                  label="NCM"
                  popup-content-class="lg:!tw-w-[160px]"
                  :required="true"
                  :readonly="dataModal.readonly"
                />
              </div>

              <div class="custom-required tw-col-span-6 !tw-min-w-60 tw-flex-1">
                <InputSelect
                  :key="forms.fiscal.codNcmNew"
                  v-model="forms.fiscal.codCestNew"
                  :clearable="true"
                  v-bind="{
                    ...ModelInputsSelectSearch,
                    params: {
                      apiRoute: '/api/ncm/cest',
                      central: true,
                      filterOr: ['cest.descricao', 'cest.cest'],
                      filtersV2: cestFiltersV2
                    }
                  }"
                  :option-label="(row) => row?.cest?.descricaoCodigo"
                  option-value="codCest"
                  label="CEST"
                  popup-content-class="lg:!tw-w-[160px]"
                  :required="false"
                  :readonly="dataModal.readonly"
                />
              </div>
            </div>
            <Money
              v-if="!mostrarCardInfoTributaria && mostrarBaseCalcICMS"
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              stack-label
              v-model="forms.fiscal.bcIcms"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcms = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C ICMS"
              class="tw-col-span-6"
              :readonly="props.readonly"
            />
            <q-input
              v-model="forms.fiscal.infosAdicionais"
              type="textarea"
              outlined
              autogrow
              class="tw-col-span-full"
              maxlength="255"
              hide-bottom-space
              label="Informações adicionais"
              dense
              stack-label
              input-class="tw-text-SGBRGray tw-p-0"
              input-style="min-height: 5rem"
            />
          </SGCard>

          <SGCard
            v-if="mostrarCardInfoTributaria"
            title="Informações tributárias"
            id="compras-impostos-impostos"
            :cols="12"
            remove-gap
            :has-circle="false"
            container-class="!tw-mt-2 !tw-py-0 tw-gap-y-1"
            class="!tw-px-0 !tw-pb-0 !tw-shadow-none"
          >
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.aliqIcms"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcms = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% ICMS"
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.percReducaoBcIcms"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcms = null;
                    forms.fiscal.bcIcms = null;

                    await debounce(fetchImpostos);

                    const valorRedicaoBcIcms = forms.fiscal.percReducaoBcIcms;

                    if (!valorRedicaoBcIcms) {
                      forms.fiscal.codBeneficioRbc = null;
                    }
                  }
                }
              "
              label="% Red. ICMS"
              stack-label
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              stack-label
              v-model="forms.fiscal.bcIcms"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcms = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C ICMS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.valorIcms"
              label="Valor ICMS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.aliqIcmsSt"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcmsSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% ICMS ST"
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.percReducaoBcIcmsSt"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcmsSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% Red. ST"
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.percMva"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcmsSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="MVA ST"
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.bcIcmsSt"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIcmsSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C ST"
              class="tw-col-span-2"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.valorIcmsSt"
              label="Valor ICMS ST"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.aliqIpi"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIpi = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% IPI"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.bcIpi"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorIpi = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C IPI"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.valorIpi"
              label="Valor IPI"
              stack-label
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              :key="forms.fiscal.codPis"
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="forms.fiscal?.codPis === '03' ? 4 : 2"
              :max="999999.99"
              v-model="forms.fiscal.aliqPis"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorPis = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% PIS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.bcPis"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorPis = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C PIS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.valorPis"
              label="Valor PIS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.aliqCofins"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorCofins = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% COFINS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.bcCofins"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorCofins = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C COFINS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :decimals-quantity="2"
              :max="999999.99"
              v-model="forms.fiscal.valorCofins"
              label="Valor COFINS"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />

            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.aliqFcp"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorFcp = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% FCP"
              stack-label
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.bcFcp"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorFcp = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C FCP"
              stack-label
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.valorFcp"
              label="Valor FCP"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.aliqFcpSt"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorFcpSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="% FCP ST"
              stack-label
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.bcFcpSt"
              @update:model-value="
                async () => {
                  if (mounted) {
                    forms.fiscal.valorFcpSt = null;

                    await debounce(fetchImpostos);
                  }
                }
              "
              label="B.C FCP ST"
              stack-label
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
            <Money
              dense
              outlined
              hide-bottom-space
              stack-label
              :max="999999.99"
              :decimals-quantity="2"
              v-model="forms.fiscal.valorFcpSt"
              label="Valor FCP ST"
              class="tw-col-span-4"
              :readonly="props.readonly || !informacoesTributariasEditaveis"
            />
          </SGCard>
        </q-card-section>
      </div>
    </div>
  </div>

  <div
    class="tw-mt-3 tw-flex tw-flex-wrap tw-items-center tw-justify-end tw-gap-x-6 tw-gap-y-3 tw-px-2"
  >
    <q-btn
      flat
      unelevated
      dense
      class="tw-flex tw-w-fit !tw-min-w-[120px] tw-grow tw-flex-row tw-gap-2 tw-rounded-md tw-bg-SGBRGrayLighten tw-px-2 tw-font-medium tw-text-textsSGBR-gray"
      padding="5px"
      @click="onCancel"
      v-close-popup
    >
      <div class="tw-flex tw-w-full tw-items-center tw-justify-center">
        <Shortkey v-if="showShortKey" text="Esc" />

        <p class="tw-inline-block tw-font-normal tw-text-textsSGBR-gray">
          Cancelar
        </p>
      </div>
    </q-btn>
    <q-btn
      flat
      unelevated
      dense
      class="tw-flex tw-w-fit !tw-min-w-[120px] tw-grow tw-flex-row tw-gap-2 tw-rounded-md tw-bg-SGBRBlueLighten tw-px-2 tw-font-medium tw-text-white"
      padding="5px"
      @click="onConfirm"
      :disabled="!isSaveEnabled"
    >
      <div class="tw-flex tw-w-full tw-items-center tw-justify-center">
        <Shortkey v-if="showShortKey" text="Enter" />

        <p class="tw-inline-block tw-font-normal tw-text-inherit">Confirmar</p>
      </div>
    </q-btn>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, toRaw, nextTick } from 'vue';
import { useGlobal } from 'src/stores/global';
import SGCard from 'components/generic/SGCard.vue';
import { useCfopData } from 'src/stores/api/cfop';
import { storeToRefs } from 'pinia';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { useQuasar } from 'quasar';
import Shortkey from 'components/generic/shortkey/Shortkey.vue';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import Money from 'src/components/generic/input/Money.vue';
import InputPriceOrPercent from 'components/generic/input/PriceOrPercent.vue';
import round from 'src/components/utils/round';
import { useCompanyData } from 'stores/api/company/index';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputsSelectSearch from 'src/core/models/inputs/SelectSearch';
import { useCalcularImpostoPayload } from 'stores/api/sales/calcularImpostoPayload';
import SelectInput from 'components/generic/input/Select/index.vue';
import { useCsosn } from 'stores/api/csosn';
import { useCst } from 'stores/api/cst';
import validarCstCsosnCfop from 'src/pages/nf/composables/useValidarCstCsosnCfop.js';
import {
  CST_10_OPTIONS,
  CST_20_OPTIONS,
  CST_30_OPTIONS,
  CST_40_41_50_OPTIONS,
  CST_70_OPTIONS,
  CST_90_OPTIONS,
  LISTA_CST_DESONERACAO
} from '/src/modules/produtos/constants/constants.js';
import { useDebounceFn } from '@vueuse/core';

const debounce = useDebounceFn(async (callback) => {
  await callback();
}, 400);

const props = defineProps({
  scope: {
    type: String,
    required: true
  },
  dataModal: {
    type: Object,
    required: true
  }
});

// Eventos
const emit = defineEmits(['ok', 'on-save', 'on-cancel', 'accepted']);

const $q = useQuasar();

const loading = ref(true);

// Stores e instancias
const global = useGlobal();
const calcularImpostoPayload = useCalcularImpostoPayload();

const { showShortKey } = storeToRefs(global);

const DESONERACAO_ICMS_OPTIONS = computed(() => {
  switch (forms.value.fiscal.codCstCsosn) {
    case '10':
      return CST_10_OPTIONS;
    case '20':
      return CST_20_OPTIONS;
    case '30':
      return CST_30_OPTIONS;
    case '40':
    case '41':
    case '50':
      return CST_40_41_50_OPTIONS;
    case '70':
      return CST_70_OPTIONS;
    case '90':
      return CST_90_OPTIONS;
    default:
      return [];
  }
});

const LISTA_CST_CSOSN_NATUREZA_AJUSTE_OU_COMPLEMENTAR = [
  '00',
  '10',
  '20',
  '30',
  '40',
  '41',
  '50',
  '51',
  '60',
  '70',
  '90',
  '02',
  '15',
  '53',
  '61',
  '900'
];

const mostrarBaseCalcICMS = computed(() => {
  return (
    LISTA_CST_CSOSN_NATUREZA_AJUSTE_OU_COMPLEMENTAR.includes(
      forms.value.fiscal.codCstCsosn
    ) && naturezaOperacaoDeAjusteOuComplementar
  );
});

let timeoutId = null;

function debounceTimeout(callback) {
  if (timeoutId) {
    clearTimeout(timeoutId);
  }

  timeoutId = setTimeout(async () => {
    await callback();
    timeoutId = null;
  }, 700);
}

const cfopFilters = {
  tipoOperacao: {
    filterType: 'EQUALS',
    filterValue: props.dataModal.naturezaOperacao.tipoOperacao
  }
};

// Cfop
const cfopStore = useCfopData();
const cfopEditValue = ref({});

// Cst
const cstStore = useCst();
const cstEditValue = ref({});

// Csosn
const csosnStore = useCsosn();
const csosnEditValue = ref({});

// Dados da empresa
const company = useCompanyData();
const { data: companyData } = storeToRefs(company);

const naturezaOperacaoDeAjusteOuComplementar =
  props.dataModal.naturezaOperacao?.finalidadeOperacao == 3 ||
  props.dataModal.naturezaOperacao?.finalidadeOperacao == 2;

const estadosBeneficioFiscal = ['DF', 'ES', 'GO', 'PR', 'RJ', 'RS', 'SC'];

const estadoComBeneficioFiscal = computed(() =>
  estadosBeneficioFiscal.includes(
    companyData.value?.empresaWithEmpresaEndereco?.uf
  )
);

const mostrarBeneficioFiscalRBC = computed(() => {
  const empresaDeSantaCatarina =
    companyData.value?.empresaWithEmpresaEndereco?.uf === 'SC';

  const ICMS_51 = forms.value.fiscal.codCstCsosn === '51';
  const possuiPercReducaoBcIcms = forms.value.fiscal?.percReducaoBcIcms > 0;

  return empresaDeSantaCatarina && ICMS_51 && possuiPercReducaoBcIcms;
});

const cfopKey = ref(false);

const mounted = ref(false);

const mostrarCardInfoTributaria = ref(false);
// const mostrarCardIcmsRelativo = ref(false);

const fetchImpostos = async () => {
  mounted.value = false;

  const payload = props.dataModal.getPayload();

  payload.item = [forms.value];

  const { data } = await calcularImpostoPayload.post({
    payload: {
      payload,
      modelo: '55'
    },
    successMsg: 'Os valores das informações tributárias foram atualizados'
  });

  forms.value.fiscal.aliqIcms = data?.produtos?.[0]?.icms?.pICMS || 0;
  forms.value.fiscal.percReducaoBcIcms = data?.produtos?.[0]?.icms?.pRedBC || 0;
  forms.value.fiscal.bcIcms = data?.produtos?.[0]?.icms?.vBC || 0;
  forms.value.fiscal.valorIcms = data?.produtos?.[0]?.icms?.vICMS || 0;
  forms.value.fiscal.aliqPis = data?.produtos?.[0]?.pis?.pPIS || 0;
  forms.value.fiscal.bcPis = data?.produtos?.[0]?.pis?.vBC || 0;
  forms.value.fiscal.valorPis = data?.produtos?.[0]?.pis?.vPIS || 0;
  forms.value.fiscal.aliqIcmsSt = data?.produtos?.[0]?.icms?.pICMSST || 0;
  forms.value.fiscal.bcIcmsSt = data?.produtos?.[0]?.icms?.vBCST || 0;
  forms.value.fiscal.valorIcmsSt = data?.produtos?.[0]?.icms?.vICMSST || 0;
  forms.value.fiscal.percReducaoBcIcmsSt =
    data?.produtos?.[0]?.icms?.pRedBCST || 0;
  forms.value.fiscal.percMva = data?.produtos?.[0]?.icms?.pMVAST || 0;
  forms.value.fiscal.aliqCofins = data?.produtos?.[0]?.cofins?.pCOFINS || 0;
  forms.value.fiscal.bcCofins = data?.produtos?.[0]?.cofins?.vBC || 0;
  forms.value.fiscal.valorCofins = data?.produtos?.[0]?.cofins?.vCOFINS || 0;
  forms.value.fiscal.aliqIpi = data?.produtos?.[0]?.ipi?.pIPI || 0;
  forms.value.fiscal.bcIpi = data?.produtos?.[0]?.ipi?.vBC || 0;
  forms.value.fiscal.valorIpi = data?.produtos?.[0]?.ipi?.vIPI || 0;
  forms.value.fiscal.aliqFcp = 0;
  forms.value.fiscal.bcFcp = 0;
  forms.value.fiscal.valorFcp = 0;
  forms.value.fiscal.aliqFcpSt = data?.produtos?.[0]?.icms?.pFCPST || 0;
  forms.value.fiscal.bcFcpSt = data?.produtos?.[0]?.icms?.vBCFCPST || 0;
  forms.value.fiscal.valorFcpSt = data?.produtos?.[0]?.icms?.vFCPST || 0;

  setTimeout(() => {
    mounted.value = true;
  }, 300);
};

const fetchOperationNatureCfop = (product) => {
  const field = props.dataModal.isDevolucao
    ? 'vendaItemWithFiscal'
    : 'produtoWithFiscal';
  if (!props.dataModal.naturezaOperacao)
    return props.dataModal.isDevolucao
      ? product?.selectData[field]?.vendaItemWithFiscalvendaItemFiscalWithCfop
      : product?.selectData[field]?.produtoFiscalWithCfop;

  // CST e CSOSN com substituição
  const CST_CSOSN_COM_ST = ['201', '202', '203', '500', '10', '30', '70', '60'];
  const clientState = props.dataModal.client?.pessoaWithEndereco?.uf;
  const companyState = companyData.value?.empresaWithEmpresaEndereco?.uf;

  if (
    props.dataModal.naturezaOperacao?.finalidadeOperacao == 1 &&
    (clientState ? clientState == companyState : true)
  ) {
    return product?.selectData[field]?.produtoFiscalWithCfop;
  }

  if (clientState && clientState != companyState) {
    return CST_CSOSN_COM_ST.includes(product?.selectData[field]?.codCstCsosn)
      ? props.dataModal.naturezaOperacao?.naturezaOperacaoWithCfopForaSt
      : props.dataModal.naturezaOperacao?.naturezaOperacaoWithCfopFora;
  }

  return CST_CSOSN_COM_ST.includes(product?.selectData[field]?.codCstCsosn)
    ? props.dataModal.naturezaOperacao?.naturezaOperacaoWithCfopDentroSt
    : props.dataModal.naturezaOperacao?.naturezaOperacaoWithCfopDentro;
};

const forms = ref({
  codProduto: null,
  qtde: 1,
  nome: '',
  valorUnitario: 0,
  tipoValorDesconto: '0',
  tipoValorAcrescimo: '0',
  valorDesconto: 0,
  valorAcrescimo: 0,
  usaGrade: false,
  xPed: null,
  nItemPed: null,
  selectData: null,
  fiscal: {
    codCstCsosn: null,
    codCfop: null,
    cfop: null,
    codBeneficio: null,
    codBeneficioRbc: null,
    valorIcmsDeson: 0,
    aliqIcmsDeson: 0,
    motivoIcmsDeson: null,
    infosAdicionais: '',
    codPis: null,
    codNcmNew: null,
    codCestNew: null,

    // Impostos
    aliqIcms: null,
    percReducaoBcIcms: null,
    bcIcms: null,
    valorIcms: null,
    aliqPis: null,
    bcPis: null,
    valorPis: null,
    aliqIcmsSt: null,
    percReducaoBcIcmsSt: null,
    percMva: null,
    bcIcmsSt: null,
    valorIcmsSt: null,
    aliqCofins: null,
    bcCofins: null,
    valorCofins: null,
    aliqIpi: null,
    bcIpi: null,
    valorIpi: null,
    aliqFcp: null,
    bcFcp: null,
    valorFcp: null,
    aliqFcpSt: null,
    bcFcpSt: null,
    valorFcpSt: null
  }
});

let cestFiltersV2 = [];

function onChangeNcm(value) {
  forms.value.fiscal.codCestNew = null;

  if (value) {
    cestFiltersV2 = [
      {
        field: 'codNcm',
        filterType: 'EQUALS',
        filterValue: value
      }
    ];
  } else {
    cestFiltersV2 = [{ field: 'codNcm', filterType: 'NULL' }];
  }
}

const valorBruto = computed(() =>
  round(forms.value.qtde * forms.value.valorUnitario, 4)
);

const calcValorDescontoItem = (produto) => {
  if (!produto.valorDesconto || !valorBruto.value) return 0;

  if (produto.tipoValorDesconto === 0) {
    return round(produto.valorDesconto, 2);
  } else {
    return round((valorBruto.value * produto.valorDesconto) / 100, 2);
  }
};

const calcValorIcmsDeson = (produto) => {
  const desconto = calcValorDescontoItem(produto);

  const valorIcmsDeson = round(
    (valorBruto.value - desconto) * (produto.fiscal.aliqIcmsDeson / 100),
    2
  );

  return valorIcmsDeson;
};

onMounted(async () => {
  await company.get();

  const { produto } = props.dataModal;

  if (props.dataModal.isEditing) {
    forms.value.fiscal = produto.fiscal;

    cfopEditValue.value =
      produto?.cfopEditValue ||
      produto?.itemData?.vendaItemWithFiscal?.vendaItemFiscalWithCfop ||
      produto?.selectData?.produtoWithFiscal?.produtoFiscalWithCfop ||
      null;
  } else {
    cfopEditValue.value = fetchOperationNatureCfop(produto);
    forms.value.fiscal.cfop = fetchOperationNatureCfop(produto)?.codCfop;
    forms.value.fiscal.codCfop = fetchOperationNatureCfop(produto)?.controle;
  }

  const validacaoCstCsosnCfop = validarCstCsosnCfop({
    cfop: forms.value.fiscal?.cfop,
    cstCsosn: produto?.fiscal?.codCstCsosn,
    finalidadeNaturezaOperacao:
      props.dataModal.naturezaOperacao.finalidadeOperacao
  });

  await handleMostrarCardInfoTributaria(validacaoCstCsosnCfop);

  cstEditValue.value =
    produto?.cstCsosnEditValue ||
    produto?.itemData?.vendaItemWithFiscal?.vendaItemFiscalWithCst ||
    produto?.selectData?.produtoWithFiscal?.produtoFiscalWithCst ||
    null;

  csosnEditValue.value =
    produto?.cstCsosnEditValue ||
    produto?.itemData?.vendaItemWithFiscal?.vendaItemFiscalWithCsosn ||
    produto?.selectData?.produtoWithFiscal?.produtoFiscalWithCsosn ||
    null;

  if (produto.adicionandoItem) {
    forms.value.fiscal.codNcmNew =
      produto.selectData?.produtoWithFiscal?.codNcmNew ?? null;
    forms.value.fiscal.codCestNew =
      produto.selectData?.produtoWithFiscal?.codCestNew ?? null;
  } else {
    forms.value.fiscal.codNcmNew = produto.fiscal.codNcmNew ?? null;
    forms.value.fiscal.codCestNew = produto.fiscal.codCestNew ?? null;
  }

  if (forms.value.fiscal.codNcmNew) {
    cestFiltersV2 = [
      {
        field: 'codNcm',
        filterType: 'EQUALS',
        filterValue: forms.value.fiscal.codNcmNew
      }
    ];
  }

  forms.value.selectData = produto?.selectData;
  forms.value.fiscal.codPis = produto?.selectData?.produtoWithFiscal?.codPis;
  forms.value.codProduto = produto.selectData?.controle;
  forms.value.nome = produto?.nome || produto.selectData?.nome;
  forms.value.fiscal.codCstCsosn = produto?.fiscal?.codCstCsosn;

  forms.value.fiscal.codBeneficio = produto?.fiscal?.codBeneficio;
  forms.value.fiscal.codBeneficioRbc = produto?.fiscal?.codBeneficioRbc || null;
  forms.value.fiscal.valorIcmsDeson = produto?.fiscal?.valorIcmsDeson || 0;
  forms.value.fiscal.percReducaoBcIcms =
    produto?.fiscal?.percReducaoBcIcms || 0;
  forms.value.fiscal.aliqIcmsDeson = produto?.fiscal?.aliqIcmsDeson || 0;
  forms.value.fiscal.motivoIcmsDeson = produto?.fiscal?.motivoIcmsDeson || null;
  forms.value.fiscal.infosAdicionais = produto?.fiscal?.infosAdicionais || '';

  forms.value.qtde = produto?.qtde;
  forms.value.valorUnitario = produto?.valorUnitario;
  forms.value.tipoValorDesconto = produto?.tipoValorDesconto;
  forms.value.tipoValorAcrescimo = produto?.tipoValorAcrescimo;
  forms.value.valorDesconto = produto?.valorDesconto;
  forms.value.valorAcrescimo = produto?.valorAcrescimo;
  forms.value.xPed = produto?.xPed || produto?.itemData?.xPed;
  forms.value.nItemPed = produto?.nItemPed || produto?.itemData?.nItemPed;
  forms.value.usaGrade = produto?.usaGrade;

  if (
    (!props.dataModal.isEditing && mostrarCardInfoTributaria.value) ||
    (produto.fiscal.fetchImpostos && mostrarCardInfoTributaria.value)
  ) {
    resetInfoTributaria();

    await fetchImpostos();
  }

  loading.value = false;
  cfopKey.value = true;

  nextTick(() => {
    mounted.value = true;
  });
});

const informacoesTributariasEditaveis = computed(() => {
  const cstCsosn = ['900', '90', '20'];

  return cstCsosn.includes(forms.value.fiscal.codCstCsosn);
});

const isSaveEnabled = computed(() => {
  let enableSaveButton = false;

  const {
    valorIcmsDeson,
    motivoIcmsDeson,
    codCstCsosn,
    codNcmNew,
    codBeneficioRbc
  } = forms.value.fiscal;
  const { qtde: quantidade, valorUnitario } = forms.value;

  const validacaoDesoneracao =
    (valorIcmsDeson > 0 && motivoIcmsDeson) ||
    (valorIcmsDeson <= 0 && !motivoIcmsDeson) ||
    !LISTA_CST_DESONERACAO.includes(codCstCsosn);

  const validacaoValorUnitario = valorUnitario != 0 && valorUnitario != null;
  const validacaoQuantidade = quantidade != 0 && quantidade != null;

  const validacaoValorQuantidadeNatureza = computed(() => {
    if (naturezaOperacaoDeAjusteOuComplementar) {
      return true;
    }

    return validacaoValorUnitario && validacaoQuantidade;
  });

  const validatCstCsosn =
    csosnEditValue.value?.codigo || cstEditValue.value?.codigo;

  let validacaoBeneficioFiscalRBC = true;

  if (mostrarBeneficioFiscalRBC.value) {
    const codBeneficioRbcPreenchido = Boolean(codBeneficioRbc);
    validacaoBeneficioFiscalRBC = codBeneficioRbcPreenchido;
  }

  enableSaveButton =
    validatCstCsosn &&
    cfopEditValue.value &&
    codNcmNew &&
    validacaoDesoneracao &&
    validacaoBeneficioFiscalRBC &&
    validacaoValorQuantidadeNatureza.value;

  return enableSaveButton;
});

function toFloat(str) {
  if (!str) return 0;

  str = str.replace(/\./g, '');
  str = str.replace(/,/g, '.');
  str = str.replace(/\s/g, '');

  return parseFloat(str);
}

const descontoPercentageRules = [
  (val) => {
    return toFloat(val) < 100 || !toFloat(val) || 'Desconto superior à 99,9%';
  }
];

const descontoMoneyRules = (val, item) => {
  return (
    toFloat(val) < (item.qtde * item.valorUnitario).toFixed(2) ||
    !toFloat(val) ||
    'Desconto não pode ser superior ou igual ao valor unitário'
  );
};

const abrirModalAlteracaoDadosFiscais = async () => {
  if (!informacoesTributariasEditaveis.value) return true;

  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        scope: 'client-change',
        title: 'Alteração de dados fiscais',
        classCardSection: 'lg:tw-w-[500px]',
        confirmLabel: 'SIM',
        cancelLabel: 'NÃO',
        description:
          'Essa alteração removerá a possibilidade de edição das informações tributárias, deseja continuar?'
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const abrirDesejaRecalcularModal = async () => {
  if (!informacoesTributariasEditaveis.value) {
    return fetchImpostos();
  }

  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      scope: 'deseja-recalcular',
      title: 'Informações tributárias',
      classCardSection: 'lg:tw-w-[500px]',
      confirmLabel: 'Calcular',
      cancelLabel: 'Manter',
      description:
        'Mudar esse campo altera a lógica de calculo das informações tributárias. Deseja recalcular as informações tributárias?'
    }
  }).onOk(() => fetchImpostos());
};

const handleMostrarCardInfoTributaria = async (validacaoCstCsosnCfop) => {
  // Mostrar card de tributação
  if (validacaoCstCsosnCfop) {
    // if (!mostrarCardInfoTributaria.value ) {
    //   await fetchImpostos();
    // }

    mostrarCardInfoTributaria.value = true;

    return true;
  }

  // Verificar se usuário deseja remover o card de tributação
  if (mostrarCardInfoTributaria.value && !validacaoCstCsosnCfop) {
    const response = await abrirModalAlteracaoDadosFiscais();

    if (response) {
      mostrarCardInfoTributaria.value = false;

      resetInfoTributaria();

      return true;
    }

    return false;
  }

  // Continua a lógica
  return true;
};

const resetInfoTributaria = () => {
  forms.value.fiscal.fetchImpostos = null;
  forms.value.fiscal.aliqIcms = null;
  forms.value.fiscal.percReducaoBcIcms = null;
  forms.value.fiscal.bcIcms = null;
  forms.value.fiscal.valorIcms = null;
  forms.value.fiscal.aliqPis = null;
  forms.value.fiscal.bcPis = null;
  forms.value.fiscal.valorPis = null;
  forms.value.fiscal.aliqIcmsSt = null;
  forms.value.fiscal.bcIcmsSt = null;
  forms.value.fiscal.valorIcmsSt = null;
  forms.value.fiscal.percReducaoBcIcmsSt = null;
  forms.value.fiscal.percMva = null;
  forms.value.fiscal.aliqCofins = null;
  forms.value.fiscal.bcCofins = null;
  forms.value.fiscal.valorCofins = null;
  forms.value.fiscal.aliqIpi = null;
  forms.value.fiscal.bcIpi = null;
  forms.value.fiscal.valorIpi = null;
  // forms.value.fiscal.aliqFcp = null
  // forms.value.fiscal.bcFcp = null
  // forms.value.fiscal.valorFcp = null
  forms.value.fiscal.aliqFcpSt = null;
  forms.value.fiscal.bcFcpSt = null;
  forms.value.fiscal.valorFcpSt = null;
};

function onCancel() {
  emit('on-cancel');
}

function onConfirm() {
  const cstCsosnEditValue =
    companyData.value.empresaWithEmpresaFiscal?.crt == 1 ||
    companyData.value.empresaWithEmpresaFiscal?.crt == 5
      ? csosnEditValue.value
      : cstEditValue.value;

  emit('ok', {
    ...forms.value,
    cstCsosnEditValue: toRaw(cstCsosnEditValue),
    cfopEditValue: toRaw(cfopEditValue.value)
  });
}

const atalhos = [
  {
    key: 'esc',
    event: () => onCancel()
  },
  {
    key: 'enter',
    event: () => onConfirm(),
    condition: computed(() => isSaveEnabled.value)
  }
];

// Definir atalhos
useScopedHotkeys(atalhos, props.scope);
</script>
