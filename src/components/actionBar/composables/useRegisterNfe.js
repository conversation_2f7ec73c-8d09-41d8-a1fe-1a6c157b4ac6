import { computed, onBeforeUnmount, ref, nextTick, toRefs } from 'vue';
import { buttonsProperties } from './actionBarButtons';
import { emitter as mitt } from 'src/boot/mitt';
import { dom } from 'quasar';
const { ready } = dom;
import { useSteps } from 'stores/api/firstStep';
import { useGlobal } from 'stores/global';

const passos = useSteps();
const global = useGlobal();
passos.get(global.company.controle);

const { data: passosData } = toRefs(passos);

const disableNf = computed(() => {
  return (
    !passosData.value.configNfe ||
    !passosData.value.certificado ||
    !passosData.value.produto
  );
});

/**
 * Retorna uma instância computada que controla os botões de ação para um registro.
 * @param {Object} options - Parâmetro da função.
 * @param {Ref} options.params - Parâmetros do registro, contendo se permite desfazer alterações.
 * @param {Object} options.events - Objeto de eventos contendo os callbacks para os botões.
 * @returns {ComputedRef} - Uma instância computada que controla os botões de ação.
 */
export default function useRegisterNfe({
  params,
  events = {},
  readonly = false,
  isDevolucao = false
}) {
  const defaultEvents = {};
  const modalCount = ref(document.querySelectorAll('.sgbrDialogs').length);

  // Join dos eventos default com os personalizados.
  let joinedEvents = { ...defaultEvents, ...events };

  // Ativa listeners de todo os eventos
  function mount() {
    Object.keys(joinedEvents).forEach((event) => {
      mitt.on(`${event}-${modalCount.value}`, joinedEvents[event].callback);
    });
  }

  // Desativa os listeners dos eventos.
  function unmount() {
    Object.keys(joinedEvents).forEach((event) => {
      mitt.off(`${event}-${modalCount.value}`);
    });
  }

  // Desativa todos os eventos criados.
  onBeforeUnmount(() => unmount());

  // Armazena a regra de renderização dos botões para as telas de cadastro.
  const computedInstance = computed({
    get() {
      if (!params) return [];

      // Lógica de para botão de cancelar e desfazer alterações.
      let buttons = !readonly
        ? !params || !params.isWarningAnimationDisabled.value
          ? ['reset', 'espelhoPadrao', 'save', 'issueNfe']
          : ['cancel', 'espelhoPadrao', 'save', 'issueNfe']
        : [...Object.keys(joinedEvents)];

      if (isDevolucao.value)
        buttons = buttons.filter((button) => button !== 'save');

      // Retorna as propriedades dos botões. Seta os eventos que serão acionados ao clicar no botão.
      return buttons.map((btn) => {
        if (buttonsProperties[btn]) {
          if (btn == 'save') {
            return {
              ...buttonsProperties[btn],
              event: () => {
                mitt.emit(`${btn}-${modalCount.value}`);
              },
              text: 'FINALIZAR',
              disabled: !params.completed.value,
              tooltipText: !params.completed.value
                ? 'É necessário preencher os dados obrigatórios para salvar a venda'
                : 'Salvar NF-e'
            };
          }

          if (btn == 'espelhoPadrao') {
            return {
              ...buttonsProperties[btn],
              event: () => {
                mitt.emit(`${btn}-${modalCount.value}`);
              },
              disabled: !params.completed.value,
              tooltipText: !params.completed.value
                ? 'É necessário preencher os dados obrigatórios para gerar o espelho'
                : 'Gerar espelho NF-e'
            };
          }

          if (btn == 'issueNfe') {
            const tooltipText = computed(() => {
              if (disableNf.value) {
                return 'Adicione as configurações e certificado para emitir a NF-e. Verifique se itens foram adicionados.';
              } else if (!params.completed.value) {
                return 'É necessário preencher os dados obrigatórios para salvar e emitir a NF-e';
              } else {
                return 'Salvar e emitir a NF-e';
              }
            });

            return {
              ...buttonsProperties[btn],
              event: () => {
                mitt.emit(`${btn}-${modalCount.value}`);
              },

              disabled: disableNf.value || !params.completed.value,
              tooltipText: tooltipText.value
            };
          }
          return {
            ...buttonsProperties[btn],
            event: () => {
              mitt.emit(`${btn}-${modalCount.value}`);
            }
          };
        }
      });
    },

    // Toda vez que a instancia computada é recalculada, verifica a quantidade de modais abertos para remontar os eventos apropriadamente.
    set() {
      modalCount.value = document.querySelectorAll('.sgbrDialogs').length;
      unmount();
      mount();
    }
  });

  // Após a renderização da tela, força recálculo da instancia computada para modais abertos.
  // Causa trigger na instancia computada, passando por set().
  ready(() => {
    nextTick(() => {
      computedInstance.value++;
    });
  });

  return computedInstance;
}
