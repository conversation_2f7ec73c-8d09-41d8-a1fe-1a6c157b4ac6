import { toTypedSchema } from '@vee-validate/yup';
import { userTemplate } from 'src/components/ConfigEmpresa/Email/HtmlTemplates/userTemplate';
import { useField, useFieldArray, useForm } from 'vee-validate';
import { ref } from 'vue';
import { array, boolean, number, object, string } from 'yup';
import { emailRegex } from 'src/components/utils/emailRegex';

/**
 * Retorna os objetos de formulários vinculados com suas configurações de validação.
 * @returns {Object} Um objeto contendo os formulários vinculados e funções auxiliares.
 */
export function useFormSchemaConfig(initialValues = false) {

  const schema = object({
    controle: string().default('').nullable(),
    manterFocoCampoBusca: boolean().default(false),
    bloquearVendaEstoqueNegativoOuZerado: boolean().default(true),
    exigirVendedorParaRealizarVenda: boolean().default(false),
    exigirSupervisorExcluirItem: boolean().default(false),
    codCentroCusto: string().default('1').required('O campo é obrigatório.'),
    codNaturezaOperacao: string()
      .default('1')
      .required('O campo é obrigatório.'),
    codCfop: string().default('1').required('O campo é obrigatório.'),
    permitirFinalizarVendaSemEmissaoNfce: boolean().default(false),
    infosAdicionaisPadrao: string('').default('').nullable(),
    observacoesPadrao: string('').default('').nullable(),
    reservaEstoque: boolean().default(false),
    buscaPadrao: number()
      .default(1)
      .required('O campo é obrigatório.')
      .transform((val) => Number(val || 1) || 1),
    opcoesLeitura: number().default(1).required('O campo é obrigatório.'),
    senhaSupervisor: string().default(''),
    caixaCego: boolean().default(false),
    mostrarConfissaoDividaNotaManual: string().default('1').nullable(),
    usarCanhotoNfce: boolean().default(true).nullable(),
    mostrarConfissaoDividaNfce: string().default('3').nullable(),
    identificacaoProdutoNotaManual: string().default('1').nullable(),
    padraoWhatsApp: boolean().default(false).nullable(),
    usuario: object({
      createdAt: string().default('').nullable(),
      nfce: object({
        tipoImpressao: string().default('2').nullable(),

        logotipoPadrao: boolean().default(true).nullable(),
        logotipoImprimir: boolean().default(true).nullable(),

        margemPadraoA4: boolean().default(true).nullable(),
        margemTopoA4: number().default(0).nullable(),
        margemEsquerdaA4: number().default(0).nullable(),
        margemDireitaA4: number().default(0).nullable(),
        margemBaixoA4: number().default(0).nullable(),

        margemPadrao: boolean().default(true).nullable(),
        margemTopo: number().default(0).nullable(),
        margemEsquerda: number().default(0).nullable(),
        margemDireita: number().default(0).nullable(),
        margemBaixo: number().default(0).nullable(),

        margemBaixo58: number().default(0).nullable(),
        margemEsquerda58: number().default(0).nullable(),
        margemDireita58: number().default(0).nullable(),
        margemTopo58: number().default(0).nullable(),
        margemPadrao58: boolean().default(true).nullable(),
        impressaoRapida: object({
          modulo: number().default(1), // 1 - PDV
          habilitarImpressaoRapida: boolean().default(false),
          usarPadraoUsuario: boolean().default(true),
          ipConexaoRede: string()
            .default('')
            .nullable()
            .transform((value) => (value === null ? '' : value)),
          impressora: string()
            .default('')
            .when(
              ['usarPadraoUsuario', 'habilitarImpressaoRapida'],
              ([usarPadraoUsuario, habilitarImpressaoRapida], schema) => {
                return !usarPadraoUsuario && habilitarImpressaoRapida
                  ? schema.required('Obrigatório selecionar uma impressora')
                  : schema.nullable();
              }
            )
        })
      }),
      notaManual: object({
        tipoImpressao: string().default('3').nullable(),

        logotipoPadrao: boolean().default(true).nullable(),
        logotipoImprimir: boolean().default(true).nullable(),

        margemPadraoA4: boolean().default(true).nullable(),
        margemTopoA4: number().default(0).nullable(),
        margemEsquerdaA4: number().default(0).nullable(),
        margemDireitaA4: number().default(0).nullable(),
        margemBaixoA4: number().default(0).nullable(),

        margemPadrao: boolean().default(true).nullable(),
        margemTopo: number().default(0).nullable(),
        margemEsquerda: number().default(0).nullable(),
        margemDireita: number().default(0).nullable(),
        margemBaixo: number().default(0).nullable(),

        margemBaixo58: number().default(0).nullable(),
        margemEsquerda58: number().default(0).nullable(),
        margemDireita58: number().default(0).nullable(),
        margemTopo58: number().default(0).nullable(),
        margemPadrao58: boolean().default(true).nullable()
      })
    }),
    email: object({
      padrao: boolean().default(true).nullable(),
      tipo: number().default(3).nullable(),
      enviaEmailEmissao: boolean()
        .default(false)
        .required('O campo é obrigatório.'),
      smtpServidor: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpPorta: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpUsuario: string()
        .default('')
        .test((value, ctx) => {
          const { padrao } = ctx.parent;
          if (!padrao && !value) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }
          if (value && value.includes('@') ? !emailRegex.test(value) : false) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpEmail: string()
        .default('')
        .test((value, ctx) => {
          const { smtpUsuario } = ctx.parent;
          if (smtpUsuario && !emailRegex.test(smtpUsuario) && !value) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }
          if (value && !emailRegex.test(value)) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpSenha: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpSSL: boolean()
        .default(false)
        .required('O campo é obrigatório.')
        .nullable(),
      smtpAutoTls: boolean()
        .default(true)
        .required('O campo é obrigatório.')
        .nullable(),
      assuntoEmail: string().default('').nullable(),
      descricaoRemetente: string().default('').nullable(),
      arquivosPartes: boolean().default(false).nullable(),
      tamanhoArquivo: string().default('15').nullable().test((value, ctx) => {
        return (value > 0 && !ctx.parent.padrao) || ctx.parent.padrao || (!ctx.parent.padrao && !ctx.parent.arquivosPartes) || (ctx.parent.arquivosPartes && ctx.createError({ message: 'Minimo de 1 MB' }));
      }),
      emailsAdicionais: array().default([]).nullable(),
      footerHtml: string().default(userTemplate).nullable()
    }),
    emailXml: object({
      tipo: number().default(1).nullable(),
      padraoEmailXml: boolean().default(true).nullable(),
      assuntoEmailXml: string().default('').nullable(),
      emailPrincipalXml: string()
        .default('')
        .when('padraoEmailXml', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      emailsAdicionais: array().default([]).nullable()
    })
  });

  // Configura os valores do formulário e suas validações.
  const form = useForm({
    validationSchema: toTypedSchema(schema),
    validateOnMount: false,
    initialValues: initialValues
      ? Object.keys(initialValues).length === 1
        ? initialValues
        : schema
            .json()
            .cast(initialValues, { assert: false, stripUnknown: true })
      : undefined,
    initialErrors: {}
  });

  // Função recursiva para criar campos dinamicamente
  function createFields(schema, parentKey = '') {
    const fields = {};
    Object.keys(schema.fields).forEach((key) => {
      const fieldKey = parentKey ? `${parentKey}.${key}` : key;
      const fieldSchema = schema.fields[key];
      if (fieldSchema.type === 'object') {
        fields[key] = createFields(fieldSchema, fieldKey);
      } else if (fieldSchema.type === 'array' && fieldSchema.schema) {
        fields[key] = useFieldArray(fieldKey);
      } else {
        fields[key] = useField(fieldKey);
      }
    });
    return fields;
  }

  const fields = ref(createFields(schema));
  return { ...form, fields, useField };
}
