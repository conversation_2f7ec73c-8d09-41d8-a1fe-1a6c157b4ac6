<template>
  <div>
    <SGRegisterSkeleton v-if="isLoadingPage" />

    <SGRegisterPage
      v-else
      ref="registerPageRef"
      id="sessao-emissao-pdv"
      title="Configurações do PDV"
      :buttons="actionBarButtons"
      :modal="modal"
      :disable-warning-animation="isWarningAnimationDisabled"
    >
      <section class="tw-col-span-full">
        <q-tabs
          v-model="tab"
          dense
          class="tw-col-span-full tw-rounded-t-md tw-text-textsSGBR-gray"
          align="left"
          narrow-indicator
          active-color="primary"
          indicator-color="primary"
        >
          <q-tab
            name="user"
            label="Usuário"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
          />
          <q-tab
            name="company"
            label="Empresa"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
            :disable="!global?.roles?.includes('VENDA.PDV:CONFIGURACAO')"
          >
            <TooltipCustom
              v-if="!global?.roles?.includes('VENDA.PDV:CONFIGURACAO')"
              text-tooltip="Você não possui permissão para alterar as
            configurações da empresa"
            />
          </q-tab>
        </q-tabs>

        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tab"
          animated
          class="tw-col-span-full tw-bg-inherit"
          transition-prev="jump-right"
          transition-next="jump-left"
          :transition-duration="120"
          @transition="tabChange"
        >
          <q-tab-panel
            name="user"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <SGCard
              title="Configurações NFC-e"
              id="sessao-nfce-pdv-usuario"
              :cols="8"
              :schema-errors="errors"
              :meta="meta"
              remove-gap
              has-title-separator
              has-circle
            >
              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Tipo impressão</p>
                <q-option-group
                  v-model="fields.usuario.nfce.tipoImpressao.value"
                  :error="!!fields.usuario.nfce.tipoImpressao.errorMessage"
                  :error-message="
                    fields.usuario.nfce.tipoImpressao.errorMessage
                  "
                  :valid="fields.usuario.nfce.tipoImpressao.meta.valid"
                  :options="typesPrintNFCe"
                  color="primary"
                  size="30px"
                  inline
                  class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <Logotipo
                v-model:fields="fields.usuario.nfce"
                :has-default="true"
                class="tw-col-span-full"
              />

              <div
                class="tw-col-span-full tw-mt-4 tw-flex tw-flex-wrap tw-gap-customGridGapCards"
              >
                <Margins
                  title="Margens impressão (A4)"
                  v-model:fields="fields.usuario.nfce"
                  :has-margin-default="true"
                  type="A4"
                />

                <Margins
                  title="Margens impressão térmica (80mm)"
                  v-model:fields="fields.usuario.nfce"
                  :has-margin-default="true"
                />

                <Margins
                  title="Margens impressão térmica (58mm)"
                  v-model:fields="fields.usuario.nfce"
                  :has-margin-default="true"
                  type="58"
                />
              </div>
            </SGCard>

            <SGCard
              id="sessao-nota-manual-pdv-usuario"
              title="Configurações de nota manual"
              :cols="8"
              :schema-errors="errors"
              :meta="meta"
              has-title-separator
              has-circle
            >
              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Tipo impressão</p>
                <q-option-group
                  v-model="fields.usuario.notaManual.tipoImpressao.value"
                  :error="
                    !!fields.usuario.notaManual.tipoImpressao.errorMessage
                  "
                  :error-message="
                    fields.usuario.notaManual.tipoImpressao.errorMessage
                  "
                  :valid="fields.usuario.notaManual.tipoImpressao.meta.valid"
                  :options="typesPrintManual"
                  color="primary"
                  size="2rem"
                  inline
                  class="tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <Logotipo
                v-model:fields="fields.usuario.notaManual"
                :has-default="true"
                class="tw-col-span-full"
              />

              <div
                class="tw-col-span-full tw-flex tw-flex-wrap tw-gap-customGridGapCards"
              >
                <Margins
                  title="Margens impressão (A4)"
                  v-model:fields="fields.usuario.notaManual"
                  :has-margin-default="true"
                  type="A4"
                />

                <Margins
                  title="Margens impressão térmica (80mm)"
                  v-model:fields="fields.usuario.notaManual"
                  :has-margin-default="true"
                />

                <Margins
                  title="Margens impressão térmica (58mm)"
                  v-model:fields="fields.usuario.notaManual"
                  :has-margin-default="true"
                  type="58"
                />
              </div>
            </SGCard>

            <SGCard
              title="Impressão rápida"
              id="fast-print-integration"
              :schema-errors="errors"
              :error-keys="['usuario.nfce.impressaoRapida.impressora']"
              :meta="meta"
              :cols="8"
              remove-gap
              has-circle
              has-title-separator
            >
              <q-checkbox
                @update:model-value="handleEnableFastPrint"
                v-model="
                  fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                    .value
                "
                :error="
                  !!fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :error-message="
                  fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :valid="
                  fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                    .meta.valid
                "
                size="2rem"
                label="Habilitar impressão rápida"
                class="tw-col-span-full"
              />

              <q-checkbox
                v-model="
                  fields.usuario.nfce.impressaoRapida.usarPadraoUsuario.value
                "
                :error="
                  !!fields.usuario.nfce.impressaoRapida.usarPadraoUsuario
                    .errorMessage
                "
                :error-message="
                  fields.usuario.nfce.impressaoRapida.usarPadraoUsuario
                    .errorMessage
                "
                :valid="
                  fields.usuario.nfce.impressaoRapida.usarPadraoUsuario.meta
                    .valid
                "
                :disable="
                  !fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                    .value
                "
                size="2rem"
                label="Usar impressão rápida padrão do usuário"
                class="tw-col-span-full tw-mb-4"
              />

              <div
                class="tw-col-span-full tw-flex tw-flex-col tw-gap-4"
                :class="{
                  'tw-pointer-events-none tw-opacity-40':
                    !fields.usuario.nfce.impressaoRapida
                      .habilitarImpressaoRapida.value ||
                    fields.usuario.nfce.impressaoRapida.usarPadraoUsuario.value
                }"
              >
                <FastPrintSteps
                  v-model:fields="fields.usuario.nfce.impressaoRapida"
                  :is-fast-print-enabled="
                    fields.usuario.nfce.impressaoRapida.habilitarImpressaoRapida
                      .value &&
                    !fields.usuario.nfce.impressaoRapida.usarPadraoUsuario.value
                  "
                />
              </div>
            </SGCard>
          </q-tab-panel>

          <q-tab-panel
            name="company"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <SGCard
              title="Gerais"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              id="sessao-nfe-nf-gerais"
              has-title-separator
              has-circle
              container-class="tw-gap-y-1"
            >
              <q-checkbox
                v-model="fields.reservaEstoque.value"
                :error="!!fields.reservaEstoque.errorMessage"
                :error-message="fields.reservaEstoque.errorMessage"
                :valid="fields.reservaEstoque.meta.valid"
                label="Reserva estoque"
                class="tw-col-span-4"
                size="2rem"
              />
              <q-checkbox
                v-model="fields.bloquearVendaEstoqueNegativoOuZerado.value"
                :error="
                  !!fields.bloquearVendaEstoqueNegativoOuZerado.errorMessage
                "
                :error-message="
                  fields.bloquearVendaEstoqueNegativoOuZerado.errorMessage
                "
                :valid="fields.bloquearVendaEstoqueNegativoOuZerado.meta.valid"
                color="primary"
                size="2rem"
                class="tw-col-span-4"
                label="Bloquear venda com estoque negativo ou zerado"
              />
              <q-checkbox
                v-model="fields.exigirSupervisorExcluirItem.value"
                :error="!!fields.exigirSupervisorExcluirItem.errorMessage"
                :error-message="fields.exigirSupervisorExcluirItem.errorMessage"
                :valid="fields.exigirSupervisorExcluirItem.meta.valid"
                color="primary"
                size="2rem"
                class="tw-col-span-4"
                label="Exigir supervisor para excluir item"
              />
              <q-checkbox
                v-model="fields.permitirFinalizarVendaSemEmissaoNfce.value"
                :error="
                  !!fields.permitirFinalizarVendaSemEmissaoNfce.errorMessage
                "
                :error-message="
                  fields.permitirFinalizarVendaSemEmissaoNfce.errorMessage
                "
                :valid="fields.permitirFinalizarVendaSemEmissaoNfce.meta.valid"
                @update:model-value="
                  (value) => {
                    if (!value) {
                      setFieldValue('infosAdicionaisPadrao', '');
                      setFieldValue('observacoesPadrao', '');
                    }
                  }
                "
                color="primary"
                size="2rem"
                class="tw-col-span-4"
                label="Permitir finalização de venda sem emissão de NFC-e (nota manual)"
              />
              <q-checkbox
                v-model="fields.caixaCego.value"
                :error="!!fields.caixaCego.errorMessage"
                :error-message="fields.caixaCego.errorMessage"
                :valid="fields.caixaCego.meta.valid"
                color="primary"
                class="tw-col-span-4"
                size="2rem"
                label="Fechamento de caixa cego"
              />
              <q-input
                v-if="values.exigirSupervisorExcluirItem"
                v-model="fields.senhaSupervisor.value"
                :error="!!fields.senhaSupervisor.errorMessage"
                :error-message="fields.senhaSupervisor.errorMessage"
                :valid="fields.senhaSupervisor.meta.valid"
                v-bind="ModelInputText"
                type="password"
                placeholder="********"
                label="Senha do supervisor"
                class="tw-col-span-8"
                label-slot
                maxlength="20"
              />
            </SGCard>

            <SGCard
              v-if="values.permitirFinalizarVendaSemEmissaoNfce"
              title="Configurações de nota manual"
              id="pdv-config-nota-manual"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              remove-gap
              has-title-separator
              has-circle
            >
              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">
                  Identificação do produto na nota manual
                </p>
                <q-option-group
                  v-model="fields.identificacaoProdutoNotaManual.value"
                  :error="!!fields.identificacaoProdutoNotaManual.errorMessage"
                  :error-message="
                    fields.identificacaoProdutoNotaManual.errorMessage
                  "
                  :valid="fields.identificacaoProdutoNotaManual.meta.valid"
                  :options="identificationsProduct"
                  color="primary"
                  size="2rem"
                  inline
                  class="tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Confissão de dívida</p>
                <q-option-group
                  v-model="fields.mostrarConfissaoDividaNotaManual.value"
                  :error="
                    !!fields.mostrarConfissaoDividaNotaManual.errorMessage
                  "
                  :error-message="
                    fields.mostrarConfissaoDividaNotaManual.errorMessage
                  "
                  :valid="fields.mostrarConfissaoDividaNotaManual.meta.valid"
                  :options="confissaoOptions"
                  color="primary"
                  size="2rem"
                  inline
                  class="tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <q-input
                v-model="fields.infosAdicionaisPadrao.value"
                :error="!!fields.infosAdicionaisPadrao.errorMessage"
                :error-message="fields.infosAdicionaisPadrao.errorMessage"
                :valid="fields.infosAdicionaisPadrao.meta.valid"
                v-bind="ModelInputText"
                label="Informações adicionais padrão"
                type="textarea"
                class="tw-col-span-4 !tw-max-h-[126px]"
                input-style="min-height: 80px; max-height: 80px"
                maxlength="255"
                autogrow
                label-slot
                bottom-slots
              />

              <q-input
                v-model="fields.observacoesPadrao.value"
                :error="!!fields.observacoesPadrao.errorMessage"
                :error-message="fields.observacoesPadrao.errorMessage"
                :valid="fields.observacoesPadrao.meta.valid"
                v-bind="ModelInputText"
                label="Observações padrão"
                maxlength="255"
                type="textarea"
                class="tw-col-span-4 !tw-max-h-[126px]"
                input-style="min-height: 80px; max-height: 80px"
                autogrow
                label-slot
                bottom-slots
              />
            </SGCard>
            <SGCard
              title="Configurações de NFC-e"
              id="sessao-nfce-pdv-empresa"
              :cols="8"
              :schema-errors="errors"
              :meta="meta"
              remove-gap
              has-title-separator
              has-circle
            >
              <div class="tw-col-span-full tw-mb-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Incluir canhoto (A4)</p>
                <q-option-group
                  v-model="fields.usarCanhotoNfce.value"
                  :error="!!fields.usarCanhotoNfce.errorMessage"
                  :error-message="fields.usarCanhotoNfce.errorMessage"
                  :valid="fields.usarCanhotoNfce.meta.valid"
                  :options="canhotoOptions"
                  color="primary"
                  size="2rem"
                  class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                  inline
                />
              </div>

              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Confissão de dívida</p>
                <q-option-group
                  v-model="fields.mostrarConfissaoDividaNfce.value"
                  :error="!!fields.mostrarConfissaoDividaNfce.errorMessage"
                  :error-message="
                    fields.mostrarConfissaoDividaNfce.errorMessage
                  "
                  :valid="fields.mostrarConfissaoDividaNfce.meta.valid"
                  :options="confissaoOptions"
                  color="primary"
                  size="2rem"
                  inline
                  class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>
            </SGCard>
            <SGCard
              id="sessao-pdv-nf-financeiro"
              title="Financeiro"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              remove-gap
              has-title-separator
              has-circle
            >
              <CostCenterRegisterModal
                v-if="costCenterRegisterModalModel"
                @update:model-modal="
                  (newValue) => (costCenterRegisterModalModel = newValue)
                "
                @on-save="updateCostCenterOptions"
                @on-cancel="costCenterRegisterModalModel = false"
                :model-value="costCenterRegisterModalModel"
              />

              <InputSelect
                v-model="fields.codCentroCusto.value"
                :error="!!fields.codCentroCusto.errorMessage"
                :error-message="fields.codCentroCusto.errorMessage"
                :valid="fields.codCentroCusto.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/centro/custo',
                    filterOr: ['controle', 'descricao'],
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                role-prefix="CADASTRO.CENTRO-CUSTO"
                class="tw-col-span-4"
                label="Centro de custo"
                required
                add-button
                @on-add="() => (costCenterRegisterModalModel = true)"
              />

              <InputSelect
                @on-add="openRegisterNature"
                @on-edit="openRegisterNature"
                ref="natureOperationRef"
                v-model="fields.codNaturezaOperacao.value"
                :error="!!fields.codNaturezaOperacao.errorMessage"
                :error-message="fields.codNaturezaOperacao.errorMessage"
                :valid="fields.codNaturezaOperacao.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/natureza/operacao',
                    filterOr: ['controle', 'descricaooperacao'],
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                role-prefix="CADASTRO.NATUREZA-OPERACAO"
                class="tw-col-span-4"
                label="Natureza de operação"
                add-button
                edit-button
                required
              />

              <InputSelect
                v-model="fields.codCfop.value"
                :error="!!fields.codCfop.errorMessage"
                :error-message="fields.codCfop.errorMessage"
                :valid="fields.codCfop.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/cfop',
                    central: true,
                    filterOr: ['codcfop', 'descricao'],
                    filters: {
                      deleted_at: {
                        filterType: 'NULL'
                      }
                    },
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                class="tw-col-span-8"
                label="CFOP padrão"
                required
              />
            </SGCard>
            <SGCard
              id="sessao-pdv-nf-campos-alteraveis"
              title="Opções de digitalização e leitura"
              :cols="8"
              :schema-errors="errors"
              :meta="meta"
              remove-gap
              has-title-separator
              has-circle
            >
              <InputSelect
                v-model="fields.buscaPadrao.value"
                :error="!!fields.buscaPadrao.errorMessage"
                :error-message="fields.buscaPadrao.errorMessage"
                :valid="fields.buscaPadrao.meta.valid"
                v-bind="{
                  ...ModelInputSelect,
                  params: {
                    optionsStatic: TIPOS_BUSCA
                  }
                }"
                :clearable="false"
                option-label="descricao"
                option-value="controle"
                class="tw-col-span-8"
                label="Busca fixa padrão no PDV por"
                required
              />

              <div class="tw-col-span-8">
                <q-option-group
                  v-model="fields.opcoesLeitura.value"
                  :error="!!fields.opcoesLeitura.errorMessage"
                  :error-message="fields.opcoesLeitura.errorMessage"
                  :valid="fields.opcoesLeitura.meta.valid"
                  :options="opcoesLeitura"
                  type="radio"
                  size="2rem"
                  class="tw-flex tw-flex-col tw-gap-x-2 tw-gap-y-1"
                />
              </div>
            </SGCard>

            <EmailConfig
              v-model:fields="fields"
              v-model:errors="errors"
              v-model:values="values"
              :validate="validate"
              :show-default="true"
            />

            <SGCard
              title="E-mail para envio de xml"
              id="email-xml"
              :cols="12"
              :schema-errors="errors"
              :error-keys="['emailXml.emailsAdicionais']"
              remove-gap
              has-circle
              has-title-separator
            >
              <EmailXml
                ref="emailXmlRef"
                :fields="fields.emailXml"
                :errors="errors"
                :show-default="true"
              />
            </SGCard>

            <SGCard
              title="WhatsApp"
              id="whatsapp-config"
              :cols="12"
              :schema-errors="errors"
              remove-gap
              has-circle
              has-title-separator
            >
              <WhatsApp
                v-model:fields="fields"
                :errors="errors"
                :show-default="true"
                :config-type="2"
                :show-send-message-button="true"
              />
            </SGCard>
          </q-tab-panel>
        </q-tab-panels>
      </section>
    </SGRegisterPage>
  </div>
</template>

<script setup>
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import { useQuasar } from 'quasar';
import useRegister from 'src/components/actionBar/composables/useRegister';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import Logotipo from 'src/core/components/config/Logotipo.vue';
import Margins from 'src/core/components/config/Margins.vue';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import EmailConfig from 'src/core/components/SG/Email/EmailConfig.vue';
import EmailXml from 'src/core/components/SG/Email/EmailXml.vue';
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import SGRegisterSkeleton from 'src/core/components/SG/Skeleton/SGRegisterSkeleton.vue';
import ModelInputSelect from 'src/core/models/inputs/Select';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputText from 'src/core/models/inputs/Text';
import FastPrintSteps from 'src/modules/navbarUsuario/usuario/config/components/FastPrintSteps.vue';
import { useUserConfigStore } from 'src/modules/navbarUsuario/usuario/config/store/useUserConfigStore';
import { default as CostCenterRegisterModal } from 'src/modules/cadastros/centro-custo/views/register.vue';
import { default as RegisterNatureOperation } from 'src/modules/cadastros/naturezaDeOperacao/views/register.vue';
import { useFormSchemaConfig } from 'src/modules/vendas/pdv/models/useFormSchemaConfig';
import { useEmailConfigStore } from 'src/modules/vendas/pdv/store/useEmailConfigStore';
import { useEmailXmlConfigStore } from 'src/modules/vendas/pdv/store/useEmailXmlConfigStore';
import { usePdvConfigStore } from 'src/modules/vendas/pdv/store/usePdvConfigStore';
import { useGlobal } from 'src/stores/global';
import { ref, toRaw, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';

const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const { modal } = toRefs(props);
let editValues = false;
const global = useGlobal();
const $q = useQuasar();
const router = useRouter();
const registerPageRef = ref();
const isWarningAnimationDisabled = ref(true);
const emailXmlRef = ref();
const tab = ref('user');

// Stores
const userConfigStore = useUserConfigStore();
const emailXmlStore = useEmailXmlConfigStore();

const pdvConfigStore = usePdvConfigStore();
pdvConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  }
};

const emailConfigStore = useEmailConfigStore();
emailConfigStore.table.filters = {
  ...emailConfigStore.table.filters,
  deleted_at: {
    filterType: ''
  },
  tipo: {
    filterValue: 3,
    filterType: 'EQUALS'
  }
};

// Ref
const isLoadingPage = ref(true);
const costCenterInputRef = ref();
const costCenterRegisterModalModel = ref();
const natureOperationRef = ref();

const identificationsProduct = [
  { label: 'Código interno', value: '1' },
  { label: 'Código de barras', value: '2' }
];

const canhotoOptions = [
  { label: 'Incluir', value: true },
  { label: 'Não incluir', value: false }
];

const typesPrintManual = [
  { label: 'A4', value: '1' },
  { label: 'Térmica (80mm)', value: '3' },
  { label: 'Térmica (58mm)', value: '4' }
];

const typesPrintNFCe = [
  { label: 'A4', value: '1' },
  { label: 'Térmica (80mm)', value: '2' },
  { label: 'Térmica (58mm)', value: '3' }
];

const confissaoOptions = [
  { value: '1', label: 'Imprimir direto' },
  { value: '2', label: 'Perguntar se deseja imprimir' },
  { value: '3', label: 'Não imprimir' }
];

const TIPOS_BUSCA = [
  {
    descricao: 'Padrão (cód. produto ou cód. barras GTIN)',
    controle: 1
  },
  {
    descricao: 'Cód. produto ou cód. barras GTIN ou cód. barras interno',
    controle: 6
  },
  {
    descricao: 'Cód. produto ou cód. barras interno',
    controle: 7
  },
  {
    descricao: 'Código do produto',
    controle: 2
  },
  {
    descricao: 'Código de barras GTIN',
    controle: 3
  },
  {
    descricao: 'Código de barras interno',
    controle: 4
  },
  {
    descricao: 'Referência',
    controle: 5
  }
];

const opcoesLeitura = [
  {
    label: 'Não permitir alteração rápida',
    value: 1
  },
  {
    label:
      'Permitir alteração de valor unitário, valor total e calcular a quantidade.',
    value: 2
  },
  {
    label:
      'Permitir alteração de quantidade, valor total e calcular o valor unitário.',
    value: 3
  },
  {
    label:
      'Permitir alteração da quantidade, valor unitário e calcular o valor total.',
    value: 4
  }
];

async function getPdvConfig() {
  if (!global?.roles?.includes('VENDA.PDV:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  return pdvConfigStore.get();
}

async function getEmailConfig() {
  if (!global?.roles?.includes('VENDA.PDV:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  return emailConfigStore.get();
}

async function getConfig() {
  isLoadingPage.value = true;

  // Requisições em paralelo para buscar configuracoes (user, pdv e email)
  const [userConfigPromise, pdvConfigPromise, emailConfigPromise] =
    await Promise.all([
      userConfigStore.get({ deletedAt: false }),
      getPdvConfig(),
      getEmailConfig()
    ]);

  isLoadingPage.value = false;

  const { success: userSuccess, data: userConfigData } = userConfigPromise;
  const { success: pdvSuccess, data: pdvConfigData } = pdvConfigPromise;
  const { success: emailSuccess, data: emailConfigData } = emailConfigPromise;

  if (!pdvSuccess || !emailSuccess || !userSuccess) cancel();

  const { rowsData: userConfigs } = userConfigData;
  const userConfig = userConfigs?.[0] ?? {};
  const { impressaoRapida } = userConfig.nfce ?? {};

  const { rowsData: emailConfigs } = emailConfigData;
  const { configEmail, configEmailXml } = emailConfigs?.[0] ?? {};

  const { rowsData: configs } = pdvConfigData ?? {};
  const config = configs?.[0] ?? {};

  editValues = {
    ...config,
    email: configEmail,
    emailXml: configEmailXml,
    usuario: {
      ...userConfig,
      nfce: {
        ...userConfig.nfce,
        impressaoRapida: {
          ...impressaoRapida,
          modulo: 1,
          habilitarImpressaoRapida:
            impressaoRapida?.habilitarImpressaoRapida ?? false,
          usarPadraoUsuario: impressaoRapida?.usarPadraoUsuario ?? true
        }
      }
    }
  };
}

await getConfig();
const {
  fields,
  values,
  initialValues,
  setFieldValue,
  errors,
  meta,
  isSubmitting,
  validate,
  handleReset
} = useFormSchemaConfig(editValues);

async function tabChange() {
  setTimeout(() => {
    registerPageRef.value.reMountCards('.q-tab-panel');
  }, 255);
}

function updateCostCenterOptions() {
  costCenterRegisterModalModel.value = false;
  costCenterInputRef.value?.reMount();
}

function handleEnableFastPrint(enableFastPrint) {
  if (!enableFastPrint) {
    setFieldValue('usuario.nfce.impressaoRapida.usarPadraoUsuario', true);
  }
}

function openRegisterNature(controle = null) {
  let props = {};

  if (controle) {
    props = {
      data: {
        isEditing: true,
        controle
      },
      modal: true
    };
  }

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterNatureOperation,
      scope: 'register-nature',
      ...props
    }
  }).onOk(async () => {
    await natureOperationRef.value?.reMount();
  });
}

function resetForm() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    emailXmlRef.value?.clearField();
    handleReset();
  });
}

function cancel() {
  isWarningAnimationDisabled.value = true;

  if (modal.value) emit('cancel');
  else router.push('/vendas/pdv-listagem');
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

async function save() {
  if (!(await isFormValid())) return;

  let payload = toRaw(values);

  const emailPayload = payload.email;
  const xmlEmailPayload = payload.emailXml;
  const userPayload = {
    ...payload.usuario,
    impressaoRapida: {
      ...payload.usuario?.nfce?.impressaoRapida
    }
  };

  delete payload.usuario;
  delete payload.email;
  delete userPayload?.codUsuario;

  // Verifica se já existe um registro de configurações salva, se sim, atualiza, caso contrário, cria.
  const [pdvPromise, userPromise, emailPromise, emailXmlPromise] =
    await Promise.all([
      sendPdvConfigRequest(initialValues.value, payload),
      sendUserConfigRequest(userPayload),
      sendEmailConfigRequest(emailPayload),
      sendEmailXmlConfigRequest(xmlEmailPayload)
    ]);

  if (
    pdvPromise?.success &&
    userPromise?.success &&
    emailPromise?.success &&
    emailXmlPromise?.success
  ) {
    notify('Dados salvos com sucesso!', 'positive');
    emit('ok');
    isWarningAnimationDisabled.value = true;
    router.push('/vendas/pdv-listagem');
  }
}

async function sendPdvConfigRequest(initialValues, payload) {
  if (!global?.roles?.includes('VENDA.PDV:CONFIGURACAO'))
    return { success: true };

  if (initialValues.controle) {
    if (!payload.senhaSupervisor) delete payload.senhaSupervisor;
    return pdvConfigStore.put({
      payload,
      controle: payload.controle,
      silence: true
    });
  } else {
    return pdvConfigStore.post({ payload, silence: true });
  }
}

async function sendUserConfigRequest(userPayload) {
  if (userPayload?.createdAt) {
    return userConfigStore.put({
      payload: userPayload,
      silence: true
    });
  }
  return userConfigStore.post({
    payload: userPayload,
    silence: true
  });
}

async function sendEmailConfigRequest(emailPayload) {
  if (!global?.roles?.includes('VENDA.PDV:CONFIGURACAO'))
    return { success: true };
  return await emailConfigStore.put({
    payload: { configEmail: emailPayload },
    silence: true
  });
}

async function sendEmailXmlConfigRequest(xmlEmailPayload) {
  if (!global?.roles?.includes('VENDA.PDV:CONFIGURACAO'))
    return { success: true };
  return await emailXmlStore.put({
    payload: { configEmailXml: xmlEmailPayload },
    silence: true
  });
}

// Desfazer alterações.
watch(
  values,
  async () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);

// Objeto que mapeia os eventos do actionBar aos seus respectivos callbacks.
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: resetForm
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});
</script>
