import { toTypedSchema } from '@vee-validate/yup';
import { userTemplate } from 'src/components/ConfigEmpresa/Email/HtmlTemplates/userTemplate';
import { useField, useFieldArray, useForm } from 'vee-validate';
import { ref } from 'vue';
import { array, boolean, number, object, string } from 'yup';
import { emailRegex } from 'src/components/utils/emailRegex';

/**
 * Retorna os objetos de formulários vinculados com suas configurações de validação.
 * @returns {Object} Um objeto contendo os formulários vinculados e funções auxiliares.
 */
export function useFormSchemaConfig(initialValues = false) {
  
  const schema = object({
    padraoWhatsApp: boolean().default(false).nullable(),
    usuario: object({
      createdAt: string().default('').nullable(),
      mdfe: object({
        logotipoPadrao: boolean().default(true).nullable(),
        logotipoImprimir: boolean().default(true).nullable(),

        margemBaixoA4: number().default(0).nullable(),
        margemEsquerdaA4: number().default(0).nullable(),
        margemDireitaA4: number().default(0).nullable(),
        margemTopoA4: number().default(0).nullable(),
        margemPadraoA4: boolean().default(true).nullable(),

        impressaoRapida: object({
          modulo: number().default(7),
          habilitarImpressaoRapida: boolean().default(false),
          usarPadraoUsuario: boolean().default(true),
          ipConexaoRede: string()
            .default('')
            .nullable()
            .transform((value) => (value === null ? '' : value)),
          impressora: string()
            .default('')
            .transform((value) => (value === null ? '' : value))
            .when(
              ['usarPadraoUsuario', 'habilitarImpressaoRapida'],
              ([usarPadraoUsuario, habilitarImpressaoRapida], schema) => {
                return !usarPadraoUsuario && habilitarImpressaoRapida
                  ? schema.required('Obrigatório selecionar uma impressora')
                  : schema.nullable();
              }
            )
        })
      })
    }),
    email: object({
      padrao: boolean().default(true).nullable(),
      tipo: number().default(5).nullable(),
      enviaEmailEmissao: boolean()
        .default(false)
        .required('O campo é obrigatório.'),
      smtpServidor: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpPorta: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpUsuario: string()
        .default('')
        .test((value, ctx) => {
          const { padrao } = ctx.parent;
          if (!padrao && !value) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }
          if (value && value.includes('@') ? !emailRegex.test(value) : false) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpEmail: string()
        .default('')
        .test((value, ctx) => {
          const { smtpUsuario } = ctx.parent;
          if (smtpUsuario && !emailRegex.test(smtpUsuario) && !value) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }
          if (value && !emailRegex.test(value)) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpSenha: string()
        .default('')
        .when('padrao', {
          is: false,
          then: (schema) => schema.required('O campo é obrigatório.'),
          otherwise: (schema) => schema.nullable()
        }),
      smtpSSL: boolean().default(false).required('O campo é obrigatório.'),
      smtpAutoTls: boolean().default(true).required('O campo é obrigatório.'),
      assuntoEmail: string().default('').nullable(),
      descricaoRemetente: string().default('').nullable(),
      arquivosPartes: boolean().default(false).nullable(),
      tamanhoArquivo: string().default('15').nullable().test((value, ctx) => {
        return (value > 0 && !ctx.parent.padrao) || ctx.parent.padrao || (!ctx.parent.padrao && !ctx.parent.arquivosPartes) || (ctx.parent.arquivosPartes && ctx.createError({ message: 'Minimo de 1 MB' }));
      }),
      emailsAdicionais: array().default([]).nullable(),
      footerHtml: string().default(userTemplate).nullable()
    })
  });

  // Configura os valores do formulário e suas validações.
  const form = useForm({
    validationSchema: toTypedSchema(schema),
    validateOnMount: false,
    initialValues: initialValues
      ? Object.keys(initialValues).length === 1
        ? initialValues
        : schema
            .json()
            .cast(initialValues, { assert: false, stripUnknown: true })
      : undefined,
    initialErrors: {}
  });

  // Função recursiva para criar campos dinamicamente
  function createFields(schema, parentKey = '') {
    const fields = {};
    Object.keys(schema.fields).forEach((key) => {
      const fieldKey = parentKey ? `${parentKey}.${key}` : key;
      const fieldSchema = schema.fields[key];
      if (fieldSchema.type === 'object') {
        fields[key] = createFields(fieldSchema, fieldKey);
      } else if (fieldSchema.type === 'array' && fieldSchema.schema) {
        fields[key] = useFieldArray(fieldKey);
      } else {
        fields[key] = useField(fieldKey);
      }
    });
    return fields;
  }

  const fields = ref(createFields(schema));
  return { ...form, fields, useField };
}
