<template>
  <SGRegisterSkeleton v-if="isLoadingPage" />
  <NewSGRegisterPage
    v-else
    id="sessao-emissao-pdv"
    ref="registerPageRef"
    title="Configurações da MDF-e"
    action-text="Gostaria de salvar?"
    :buttons="actionBarButtons"
    :modal="modal"
    :cols="8"
    hide-side-nav
  >
    <section class="tw-col-span-full">
      <q-tabs
        v-model="tab"
        dense
        class="tw-col-span-full tw-rounded-t-md tw-text-textsSGBR-gray"
        align="left"
        narrow-indicator
        active-color="primary"
        indicator-color="primary"
      >
        <q-tab
          name="user"
          label="Usuário"
          class="tw-p-0 tw-text-sm"
          content-class="tw-py-0 tw-px-4"
        />
        <q-tab
          name="company"
          label="Empresa"
          class="tw-p-0 tw-text-sm"
          content-class="tw-py-0 tw-px-4"
          :disable="!global?.roles?.includes('VENDA.MDFE:CONFIGURACAO')"
        >
          <TooltipCustom
            v-if="!global?.roles?.includes('VENDA.MDFE:CONFIGURACAO')"
            text-tooltip="Você não possui permissão para alterar as
            configurações da empresa"
          />
        </q-tab>
      </q-tabs>
      <q-separator class="tw-col-span-full tw-mb-4" />

      <q-tab-panels
        v-model="tab"
        animated
        class="tw-col-span-full tw-bg-inherit"
        transition-prev="jump-right"
        transition-next="jump-left"
        :transition-duration="120"
        @transition="tabChange"
      >
        <q-tab-panel
          name="user"
          class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
        >
          <NewSGCard
            id="configuracao-impressao"
            title="Configurações do usuário"
            class="tw-mb-2"
            has-title-separator
            :cols="8"
          >
            <Logotipo
              v-model:fields="fields.usuario.mdfe"
              :has-default="true"
              class="tw-col-span-full"
            />

            <div
              class="tw-col-span-full tw-mt-4 tw-flex tw-flex-wrap tw-gap-customGridGapCards"
            >
              <Margins
                title="Margens impressão (A4)"
                v-model:fields="fields.usuario.mdfe"
                :has-margin-default="true"
                type="A4"
              />
            </div>
          </NewSGCard>

          <NewSGCard
            title="Impressão rápida"
            id="fast-print-integration-mdfe"
            :meta="meta"
            :error-keys="['usuario.mdfe.impressaoRapida.impressora']"
            container-class="tw-pb-4"
            :schema-errors="errors"
            :cols="8"
            remove-gap
            has-circle
            has-title-separator
          >
            <q-checkbox
              @update:model-value="handleEnableFastPrint"
              v-model="
                fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                  .value
              "
              :error="
                !!fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                  .errorMessage
              "
              :error-message="
                fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                  .errorMessage
              "
              :valid="
                fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                  .meta.valid
              "
              size="2rem"
              label="Habilitar impressão rápida"
              class="tw-col-span-full"
            />

            <q-checkbox
              v-model="
                fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario.value
              "
              :error="
                !!fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario
                  .errorMessage
              "
              :error-message="
                fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario
                  .errorMessage
              "
              :valid="
                fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario.meta.valid
              "
              :disable="
                !fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                  .value
              "
              size="2rem"
              label="Usar impressão rápida padrão do usuário"
              class="tw-col-span-full tw-mb-4"
            />

            <div
              class="tw-col-span-full tw-flex tw-flex-col tw-gap-4"
              :class="{
                'tw-pointer-events-none tw-opacity-40':
                  !fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                    .value ||
                  fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario.value
              }"
            >
              <FastPrintSteps
                v-model:fields="fields.usuario.mdfe.impressaoRapida"
                :is-fast-print-enabled="
                  fields.usuario.mdfe.impressaoRapida.habilitarImpressaoRapida
                    .value &&
                  !fields.usuario.mdfe.impressaoRapida.usarPadraoUsuario.value
                "
              />
            </div>
          </NewSGCard>
        </q-tab-panel>

        <q-tab-panel
          name="company"
          class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
        >
          <EmailConfig
            show-default
            v-model:fields="fields"
            v-model:errors="errors"
            v-model:values="values"
            :validate="validate"
          />

          <NewSGCard
            title="WhatsApp"
            id="whatsapp-config"
            :cols="12"
            :schema-errors="errors"
            remove-gap
            has-circle
            has-title-separator
          >
            <WhatsApp
              v-model:fields="fields"
              :errors="errors"
              :show-default="true"
              :config-type="6"
              :show-send-message-button="true"
            />
          </NewSGCard>
        </q-tab-panel>
      </q-tab-panels>
    </section>
  </NewSGRegisterPage>
</template>

<script setup>
import { useQuasar } from 'quasar';
import useRegister from 'src/components/actionBar/composables/useRegister';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import NewSGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import SGRegisterSkeleton from 'src/core/components/SG/Skeleton/SGRegisterSkeleton.vue';
import EmailConfig from 'src/core/components/SG/Email/EmailConfig.vue';
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';
import { useFormSchemaConfig } from 'src/modules/vendas/mdfe/models/useFormSchemaConfig';
import { useEmailConfigStore } from 'src/modules/vendas/pdv/store/useEmailConfigStore';
import { useUserConfigStore } from 'src/modules/navbarUsuario/usuario/config/store/useUserConfigStore';
import { ref, toRaw, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import FastPrintSteps from 'src/modules/navbarUsuario/usuario/config/components/FastPrintSteps.vue';
import { useGlobal } from 'src/stores/global';
import Margins from 'src/core/components/config/Margins.vue';
import Logotipo from 'src/core/components/config/Logotipo.vue';

const userConfigStore = useUserConfigStore();
const emailConfigStore = useEmailConfigStore();

const global = useGlobal();

const isLoadingPage = ref(true);

const tab = ref('user');

emailConfigStore.table.filters = {
  ...emailConfigStore.table.filters,
  deleted_at: {
    filterType: ''
  },
  tipo: {
    filterValue: 5,
    filterType: 'EQUALS'
  }
};

// Props do componente.
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

// Emits do componente.
const emit = defineEmits(['cancel', 'ok']);
const { modal } = toRefs(props);

// Quasar
const $q = useQuasar();
const router = useRouter();

let editValues = false;

// Refs
const isWarningAnimationDisabled = ref(true);

// Requisição das configurações.
async function getConfig() {
  isLoadingPage.value = true;

  // Requisições em paralelo para buscar configuracoes (user e email)
  const [userConfigPromise, emailConfigPromise] = await Promise.all([
    getUserConfig(),
    getEmailConfig()
  ]);

  isLoadingPage.value = false;

  const { success: emailSuccess, data: emailData } = emailConfigPromise;
  const { success: userSuccess, data: userData } = userConfigPromise;

  if (!emailSuccess || !userSuccess) cancel();

  const { rowsData: userConfigs } = userData;
  const userConfig = userConfigs?.[0] ?? {};
  const { impressaoRapida } = userConfig.mdfe ?? {};

  const { rowsData: emailConfigs } = emailData;
  const { configEmail } = emailConfigs?.[0] ?? {};

  editValues = {
    usuario: {
      ...userConfig,
      mdfe: {
        ...userConfig.mdfe,
        impressaoRapida: {
          ...impressaoRapida,
          modulo: 7,
          habilitarImpressaoRapida:
            impressaoRapida?.habilitarImpressaoRapida ?? false,
          usarPadraoUsuario: impressaoRapida?.usarPadraoUsuario ?? true
        }
      }
    },
    email: {
      ...configEmail,
      emailsAdicionais:
        configEmail?.emailsAdicionais?.length > 0
          ? configEmail?.emailsAdicionais
          : [{ emailAdicional: '' }]
    }
  };
}

function handleEnableFastPrint(enableFastPrint) {
  if (!enableFastPrint) {
    setFieldValue('usuario.mdfe.impressaoRapida.usarPadraoUsuario', true);
  }
}

async function getEmailConfig() {
  if (!global?.roles?.includes('VENDA.MDFE:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  return emailConfigStore.get();
}

async function getUserConfig() {
  return userConfigStore.get({ deletedAt: false });
}

await getConfig();

const {
  values,
  fields,
  setFieldValue,
  resetForm,
  errors,
  validate,
  initialValues,
  isSubmitting
} = useFormSchemaConfig(editValues);

/**
 * Reinicia o formulário para os valores iniciais.
 * Esta função exibe um modal de confirmação para desfazer as alterações feitas no formulário e, se confirmado, restaura para os valores iniciais.
 * @returns {void}
 */
function reset() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(async () => {
    resetForm();
  });
}

const registerPageRef = ref();

async function tabChange() {
  setTimeout(() => {
    registerPageRef.value.reMountCards('.q-tab-panel');
  }, 255);
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

/**
 * Cancela a operação atual de cadastro, emitindo um evento de cancelamento se estiver em um modal, caso contrário, redireciona para a página '/produto'.
 * @returns {void}
 */
function cancel() {
  if (modal.value) emit('cancel');
  else router.push('/vendas/mdfe');
}

// ------------------------------------------------------------------------------------

/**
 * Salva os dados do formulário.
 * @async
 * @returns {Promise<void>} - Uma promessa que é resolvida após a conclusão do processo de salvamento.
 */
async function save() {
  if (!(await isFormValid())) return;

  let payload = toRaw(values);

  const emailPayload = payload.email;
  const userPayload = {
    ...payload.usuario,
    impressaoRapida: {
      ...payload.usuario?.mdfe?.impressaoRapida
    }
  };

  // VALIDACAO PARA EMAIL ADICIONAL, IMPORTANTE!!!
  emailPayload.emailsAdicionais = payload?.email?.emailsAdicionais?.filter(
    (el) => el.emailAdicional != ''
  );

  delete payload.usuario;
  delete payload.email;
  delete userPayload?.codUsuario;

  const [userPromise, emailPromise] = await Promise.all([
    sendUserConfigRequest(userPayload),
    sendEmailConfigRequest(emailPayload)
  ]);

  if (userPromise?.success && emailPromise?.success) {
    notify('Dados salvos com sucesso!', 'positive');
    cancel();
  }
}

async function sendUserConfigRequest(userPayload) {
  if (userPayload?.createdAt) {
    return userConfigStore.put({
      payload: userPayload,
      silence: true
    });
  }
  return userConfigStore.post({
    payload: userPayload,
    silence: true
  });
}

async function sendEmailConfigRequest(emailPayload) {
  if (!global?.roles?.includes('VENDA.MDFE:CONFIGURACAO'))
    return { success: true };
  return await emailConfigStore.put({
    payload: { configEmail: emailPayload },
    silence: true
  });
}

// Objeto que mapeia os eventos do actionBar aos seus respectivos callbacks.
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});

watch(
  values,
  () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
