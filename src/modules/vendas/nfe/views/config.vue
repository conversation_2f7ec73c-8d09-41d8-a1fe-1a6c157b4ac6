<template>
  <SGRegisterSkeleton v-if="isLoadingPage" />

  <SGRegisterPage
    v-else
    ref="registerPageRef"
    id="nfe-config"
    title="Configurações NF-e"
    :buttons="actionBarButtons"
    :modal="modal"
    :disable-warning-animation="isWarningAnimationDisabled"
    hide-side-nav
  >
    <section class="tw-col-span-full">
      <q-tabs
        v-model="tab"
        dense
        class="tw-col-span-full tw-rounded-t-md tw-text-textsSGBR-gray"
        align="left"
        narrow-indicator
        active-color="primary"
        indicator-color="primary"
      >
        <q-tab
          name="user"
          label="Usuário"
          class="tw-p-0 tw-text-sm"
          content-class="tw-py-0 tw-px-4"
        />
        <q-tab
          name="company"
          label="Empresa"
          class="tw-p-0 tw-text-sm"
          content-class="tw-py-0 tw-px-4"
          :disable="!global?.roles?.includes('VENDA.NFE:CONFIGURACAO')"
        >
          <TooltipCustom
            v-if="!global?.roles?.includes('VENDA.NFE:CONFIGURACAO')"
            text-tooltip="Você não possui permissão para alterar as
            configurações da empresa"
          />
        </q-tab>
      </q-tabs>

      <q-separator class="tw-col-span-full tw-mb-4" />

      <q-tab-panels
        v-model="tab"
        animated
        class="tw-col-span-full tw-bg-inherit"
        transition-prev="jump-right"
        transition-next="jump-left"
        :transition-duration="120"
        @transition="tabChange"
      >
        <q-tab-panel
          name="user"
          class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
        >
          <SGCard
            id="user-print-config"
            title="Configurações do usuário"
            :cols="8"
            :schema-errors="errors"
            :meta="meta"
            remove-gap
            has-title-separator
            has-circle
          >
            <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
              <p class="tw-text-textsSGBR-gray">Tipo impressão</p>
              <q-option-group
                v-model="fields.usuario.nfe.tipoImpressao.value"
                :error="!!fields.usuario.nfe.tipoImpressao.errorMessage"
                :error-message="fields.usuario.nfe.tipoImpressao.errorMessage"
                :valid="fields.usuario.nfe.tipoImpressao.meta.valid"
                :options="PRINT_FORMATS"
                color="primary"
                size="30px"
                inline
                class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
              />
            </div>

            <Logotipo
              v-model:fields="fields.usuario.nfe"
              :has-default="true"
              class="tw-col-span-full"
            />

            <div
              class="tw-col-span-full tw-flex tw-flex-wrap tw-gap-customGridGapCards"
            >
              <Margins
                title="Margens impressão (A4)"
                v-model:fields="fields.usuario.nfe"
                :has-margin-default="true"
                type="A4"
              />

              <Margins
                title="Margens impressão térmica (80mm)"
                v-model:fields="fields.usuario.nfe"
                :has-margin-default="true"
              />

              <Margins
                title="Margens impressão térmica (58mm)"
                v-model:fields="fields.usuario.nfe"
                :has-margin-default="true"
                type="58"
              />
            </div>
          </SGCard>
          <SGCard
            title="Impressão rápida"
            id="fast-print-integration-nfe"
            :schema-errors="errors"
            :error-keys="['usuario.nfe.impressaoRapida.impressora']"
            :meta="meta"
            :cols="8"
            remove-gap
            has-circle
            has-title-separator
          >
            <q-checkbox
              @update:model-value="handleEnableFastPrint"
              v-model="
                fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                  .value
              "
              :error="
                !!fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                  .errorMessage
              "
              :error-message="
                fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                  .errorMessage
              "
              :valid="
                fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida.meta
                  .valid
              "
              size="2rem"
              label="Habilitar impressão rápida"
              class="tw-col-span-full"
            />

            <q-checkbox
              v-model="
                fields.usuario.nfe.impressaoRapida.usarPadraoUsuario.value
              "
              :error="
                !!fields.usuario.nfe.impressaoRapida.usarPadraoUsuario
                  .errorMessage
              "
              :error-message="
                fields.usuario.nfe.impressaoRapida.usarPadraoUsuario
                  .errorMessage
              "
              :valid="
                fields.usuario.nfe.impressaoRapida.usarPadraoUsuario.meta.valid
              "
              :disable="
                !fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                  .value
              "
              size="2rem"
              label="Usar impressão rápida padrão do usuário"
              class="tw-col-span-full tw-mb-4"
            />

            <div
              class="tw-col-span-full tw-flex tw-flex-col tw-gap-4"
              :class="{
                'tw-pointer-events-none tw-opacity-40':
                  !fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                    .value ||
                  fields.usuario.nfe.impressaoRapida.usarPadraoUsuario.value
              }"
            >
              <FastPrintSteps
                v-model:fields="fields.usuario.nfe.impressaoRapida"
                :is-fast-print-enabled="
                  fields.usuario.nfe.impressaoRapida.habilitarImpressaoRapida
                    .value &&
                  !fields.usuario.nfe.impressaoRapida.usarPadraoUsuario.value
                "
              />
            </div>
          </SGCard>
        </q-tab-panel>

        <q-tab-panel
          name="company"
          class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
        >
          <SGCard
            title="Configurações gerais"
            :schema-errors="errors"
            :meta="meta"
            :cols="8"
            id="default-search"
            has-title-separator
            has-circle
            container-class="tw-gap-y-1"
            remove-gap
          >
            <q-checkbox
              v-model="fields.mostrarFaturaNaDanfe.value"
              :error="!!fields.mostrarFaturaNaDanfe.errorMessage"
              :error-message="fields.mostrarFaturaNaDanfe.errorMessage"
              :valid="fields.mostrarFaturaNaDanfe.meta.valid"
              label="Informar parcelamento da venda na impressão"
              class="tw-col-span-4"
              size="30px"
            />

            <q-checkbox
              v-model="fields.bloquearVendaEstoqueNegativoOuZerado.value"
              :error="
                !!fields.bloquearVendaEstoqueNegativoOuZerado.errorMessage
              "
              :error-message="
                fields.bloquearVendaEstoqueNegativoOuZerado.errorMessage
              "
              :valid="fields.bloquearVendaEstoqueNegativoOuZerado.meta.valid"
              class="tw-col-span-4"
              size="30px"
              label="Bloquear venda com estoque negativo ou zerado"
            />

            <InputSelect
              v-model="fields.buscaPadrao.value"
              :error="!!fields.buscaPadrao.errorMessage"
              :error-message="fields.buscaPadrao.errorMessage"
              :valid="fields.buscaPadrao.meta.valid"
              v-bind="{
                ...ModelInputSelect,
                params: {
                  optionsStatic: TIPOS_BUSCA_PADRAO
                }
              }"
              :clearable="false"
              option-label="descricao"
              option-value="controle"
              class="tw-col-span-4 tw-mt-3"
              label="Busca fixa padrão na NF-e por"
              required
            />
          </SGCard>

          <EmailConfig
            v-model:fields="fields"
            v-model:errors="errors"
            v-model:values="values"
            :validate="validate"
            :show-default="true"
          />

          <SGCard
            title="E-mail para envio de xml"
            id="email-xml"
            :cols="12"
            :schema-errors="errors"
            :error-keys="['emailXml.emailsAdicionais']"
            remove-gap
            has-circle
            has-title-separator
          >
            <EmailXml
              ref="emailXmlRef"
              :fields="fields.emailXml"
              :errors="errors"
              :show-default="true"
            />
          </SGCard>
          <SGCard
            title="WhatsApp"
            id="whatsapp"
            :cols="12"
            :schema-errors="errors"
            :error-keys="['whatsapp']"
            remove-gap
            has-circle
            has-title-separator
          >
            <WhatsApp
              ref="whatsappRef"
              :fields="fields.whatsapp"
              :errors="errors"
              :show-default="true"
              :show-only-send-button="true"
              :show-only-disconnect-button="true"
              :always-enable-connect="false"
              :show-send-message-button="true"
              :config-type="2"
            />
          </SGCard>
        </q-tab-panel>
      </q-tab-panels>
    </section>
  </SGRegisterPage>
</template>

<script setup>
import { useQuasar } from 'quasar';
import useRegister from 'src/components/actionBar/composables/useRegister';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import EmailConfig from 'src/core/components/SG/Email/EmailConfig.vue';
import EmailXml from 'src/core/components/SG/Email/EmailXml.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import Logotipo from 'src/core/components/config/Logotipo.vue';
import Margins from 'src/core/components/config/Margins.vue';
import ModelInputSelect from 'src/core/models/inputs/Select';
import FastPrintSteps from 'src/modules/navbarUsuario/usuario/config/components/FastPrintSteps.vue';
import { useUserConfigStore } from 'src/modules/navbarUsuario/usuario/config/store/useUserConfigStore';
import { useFormSchemaConfig } from 'src/modules/vendas/nfe/models/useFormSchemaConfig';
import { useNfeConfigStore } from 'src/modules/vendas/nfe/store/useNfeConfigStore';
import { useEmailConfigStore } from 'src/modules/vendas/pdv/store/useEmailConfigStore';
import { useEmailXmlConfigStore } from 'src/modules/vendas/pdv/store/useEmailXmlConfigStore';
import { useGlobal } from 'src/stores/global';
import { ref, toRaw, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';

// Props do componente.
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

// Emits do componente.
const emit = defineEmits(['cancel', 'ok']);

const $q = useQuasar();
const router = useRouter();
const global = useGlobal();
const { modal } = toRefs(props);

let editValues = false;
const registerPageRef = ref();
const tab = ref('user');
const emailXmlRef = ref(null);
const isWarningAnimationDisabled = ref(true);
const isLoadingPage = ref(true);

// Stores
const userConfigStore = useUserConfigStore();
const nfeConfigStore = useNfeConfigStore();
const emailConfigStore = useEmailConfigStore();
const emailXmlStore = useEmailXmlConfigStore();

nfeConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  }
};

emailConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  },
  tipo: {
    filterValue: 4,
    filterType: 'EQUALS'
  }
};

async function getEmailConfig() {
  if (!global?.roles?.includes('VENDA.NFE:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  return emailConfigStore.get();
}

async function getNfeConfig() {
  if (!global?.roles?.includes('VENDA.NFE:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  return nfeConfigStore.get();
}

function handleEnableFastPrint(enableFastPrint) {
  if (!enableFastPrint) {
    setFieldValue('usuario.nfe.impressaoRapida.usarPadraoUsuario', true);
  }
}
const PRINT_FORMATS = [
  {
    label: 'DANFE Retrato (A4)',
    value: '1'
  },
  {
    label: 'DANFE Paisagem (A4)',
    value: '2'
  },
  {
    label: 'DANFE Simplificado (80mm)',
    value: '3'
  },
  {
    label: 'DANFE Simplificado (58mm)',
    value: '4'
  }
];

const TIPOS_BUSCA_PADRAO = [
  {
    descricao: 'Padrão (cód. produto ou cód. barras GTIN)',
    controle: '1'
  },
  {
    descricao: 'Cód. produto ou cód. barras GTIN ou cód. barras interno',
    controle: '6'
  },
  {
    descricao: 'Cód. produto ou cód. barras interno',
    controle: '7'
  },
  {
    descricao: 'Código do produto',
    controle: '2'
  },
  {
    descricao: 'Código de barras GTIN',
    controle: '3'
  },
  {
    descricao: 'Código de barras interno',
    controle: '4'
  },
  {
    descricao: 'Referência',
    controle: '5'
  }
];

async function getConfig() {
  isLoadingPage.value = true;

  // Requisições em paralelo para buscar configuracoes (user, pdv e email)
  const [userConfigPromise, nfeConfigPromise, emailConfigPromise] =
    await Promise.all([
      userConfigStore.get({ deletedAt: false }),
      getNfeConfig(),
      getEmailConfig()
    ]);

  isLoadingPage.value = false;

  const { success: userSuccess, data: userConfigData } = userConfigPromise;
  const { success: nfeSuccess, data: nfeConfigData } = nfeConfigPromise;
  const { success: emailSuccess, data: emailConfigData } = emailConfigPromise;

  if (!nfeSuccess || !emailSuccess || !userSuccess) cancel();

  const { rowsData: userConfigs } = userConfigData;
  const userConfig = userConfigs?.[0] ?? {};

  const { rowsData: emailConfigs } = emailConfigData;
  const { configEmail, configEmailXml } = emailConfigs?.[0] ?? {};

  const { rowsData: configs } = nfeConfigData ?? {};
  const config = configs?.[0] ?? {};
  const { impressaoRapida } = userConfigs?.[0]?.nfe ?? {};

  editValues = {
    ...config,
    email: configEmail,
    emailXml: configEmailXml,
    usuario: {
      ...userConfig,
      nfe: {
        ...userConfig.nfe,
        impressaoRapida: {
          ...impressaoRapida,
          modulo: 2,
          habilitarImpressaoRapida:
            impressaoRapida?.habilitarImpressaoRapida ?? false,
          usarPadraoUsuario: impressaoRapida?.usarPadraoUsuario ?? true
        }
      }
    }
  };
}

async function tabChange() {
  setTimeout(() => {
    registerPageRef.value.reMountCards('.q-tab-panel');
  }, 255);
}

function resetForm() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    emailXmlRef.value?.clearField();
    handleReset();
  });
}

function cancel() {
  isWarningAnimationDisabled.value = true;

  if (modal.value) emit('cancel');
  else router.push('/vendas/nf');
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

async function sendNfeConfigRequest(initialValues, payload) {
  if (!global?.roles?.includes('VENDA.NFE:CONFIGURACAO'))
    return { success: true };

  if (initialValues.controle) {
    return nfeConfigStore.put({
      payload,
      controle: payload.controle,
      silence: true
    });
  } else {
    return nfeConfigStore.post({ payload, silence: true });
  }
}

async function sendUserConfigRequest(userPayload) {
  if (userPayload?.createdAt) {
    return userConfigStore.put({
      payload: userPayload,
      silence: true
    });
  }
  return userConfigStore.post({
    payload: userPayload,
    silence: true
  });
}

async function sendEmailConfigRequest(emailPayload) {
  if (!global?.roles?.includes('VENDA.NFE:CONFIGURACAO'))
    return { success: true };
  return await emailConfigStore.put({
    payload: { configEmail: emailPayload },
    silence: true
  });
}

async function sendEmailXmlConfigRequest(xmlEmailPayload) {
  if (!global?.roles?.includes('VENDA.NFE:CONFIGURACAO'))
    return { success: true };
  return await emailXmlStore.put({
    payload: { configEmailXml: xmlEmailPayload },
    silence: true
  });
}

async function save() {
  if (!(await isFormValid())) return;

  let payload = toRaw(values);

  const emailPayload = payload.email;
  const xmlEmailPayload = payload.emailXml;
  const userPayload = {
    ...payload?.usuario,
    impressaoRapida: payload?.usuario?.nfe?.impressaoRapida
  };

  delete payload.email;
  delete payload.emailXml;
  delete payload.usuario;

  const [nfePromise, userPromise, emailPromise, emailXmlPromise] =
    await Promise.all([
      sendNfeConfigRequest(initialValues.value, payload),
      sendUserConfigRequest(userPayload),
      sendEmailConfigRequest(emailPayload),
      sendEmailXmlConfigRequest(xmlEmailPayload)
    ]);

  // Se a operação foi bem-sucedida, desabilita a animação de aviso e redireciona conforme necessário
  if (
    nfePromise?.success &&
    userPromise?.success &&
    emailPromise?.success &&
    emailXmlPromise?.success
  ) {
    notify('Dados salvos com sucesso!', 'positive');
    cancel();
  }
}

// Objeto que mapeia os eventos do actionBar aos seus respectivos callbacks.
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: resetForm
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});

await getConfig();
const {
  fields,
  values,
  initialValues,
  setFieldValue,
  errors,
  meta,
  isSubmitting,
  validate,
  handleReset
} = useFormSchemaConfig(editValues);

// Desfazer alterações.
watch(
  values,
  async () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
