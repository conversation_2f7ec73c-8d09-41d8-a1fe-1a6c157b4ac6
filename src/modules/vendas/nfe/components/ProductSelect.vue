<template>
  <q-select
    ref="selectRef"
    outlined
    dense
    stack-label
    use-input
    hide-selected
    fill-input
    hide-bottom-space
    label-slot
    input-debounce="200"
    class="no-label"
    input-class="inputSelect tw-text-[18px] tw-text-right tw-pt-1"
    :loading="loading?.table"
    :placeholder="props.placeholder"
    v-model="model"
    :readonly="disabled"
    :options="filtered_options"
    :option-label="props.storeKey"
    :popup-content-style="[
      filtered_options.length > 9 ? 'height: 300px; width: 50px' : ''
    ]"
    option-value="controle"
    :required="!disabled && props.required"
    virtual-scroll-sticky-size-end="40"
    hide-dropdown-icon
    @input-value="filterFn"
    @popup-show="handleShowPopup"
    @keydown="keydownBlock"
    @keydown.enter="
      (ev) => {
        ev.preventDefault();
        if (!isRequesting) {
          isRequesting = true;
          handleInputEnter(ev.target.value);
        }
      }
    "
    @update:model-value="handleNewOptionSelected"
    @virtual-scroll="(data) => updateScroll(data, 0)"
  >
    <TooltipCustom v-if="props.tooltip" :text-tooltip="props.tooltip" />
    <template #before>
      <slot name="buttons"></slot>
      <q-btn
        v-if="!disabled"
        icon="format_list_bulleted"
        color="primary"
        size="17px"
        dense
        unelevated
        flat
        padding="2px 2px"
        class="tw-relative tw-mt-2 tw-w-full tw-grow tw-justify-center tw-rounded-md tw-leading-5 max-md:tw-bg-SGBRBlueLighten max-md:!tw-text-white md:tw-max-w-[200px]"
        @click="openItemSelection"
      >
        <TooltipCustom
          text-tooltip="Visualizar e selecionar produtos cadastrados"
        />
      </q-btn>
    </template>

    <template #after-options>
      <div class="tw-sticky tw-bottom-0 tw-bg-SGBRGrayBG tw-p-2">
        <div class="tw-flex tw-items-center tw-gap-4">
          <div class="tw-flex tw-items-center tw-gap-1">
            <q-icon
              name="keyboard_arrow_up"
              size="1.2rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten"
            />
            <q-icon
              name="keyboard_arrow_down"
              size="1.2rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten"
            />
            <span class="tw-text-[10px] tw-font-bold"> Navegar </span>
          </div>

          <div class="tw-flex tw-items-center tw-gap-1">
            <q-icon
              name="keyboard_return"
              size="0.9rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten tw-p-1"
            />
            <span class="tw-text-[10px] tw-font-bold"> Selecionar </span>
          </div>

          <div class="tw-flex tw-items-center tw-gap-1">
            <span
              class="tw-rounded-md tw-bg-SGBRGrayLighten tw-p-1 tw-text-[10px] tw-font-bold"
            >
              Esc
            </span>
            <span class="tw-text-[10px] tw-font-bold"> Fechar </span>
          </div>
        </div>
      </div>
    </template>

    <template #no-option>
      <span
        class="tw-flex tw-flex-row tw-items-center tw-justify-center tw-p-4 tw-font-medium"
        >Nenhum resultado encontrado para "{{
          searchString.toUpperCase()
        }}"</span
      >
    </template>

    <template #label>
      {{ props.labelSelect }}
    </template>
  </q-select>
</template>

<script setup>
import { useApiOptions } from 'components/generic/input/Select/composables/useApiOptions';
import hotkeys from 'hotkeys-js';
import { useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import ProductsList from 'src/components/modal/global/ProductsList.vue';
import notify from 'src/components/utils/notify';
import round from 'src/components/utils/round';
import DialogModalLayout from 'src/layouts/DialogModalLayout.vue';
import { toFloat } from 'src/services/utils';
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch
} from 'vue';
import { useRoute } from 'vue-router';

// Props do componente
const props = defineProps({
  labelSelect: {
    type: String,
    required: false,
    default: null
  },
  required: {
    type: Boolean,
    default: false
  },
  store: {
    type: Object,
    required: true
  },
  scope: {
    type: String,
    required: true
  },
  storeKey: {
    type: String,
    required: true
  },
  value: {
    type: [Object, String],
    required: false,
    default: ''
  },
  filters: {
    type: [Object, String],
    required: false,
    default: ''
  },
  orderBy: {
    type: [Object, String],
    required: false,
    default: () => {}
  },
  filterOr: {
    type: Array,
    default: null
  },
  deletedAt: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: null,
    required: false
  },
  tooltip: {
    type: String,
    default: null,
    required: false
  },
  defaultSearch: {
    type: [Number, null],
    default: null,
    required: false
  }
});

// Emits do componente.
const emit = defineEmits([
  'selected',
  'productFound',
  'searching',
  'importedProducts'
]);

const { filters, filterOr, deletedAt, disabled, orderBy, defaultSearch } =
  toRefs(props);

const route = useRoute();
const IS_DEVOLUCAO = route.path.includes('vendas/devolucao');
const $q = useQuasar();
const model = ref(null);
const selectRef = ref();
const selectInput = reactive([
  {
    filterText: '',
    filteredSelectOptions: []
  }
]);
let scrollEvent = null;
const searchString = ref('');
const isSearching = ref(false);

// Ao inicializar, ativa o listener do scroll para reposicionar o menu
onMounted(() => {
  if (props.value) {
    model.value = props.value;
  }

  const dialog = document.querySelector('.sgbrDialogs');

  if (dialog) {
    scrollEvent = dialog?.addEventListener('scroll', () => {
      if (selectRef.value) selectRef.value.updateMenuPosition();
    });
  }
});

// Para qualquer mudança externa no value, altera a model interna deste componente.
watch(
  () => props.value,
  (newValue) => {
    model.value = newValue;
  }
);

// Guarda o valor original da pesquisa utilizando "/" para manipulações futuras
const originalSearchValue = ref('');

/**
 * Ao selecionar uma opção do menu, emite a opção selecionada e fecha o menu.
 * @param {object} product
 */
const handleNewOptionSelected = (product) => {
  product.produtoWithEstoque.estoqueZeradoOuNegativo =
    Number(product?.produtoWithEstoque?.qtde) <= 0;

  let quantity = 1;

  if (originalSearchValue.value?.includes('*')) {
    const quantityString = originalSearchValue.value.split('*')[0];
    quantity = Number(quantityString.replace(',', '.')) || 1;
  }

  product.qtde = quantity;

  emit('productFound', product);
  selectRef.value.updateInputValue('');
  originalSearchValue.value = '';
};

const isRequesting = ref(false);

/**
 * Faz validações referentes ao que foi digitado.
 * Armazena as seguintes funcionalidades de digitação:
 *
 * R25*10 - 25 reais do product de controle 10.
 *
 * 15*10 - 15 unidades do product de controle 10.
 *
 * 123 - Busca controle ou EAN igual a 123.
 * @param {string} string
 */
const handleInputEnter = async (string) => {
  const rNumberRegex = /^[Rr](?!0$)(?!0\d)\d+(?:,\d+)?\*(?!0$)\d+$/; // Test if RNumber*Number.
  const numberNumberRegex = /^(?!0$)(?!0\d)\d+(?:,\d+)?\*(?!0)\d+$/; // Test if Number*Number (with or without comma)
  const onlyNumbersRegex = /^\d+$/; // Test if only numbers (codigo ou EAN)
  const numberStringAndSpace = /^[a-zA-Z0-9 ]+$/; // Test if only number

  if (rNumberRegex.test(string)) {
    let [precoTotal, controle] = string.split('*');
    precoTotal = precoTotal.replace(/r/gi, '');

    const product = await handleFetchProduct(controle);
    if (product) {
      precoTotal = toFloat(precoTotal);

      product.produtoWithEstoque.estoqueZeradoOuNegativo =
        Number(product?.produtoWithEstoque?.qtde) <= 0;

      product.qtde = round(
        Number(precoTotal) / Number(product.produtoWithEstoque?.precoVenda),
        4
      );
      emit('productFound', product);
      return;
    }
  }

  if (numberNumberRegex.test(string)) {
    const [quantity, controle] = string.split('*');

    const product = await handleFetchProduct(controle);
    if (product) {
      const qtde = Number(quantity.replace(',', '.'));

      product.produtoWithEstoque.estoqueZeradoOuNegativo =
        Number(product?.produtoWithEstoque?.qtde) <= 0;

      product.qtde = qtde;
      emit('productFound', product);
      return;
    }
  }

  if (
    onlyNumbersRegex.test(string) ||
    (numberStringAndSpace.test(string) && defaultSearch.value == '5')
  ) {
    const product = await handleFetchProduct(string, true);
    if (product) {
      product.produtoWithEstoque.estoqueZeradoOuNegativo =
        Number(product?.produtoWithEstoque?.qtde) <= 0;

      product.qtde = 1;
      emit('productFound', product);
      return;
    }
  }

  if (string && model.value && typeof model.value == 'string') {
    $q.notify({
      position: 'top',
      color: 'blue',
      message: 'Entrada inválida'
    });
    selectText();
  }
  isRequesting.value = false;
};

/**
 * Lida com a busca do produto de acordo com as configurações do PDV.
 * Utiliza a versão de filtrosV2.
 * @param searchString Parâmetro de busca
 */
const handleFetchProduct = async (searchString) => {
  const isLoading = $q.notify({
    position: 'top',
    color: 'gray',
    message: 'Carregando...',
    spinner: true
  });

  const BUSCA_GTIN_OU_CONTROLE = defaultSearch.value == '1';
  const BUSCA_COD_PRODUTO = defaultSearch.value == '2';
  const BUSCA_COD_BARRAS_GTIN = defaultSearch.value == '3';
  const BUSCA_COD_BARRAS_INTERNO = defaultSearch.value == '4';
  const BUSCA_REFERENCIA = defaultSearch.value == '5';
  const BUSCA_GTIN_OU_CONTROLE_OU_CODBARRASINTERNO = defaultSearch.value == '6';
  const BUSCA_COD_PRODUTO_OU_CODBARRASINTERNO = defaultSearch.value == '7';

  let searchGrade = true;
  let filtersV2 = [
    {
      operator: 'OR',
      field: 'controle',
      filterType: 'EQUALS',
      filterValue: searchString
    }
  ];

  // Seta filtros
  const setFilters = (field, value) => {
    filtersV2 = [
      ...filtersV2,
      {
        operator: 'OR',
        field: field,
        filterType: 'EQUALS',
        filterValue: value
      }
    ];
  };

  if (BUSCA_GTIN_OU_CONTROLE_OU_CODBARRASINTERNO) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
    setFilters('codBarras', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_GTIN_OU_CONTROLE) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_PRODUTO_OU_CODBARRASINTERNO) {
    filtersV2 = [];
    setFilters('codBarras', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_PRODUTO) {
    filtersV2 = [];
    searchGrade = false;
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_BARRAS_GTIN) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
  }

  if (BUSCA_COD_BARRAS_INTERNO) {
    filtersV2 = [];
    setFilters('codBarras', searchString);
  }

  if (BUSCA_REFERENCIA) {
    filtersV2 = [];
    searchGrade = false;
    setFilters('referencia', searchString);
  }

  if (IS_DEVOLUCAO) {
    filtersV2 = [
      ...filtersV2,
      {
        field: 'produtoWithCaracteristica.codTipoUso',
        filterType: 'NOT_IN_ARRAY',
        filterValue: [10]
      }
    ];
  }

  try {
    isSearching.value = true;
    const route = '/api/produto';
    const response = await api.get(route, { params: { filtersV2 } });

    // Caso não seja encontrado, procura pelo codBarras da grade.
    if (!response.data.length && searchString.length >= 6 && searchGrade) {
      const gradeResponse = await api.get('/api/produto/grade', {
        params: {
          filters: {
            codBarras: {
              filterType: 'EQUALS',
              filterValue: searchString
            }
          }
        }
      });

      if (gradeResponse.data && gradeResponse.data.length === 1) {
        const grade = gradeResponse.data[0];
        const item = {
          ...grade?.produto,
          grade: [grade]
        };
        delete item.grade?.[0].produto;
        return item;
      } else {
        notify('Não foi possível identificar o produto');
        selectText();
        return false;
      }
    }

    const product = response.data?.[0];

    if (!product) {
      notify('Não foi possível identificar o produto');
      selectText();
      return false;
    }

    if (product && product.deletedAt) {
      notify('Produto inativo');
      selectText();
      return false;
    }

    if (
      IS_DEVOLUCAO &&
      product &&
      product?.produtoWithCaracteristica?.codTipoUso == '10'
    ) {
      notify('Não é possível adicionar serviços na devolução');
      selectText();
      return false;
    }

    return product;
  } catch (error) {
    notify('Não foi possível identificar o produto');
    selectText();
    return false;
  } finally {
    isLoading();
    isSearching.value = false;
  }
};

/**
 * Abre o diálogo de seleção de produtos.
 * Isso permite ao usuário selecionar produtos de uma lista.
 * @returns {void}
 */
const openItemSelection = () => {
  if (document.querySelectorAll('.sgbrDialogs').length > 0) return false;

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: ProductsList,
      scope: 'selecao-items'
    }
  })
    .onOk((importedProducts) => {
      setTimeout(() => {
        emit('importedProducts', importedProducts);
      }, 300);
    })
    .onCancel(() => {});
};

// Opções do select.
const filtered_options = computed(() =>
  selectInput[0].filterText
    ? selectInput[0].filteredSelectOptions
    : selectOptions.value
);

/**
 * Filtra as opções de acordo com o digitado após '/' e abre o menu.
 * @param {string} value O valor inserido no campo de busca.
 * @returns {Promise<void>} Uma promessa que é resolvida quando o filtro é aplicado.
 */
const filterFn = async (value) => {
  const multiplyQuantityUsingNameRegex = /^(?!0$)(?!0\d)\d+(?:,\d+)?\*\/\S+$/;

  if (!value) selectRef.value.hidePopup();

  if (
    value.replace('/', '').length === 0 ||
    (!value.startsWith('/') && !multiplyQuantityUsingNameRegex.test(value))
  ) {
    return;
  }

  originalSearchValue.value = value;

  const string = value.split('/')[1];
  selectInput[0].filterText = string;
  await handleFilter(string, 0, filterOr.value);
  searchString.value = string;
  selectRef.value.showPopup();
};

function keydownBlock(ev) {
  if (isSearching.value) {
    ev.preventDefault();
    ev.stopPropagation();
  }
}

/**
 * Função que impede o display do menu caso não exista '/' no input.
 */
const handleShowPopup = () => {
  const selectLabel = selectRef.value.$el;
  const input = selectLabel.querySelector('input');

  if (input) {
    const shouldShowPopup = input.value?.split('/')?.[1]?.length > 0;

    if (!shouldShowPopup) {
      selectRef.value.hidePopup();
    }
  }
};

// Instancia do composable de selects que lida com ordernação, filtros e também infiniteScroll
const { updateScroll, handleFilter, loading, selectOptions } = useApiOptions(
  selectInput,
  props.store,
  props.storeKey,
  filters,
  orderBy.value ? orderBy : {},
  deletedAt.value
);

watch(isSearching, (newValue) => {
  emit('searching', newValue);
});

/**
 * Mantém o foco no campo.
 */
const focus = () => {
  nextTick(() => {
    const input = selectRef.value?.$el.querySelector('input');
    if (input) input.focus();
  });
  isRequesting.value = false;
};

/**
 * Seleciona o texto do input.
 */
const selectText = () => {
  selectRef?.value.$el?.querySelector('input')?.select();
  isRequesting.value = false;
};

/**
 * Limpa o texto do input.
 */
const clearText = () => {
  model.value = '';
  selectRef?.value.updateInputValue('');
  isRequesting.value = false;
};

/**
 * Atribui string para texto do input.
 * @param {string} text
 */
const setText = (text) => {
  selectRef?.value.updateInputValue(text);
  isRequesting.value = false;
};

defineExpose({ focus, selectText, clearText, setText });

onUnmounted(() => {
  if (scrollEvent) document.removeEventListener('scroll', scrollEvent);
});

// function onFocus() {
//   selectRef.value.target.focus();
// }

// Definir atalhos
let previousScope = hotkeys.getScope();

nextTick(() => {
  if (previousScope !== props.scope) {
    hotkeys.setScope(props.scope);
  }

  hotkeys('f2', props.scope, (event) => {
    if (!event.repeat) {
      event.preventDefault();

      focus();
      return false;
    }
  });
});
</script>
<style scoped>
div.q-field__inner.relative-position.col.self-stretch {
  height: 65px !important;
}
</style>
