<template>
  <SGRegister
    id="venda-devolucao-formulario"
    :title="controle ? 'Editar devolução' : 'Cadastro de devolução'"
    :cards="register?.cards?.value"
    :buttons="actionBarButtons"
    :disable-warning-animation="isWarningAnimationDisabled"
  >
    <SGCardBuilder v-if="values" v-model="register.cards.value" />
  </SGRegister>
</template>

<script setup>
/* eslint-disable */
import Money from 'components/generic/input/Money.vue';
import RegisterUndo from 'components/modal/global/RegisterUndo.vue';
import { printBase64Pdf } from 'components/utils/pdf';
import { diffObjects } from 'components/utils/tests';
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import { Dialog, useQuasar } from 'quasar';
import ClienteVenda from 'src/api/cliente/venda';
import { api as apiAxios } from 'src/boot/axios.js';
import useRegister from 'src/components/actionBar/composables/useRegister';
import SGButton from 'src/components/generic/SGButton.vue';
import TotalTaxes from 'src/components/modal/nfe/TotalTaxes.vue';
import notify from 'src/components/utils/notify';
import round from 'src/components/utils/round.js';
import InputPriceOrPercentSchema from 'src/core/components/Inputs/PriceOrPercentSchema.vue';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGCardBuilder from 'src/core/components/SG/Card/SGCardBuilder.vue';
import SGRegister from 'src/core/components/SG/Register/SGRegisterPage.vue';
import { useFormSchema } from 'src/core/composables/useFormSchema';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import Inputs from 'src/core/models/Inputs';
import ModelInputsSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputsText from 'src/core/models/inputs/Text';
import ModelInputsTextArea from 'src/core/models/inputs/TextArea';
import {
  calcularAjustePorcentagemOuReal,
  convertEmptyToNull,
  extractSchemaFields
} from 'src/core/utils';
import { default as RegisterPeople } from 'src/modules/pessoa/views/register.vue';
import Itens from 'src/modules/vendas/devolucao/components/Itens.vue';
import GerarFinanceiro from 'src/modules/vendas/devolucao/components/modals/GerarFinanceiro.vue';
import ImportarVenda from 'src/modules/vendas/devolucao/components/modals/ImportarVenda.vue';
import SelecionarItens from 'src/modules/vendas/devolucao/components/modals/SelecionarItens.vue';
import TipoVenda from 'src/modules/vendas/devolucao/components/modals/TipoVenda.vue';
import { arrayBufferToBase64, formatCPFCNPJ } from 'src/services/utils';
import { useGlobal } from 'src/stores/global';
import { computed, provide, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Register from '../models/register';
import ModelInputText from 'src/core/models/inputs/Text';
import { processMobilePrint } from 'src/services/processMobilePrint';
import { usePlataformInfo } from 'src/services/useIsMobile';
import { nextTick } from 'vue';

const global = useGlobal();

const $q = useQuasar();
const isWarningAnimationDisabled = ref(true);
const router = useRouter();
const route = useRoute();

const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  },
  data: {
    type: [Object, null],
    default: () => null,
    required: false
  }
});

const emit = defineEmits(['cancel', 'ok']);

const employeeFiltersV2 = [
  {
    field: 'deleted_at',
    filterType: 'NULL'
  },
  {
    not_deleted: true,
    field: 'codUsuario',
    filterType: 'FIXED',
    filterValue: global?.user?.controle
  },
  {
    field: 'controle',
    filterType: 'FIXED',
    filterValue: 1
  }
];

const cancel = async () => {
  isWarningAnimationDisabled.value = true;

  if (props?.modal) emit('cancel');
  else await router.push('/vendas/devolucao');
};

let controle =
  props?.data?.controle ??
  route?.params?.controle ??
  $q.sessionStorage.getItem('cloneId');

const api = new ClienteVenda();

let editValues = false;

if (controle) {
  try {
    const { data: response } = await api.get({}, `/${controle}`);
    const {
      vendaWithDevolucao: devolucao,
      vendaWithItem: item,
      ...venda
    } = response;

    if (venda.modulo != 4 || venda?.vendaWithDevolucao?.codSituacao == 2)
      throw 'Venda não encontrada!';

    editValues = {
      devolucao,
      item: $q.sessionStorage.getItem('cloneId')
        ? item.map((i) => ({ ...i, controle: null }))
        : item,
      venda
    };

    editValues.item.forEach((i) => {
      i.descricaoCodigo = i?.vendaItemWithProduto?.descricaoCodigo;
    });

    // INICIA O SALDO FINANCEIRO PARA VALIDAÇÃO FINAL
    editValues.devolucao.saldoFinanceiroVendaReferenciada =
      devolucao?.vendaReferenciada?.saldoFinanceiro;

    editValues.devolucao.vendaReferenciadaJaDevolvida =
      devolucao?.vendaReferenciada?.vendaDevolvida;

    editValues.devolucao.valorVendaReferenciada =
      devolucao?.vendaReferenciada?.valorFinal > 0
        ? devolucao?.vendaReferenciada?.valorFinal
        : devolucao?.vendaReferenciada?.valorLiquido;
  } catch (e) {
    await cancel();
  } finally {
    if ($q.sessionStorage.getItem('cloneId')) {
      $q.sessionStorage.setItem('cloneId', false);
      controle = null;
      editValues.controle = null;
    }
  }
} else {
  // DADOS DO FIXED MANUAL DE UM FILTRO DIFERENTE (NA PROXIMA REFATORACAO NAO DEVE SER USADO, POIS O FIXED IRÁ FUNCIONAR)
  // MAS O FILTRO DIFERENTE DEVE SER USADO NA PROXIMA REFATORACAO
  const { data: dataEmployee } = await apiAxios.get('/api/funcionario/select', {
    params: {
      page: 1,
      paginate: 20,
      filtersV2: employeeFiltersV2,
      orderBy: { controle: 'asc' }
    }
  });

  if (dataEmployee.fixed) {
    editValues = {
      ...editValues,
      venda: {
        ...editValues?.venda,
        codFuncionarioResponsavel: dataEmployee?.fixed?.controle
      }
    };
  }
}

const register = new Register(props?.modal);
const {
  setFieldValue,
  values,
  initialValues,
  handleReset,
  handleSubmit,
  errors,
  validate,
  isSubmitting,
  isValidating
} = useFormSchema(register.schema, editValues);

const codClienteRefetchOnChange = ref(false);

// SGCARDS E INPUTS
(() => {
  // CARD DADOS CADASTRIAS
  register.addCard('devolucao', {
    cols: '12',
    hasTitleSeparator: true,
    title: 'Devolução',
    hasCircle: true,
    removeGap: true
  });

  register.addCardInput('devolucao', 'devolucao.codSituacao', {
    component: InputSelect,
    params: {
      optionsStatic: [
        {
          descricao: 'ABERTO',
          controle: '1'
        },
        {
          descricao: 'FINALIZADO',
          controle: '2'
        }
      ]
    },
    bind: {
      ...ModelInputsSelectSearch,
      label: 'Situação',
      clearable: false,
      'use-input': false,
      class: 'tw-col-span-6'
    }
  });

  register.addCardInput('devolucao', 'venda.codFuncionarioResponsavel', {
    component: InputSelect,
    params: {
      apiRoute: '/api/funcionario/select',
      orderBy: {
        controle: 'asc'
      },
      filterOr: ['controle', 'nome'],
      filtersV2: employeeFiltersV2
    },
    bind: {
      ...ModelInputsSelectSearch,
      'option-label': 'descricaoCodigo',
      'option-value': 'controle',
      label: 'Funcionário',
      class: 'tw-col-span-6',
      clearable: false
    }
  });

  register.addCardInput('devolucao', 'venda.codCliente', {
    component: InputSelect,
    params: {
      apiRoute: '/api/pessoa',
      relationships: ['pessoaWithEndereco'],
      filtersV2: [
        {
          field: 'deleted_at',
          filterType: 'NULL'
        },
        {
          filterType: 'ILIKE',
          filterValue: 'Cliente',
          field: 'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao'
        }
      ],
      orderBy: {
        controle: 'desc'
      },
      filterOr: ['razaoSocial', 'controle', 'cnpjCpf'],
      optionDescription: (opt) => {
        if (!opt || !opt?.cnpjCpf) return '';
        return formatCPFCNPJ(opt?.cnpjCpf);
      },
      optionDetails: (opt) => {
        const endereco = opt?.pessoaWithEndereco?.endereco
          ? opt?.pessoaWithEndereco?.endereco + ', '
          : '';
        const cidade = opt?.pessoaWithEndereco?.cidade ?? '';
        const uf = opt?.pessoaWithEndereco?.uf
          ? ' - ' + opt?.pessoaWithEndereco?.uf
          : '';

        return endereco + cidade + uf;
      }
    },
    bind: {
      ...ModelInputsSelectSearch,
      'option-label': 'descricaoCodigo',
      'option-value': 'controle',
      label: 'Cliente',
      class: 'tw-col-span-6',
      rolePrefix: 'PESSOA',
      addButton: true,
      editButton: true,
      'refetch-on-change': codClienteRefetchOnChange
    },
    events: {
      onAdd: () => handlePeopleRegister(),
      onEdit: (controle) => handlePeopleRegister(controle)
    }
  });

  register.addCardInput('devolucao', 'devolucao.tipo', {
    component: InputSelect,
    params: {
      optionsStatic: [
        {
          descricao: 'MANUAL',
          controle: '1'
        },
        {
          descricao: 'VENDA',
          controle: '2'
        }
      ]
    },
    bind: {
      ...ModelInputsSelectSearch,
      label: 'Tipo de devolução',
      clearable: false,
      'use-input': false,
      class: 'tw-col-span-6'
    },
    events: {
      'update:modelValue': async () => {
        const confirm = await confirmaTrocaTipo();
        if (!confirm) {
          setFieldValue(
            'devolucao.tipo',
            values?.devolucao?.tipo === '1' ? '2' : '1'
          );
          return;
        }

        setFieldValue('item', []);
        setFieldValue('devolucao.tipoVenda', null);
        setFieldValue('devolucao.codVendaReferenciada', null);
        setFieldValue('devolucao.codVendaReferenciadaSerie', null);
        setFieldValue('venda.acrescimo', 0);
        setFieldValue('venda.desconto', 0);
      }
    }
  });

  register.addCard('vendaReferenciada', {
    if: computed(() => values.devolucao.tipo === '2'),
    hasTitleSeparator: true,
    title: 'Venda referenciada',
    hasCircle: computed(() => {
      return () => !values.item.filter((item) => !item?.deletar).length;
    }),
    removeGap: true,
    schemaError: 'devolucao',
    cols: 8,
    containerClass: '!tw-items-center'
  });

  register.addCardInput('vendaReferenciada', 'devolucao ', {
    component: SGButton,
    bind: {
      label: 'IMPORTAR VENDA',
      atalho: 'alt+i',
      class:
        '!tw-col-span-2 tw-bg-SGBRBlueLighten tw-px-2 tw-text-white !tw-mb-1.5'
    },
    events: {
      click: () => handleSelecionarTipoVenda()
    }
  });

  register.addCardInput('vendaReferenciada', 'devolucao.tipoVenda', {
    component: InputSelect,
    params: {
      optionsStatic: [
        {
          descricao: 'NFC-e',
          controle: '1'
        },
        {
          descricao: 'NF-e',
          controle: '2'
        },
        {
          descricao: 'NOTA MANUAL',
          controle: '3'
        }
      ]
    },
    bind: {
      ...ModelInputsSelectSearch,
      label: 'Tipo da venda',
      clearable: false,
      'use-input': false,
      readonly: true,
      class: 'tw-col-span-1'
    }
  });

  register.addCardInput(
    'vendaReferenciada',
    'devolucao.codVendaReferenciadaSerie',
    {
      if: computed(
        () => values.devolucao.tipo === '2' && values.devolucao.tipoVenda == '1'
      ),
      bind: {
        ...ModelInputsText,
        label: 'Série',
        readonly: true,
        class: '!tw-col-span-1'
      }
    }
  );

  register.addCardInput('vendaReferenciada', 'devolucao.nota', {
    if: computed(
      () => values.devolucao.tipo === '2' && values.devolucao.tipoVenda == '1'
    ),
    bind: {
      ...ModelInputText,
      label: 'Nota',
      readonly: true,
      class: '!tw-col-span-4'
    }
  });

  register.addCard('itens', {
    hasTitleSeparator: true,
    title: 'Itens',
    hasCircle: computed(() => {
      return () => !values.item.filter((item) => !item?.deletar).length;
    }),
    removeGap: true,
    schemaError: 'item'
  });

  const itens = new Inputs(false);

  itens.addInput('descricaoCodigo', {
    bind: {
      ...ModelInputsSelectSearch,
      label: 'Produto/Serviço',
      preload: true,
      readonly: true,
      'use-input': false,
      'option-label': (opt) => `${opt.controle} - ${opt.nome}`,
      'input-class': 'tw-bg-transparent'
    }
  });

  itens.addInput('qtde', {
    component: Money,
    bind: {
      ...ModelInputsText,
      label: 'Qtde.',
      'decimals-quantity': 4,
      max: 99999999999.9999,
      readonly: (model) => values.devolucao.tipo === '2' || model.usaGrade
    }
  });

  itens.addInput('valorUnitario', {
    component: Money,
    bind: {
      ...ModelInputsText,
      label: 'Valor unitário',
      'decimals-quantity': global.qtdCasasDecimais,
      readonly: computed(() => values.devolucao.tipo === '2')
    }
  });

  itens.addInput('valorAcrescimo', {
    component: InputPriceOrPercentSchema,
    extraSchemaName: 'tipoValorAcrescimo',
    bind: {
      ...ModelInputsText,
      label: 'Valor acrésc.',
      readonly: true
    }
  });

  itens.addInput('valorDesconto', {
    component: InputPriceOrPercentSchema,
    extraSchemaName: 'tipoValorDesconto',
    bind: {
      ...ModelInputsText,
      label: 'Valor desc.',
      readonly: true
    }
  });

  const totais = new Inputs(false);

  totais.addInput('acrescimo', {
    component: InputPriceOrPercentSchema,
    bind: {
      ...ModelInputsText,
      label: 'Acréscimo',
      'hide-bottom-space': true,
      readonly: true,
      modelValue: computed(() => {
        setFieldValue('venda.acrescimo', round(values.venda.acrescimo, 2));
        // setFieldValue(
        //   'venda.valorLiquido',
        //   round(values.venda?.valorLiquido, 2)
        // );
        return round(values.venda?.acrescimo, 2);
      }),
      extra: computed(() => values.venda?.tipoValorAcrescimo ?? 0)
    }
  });

  totais.addInput('desconto', {
    component: InputPriceOrPercentSchema,
    bind: {
      ...ModelInputsText,
      label: 'Desconto',
      'hide-bottom-space': true,
      readonly: true,
      modelValue: computed(() => {
        setFieldValue('venda.desconto', round(values.venda.desconto, 2));
        // setFieldValue(
        //   'venda.valorLiquido',
        //   round(values.venda?.valorLiquido, 2)
        // );
        return round(values.venda.desconto, 2);
      }),
      extra: computed(() => values.venda?.tipoValorDesconto ?? 0)
    }
  });

  totais.addInput('valorBruto', {
    component: Money,
    bind: {
      ...ModelInputsText,
      label: 'Total bruto',
      'hide-bottom-space': true,
      readonly: true,
      modelValue: computed(() => values.venda?.valorBruto ?? 0)
    }
  });

  totais.addInput('valorLiquido', {
    component: Money,
    bind: {
      ...ModelInputsText,
      label: 'Total liquido',
      'hide-bottom-space': true,
      readonly: true,
      modelValue: computed(() => {
        const { liquido } = values.item?.reduce(
          (result, item) => {
            const valorBruto = item.valorUnitario * item.qtde;

            result.bruto += Number(valorBruto);

            result.liquido +=
              Number(valorBruto) +
              Number(item.valorAcrescimo ?? 0) -
              Number(item.valorDesconto ?? 0);

            return result;
          },
          { bruto: 0, liquido: 0, final: 0 }
        );

        const valorLiquidoFinal =
          Number(liquido) +
          Number(values.venda.acrescimo) -
          Number(values.venda.desconto);

        setFieldValue('venda.valorLiquido', round(valorLiquidoFinal, 2));

        return round(valorLiquidoFinal, 2);
      })
    }
  });

  // totais.addInput('valorFinal', {
  //   component: Money,
  //   bind: {
  //     ...ModelInputsText,
  //     label: 'Total com impostos',
  //     'hide-bottom-space': true,
  //     readonly: true,
  //     modelValue: computed(() => {
  //       return values.venda.valorFinal;
  //     })
  //   }
  // });

  register.addCardInput('itens', 'item', {
    component: Itens,
    // Conserta o placeholder do input de seleção de itens
    // esse input não foi refatorado então não deve ser afetado pelos estilos da refatoração
    refactClass: false,
    bind: {
      array: 'item',
      cast: (value) => register.schemaItem.json().cast(value),
      inputs: itens.inputs,
      inputsRefs: itens.inputsRefs,
      totais: totais.inputs,
      totalItem: (item) => {
        const valorBase = item.valorUnitario * item.qtde;
        const acrescimo = calcularAjustePorcentagemOuReal(
          item.valorAcrescimo,
          item.tipoValorAcrescimo,
          valorBase
        );
        const desconto = calcularAjustePorcentagemOuReal(
          item.valorDesconto,
          item.tipoValorDesconto,
          valorBase
        );

        // if (values.devolucao?.codVendaReferenciada) {
        //   return (item.valorFinal / item.qtdeVenda) * item.qtde;
        // }

        return valorBase + acrescimo - desconto;
      },
      errors,
      confirmToRemove: values.devolucao.tipo === '2',
      searchable: computed(() => values.devolucao.tipo === '1'),
      emptyMessage: computed(() =>
        values.devolucao.tipo === '2'
          ? values.devolucao.codVendaReferenciada
            ? 'Reimporte a venda para adicionar os itens novamente'
            : 'Importe uma venda para adicionar os itens'
          : 'Nenhum item adicionado'
      )
    },
    events: {
      total: () => {
        const { bruto, liquido, final } = values.item?.reduce(
          (result, item) => {
            const valorBruto = item.valorUnitario * item.qtde;

            // if (values.devolucao?.codVendaReferenciada) {
            //   result.liquido += Number(item.valorFinal);
            // }

            // result.final += Number(
            //   (item?.valorFinal / item?.qtdeVenda) * item?.qtde ?? 0
            // );
            result.bruto += Number(valorBruto);
            result.liquido +=
              Number(valorBruto) +
              Number(item.valorAcrescimo ?? 0) -
              Number(item.valorDesconto ?? 0);

            return result;
          },
          { bruto: 0, liquido: 0, final: 0 }
        );

        const valorLiquidoFinal =
          Number(liquido) +
          Number(values.venda.acrescimo) -
          Number(values.venda.desconto);

        setFieldValue('venda.valorBruto', round(bruto, 2));
        setFieldValue('venda.valorLiquido', round(valorLiquidoFinal, 2));
        const tipoVendaNfe = '2';
        if (values.devolucao.tipoVenda == tipoVendaNfe) {
          setFieldValue('venda.valorFinal', round(valorLiquidoFinal, 2));
        } else {
          setFieldValue('venda.valorFinal', 0);
        }

        // setFieldValue(
        //   'venda.valorFinal',
        //   round(final - values.venda.desconto + values.venda.acrescimo, 2)
        // );
      }
    }
  });

  register.addCard('observacoes', {
    cols: '12',
    hasTitleSeparator: true,
    title: 'Observações',
    hasCircle: true,
    removeGap: true
  });

  register.addCardInput('observacoes', 'venda.observacoes', {
    bind: {
      ...ModelInputsTextArea,
      type: 'textarea'
    }
  });
})();

const confirmaTrocaTipo = async () => {
  const hasInfo =
    values.item?.length ||
    values.devolucao?.codVendaReferenciada ||
    values.devolucao?.codVendaReferenciadaSerie ||
    values.devolucao?.tipoVenda;

  if (!hasInfo) return true;

  return await new Promise((res) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'trocaDevolucao',
        title: 'Troca do tipo de devolução',
        description:
          'O tipo de devolução foi alterado. As informações do documento serão apagadas. Deseja prosseguir?',
        classCardSection: 'lg:tw-w-[450px]'
      }
    })
      .onOk(() => res(true))
      .onCancel(() => res(false));
  });
};

watch(
  values,
  async () => {
    await validate();
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);

const handleImportarVenda = (tipoVenda) => {
  Dialog.create({
    component: DialogModalLayout,
    componentProps: {
      componentRef: ImportarVenda,
      scope: 'importar-venda',
      data: {
        tipoVenda
      }
    }
  })
    .onOk(async (venda) => {
      let novosItens = [];

      let itensAtuais = values.item?.filter((item) => item?.controle);
      if (itensAtuais?.length) {
        novosItens = itensAtuais.reduce((acc, item) => {
          if (item?.controle) {
            acc.push({ ...item, deletar: true });
          }
          return acc;
        }, []);
      }

      let itensDaNotaSelecionada = await handleSelecionarItens(venda);
      if (!itensDaNotaSelecionada.length) {
        setFieldValue('devolucao.tipoVenda', '');
        return;
      }
      novosItens = [...novosItens, ...itensDaNotaSelecionada];

      setFieldValue('item', novosItens);

      // ESSA PARTE É JUNTA
      // importante para o select pesquisar novamente o fixed apos cod cliente
      codClienteRefetchOnChange.value = true;

      setFieldValue('venda.codCliente', venda.codCliente);

      nextTick(() => {
        codClienteRefetchOnChange.value = false;
      });
      // ESSA PARTE É JUNTA

      setFieldValue(
        'venda.codFuncionarioResponsavel',
        venda.codFuncionarioResponsavel
      );
      setFieldValue(
        'devolucao.codVendaReferenciadaSerie',
        venda.vendaWithDfe?.serie
      );
      setFieldValue('devolucao.nota', venda.vendaWithDfe?.sequencia);
      setFieldValue('devolucao.codVendaReferenciada', venda.controle);

      // const { descontoVenda, acrescimoVenda } = novosItens.reduce(
      //   (res, item) => {
      //     res.descontoVenda += Number(
      //       (item?.descontoVenda / item?.qtdeVenda) * item.qtde || 0
      //     );

      //     res.acrescimoVenda += Number(
      //       (item.acrescimoVenda / item?.qtdeVenda) * item.qtde || 0
      //     );
      //     return res;
      //   },
      //   { descontoVenda: 0, acrescimoVenda: 0 }
      // );

      setFieldValue('venda.tipoValorAcrescimo', 0);
      setFieldValue('venda.tipoValorDesconto', 0);
      setFieldValue('venda.acrescimo', Number(venda?.acrescimo) ?? 0);
      setFieldValue('venda.desconto', Number(venda?.desconto) ?? 0);

      setFieldValue(
        'devolucao.saldoFinanceiroVendaReferenciada',
        venda?.saldoFinanceiro
      );

      setFieldValue(
        'devolucao.vendaReferenciadaJaDevolvida',
        venda?.vendaDevolvida
      );

      let valorVendaReferenciada =
        venda?.valorFinal > 0 ? venda?.valorFinal : venda?.valorLiquido;
      if (venda?.vendaDevolvida) {
        valorVendaReferenciada = round(
          Number(valorVendaReferenciada) - Number(venda?.valorDevolvido)
        );
      }

      setFieldValue('devolucao.valorVendaReferenciada', valorVendaReferenciada);
      const tipoVendaNfe = '2';
      if (values.devolucao.tipoVenda == tipoVendaNfe) {
        setFieldValue(
          'venda.valorFinal',
          round(valorVendaReferenciada, 2) ?? 0
        );
      } else {
        setFieldValue('venda.valorFinal', 0);
      }
    })
    .onCancel(() => {
      setFieldValue('devolucao.nota', null);
      setFieldValue('devolucao.tipoVenda', null);
      setFieldValue('devolucao.codVendaReferenciada', null);
      setFieldValue('devolucao.codVendaReferenciadaSerie', null);
    });
};

const handleSelecionarTipoVenda = () => {
  Dialog.create({
    component: MiniModalLayout,
    componentProps: {
      componentRef: TipoVenda,
      hasCancel: false,
      scope: 'tipoVenda',
      hasSave: false,
      title: 'Tipo da venda',
      titleClass: '!tw-text-md',
      dataModal: {
        buttons: [
          {
            bind: {
              text: 'NOTA FISCAL DE CONSUMIDOR (NFC-E)',
              flat: true,
              class: 'tw-rounded-md !tw-px-4',
              atalho: 'f1'
            },
            events: {
              click: () => setFieldValue('devolucao.tipoVenda', '1')
            }
          },
          {
            bind: {
              text: 'NOTA FISCAL ELETRÔNICA (NF-E)',
              flat: true,
              class: 'tw-rounded-md !tw-px-4',
              atalho: 'f2'
            },
            events: {
              click: () => setFieldValue('devolucao.tipoVenda', '2')
            }
          },
          {
            bind: {
              text: 'NOTA MANUAL',
              flat: true,
              class: 'tw-rounded-md !tw-px-4',
              atalho: 'f3'
            },
            events: {
              click: () => {
                setFieldValue('devolucao.tipoVenda', '3');
              }
            }
          }
        ]
      }
    }
  }).onDismiss(() => {
    if (values.devolucao.tipoVenda) {
      handleImportarVenda(values.devolucao.tipoVenda);
      setFieldValue('item', []);
    }
  });
};

/**
 * Função que abre o modal de registro e edição de pessoas.
 * @param {any} controle - O controle a ser utilizado para edição de registro.
 * @returns {void}
 */
const handlePeopleRegister = (controle = null) => {
  let options = {};
  if (controle) {
    options = { data: { controle: controle, isEditing: true } };
  }

  // Modal para cadastro ou edição de pessoas.
  Dialog.create({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'casdastrar-pessoa-devolucao',
      ...options
    }
  }).onDismiss(async () => {
    // Atualiza a lista
    register.getCardInputRef('devolucao', 'venda.codCliente').reMount();
    register.getCardInputRef('devolucao', 'venda.codCliente').ref.showPopup();
  });
};

const handlePerguntaFinanceiro = async () => {
  return new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'perguntaFinanceiro',
        title: 'Pagamento',
        description: 'Deseja gerar um lançamento financeiro?',
        classCardSection: 'lg:tw-w-[450px]',
        hasCloseIcon: false
      }
    })
      .onOk(() => {
        resolve(true);
      })
      .onCancel(() => {
        resolve(false);
      });
  });
};

const handleGerarFinanceiro = (aReceber) => {
  return new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        componentRef: GerarFinanceiro,
        scope: 'gerarFinanceiro',
        hasCancel: false,
        hasSave: false,
        hasCloseIcon: false,
        dataModal: {
          saldoTotal: aReceber,
          codVenda: values.devolucao.codVendaReferenciada,
          codCliente: values.venda.codCliente,
          nota: values.devolucao?.nota || values.devolucao.codVendaReferenciada
        }
      }
    })
      .onOk((value) => {
        setFieldValue('devolucao.financeiro', value);
      })
      .onDismiss(resolve);
  });
};

/**
 * Abre seleção dos itens da devolução
 * @param venda
 */
const handleSelecionarItens = async (venda) => {
  let itensSelecionados = [];
  await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        componentRef: SelecionarItens,
        title: 'Devolução de itens',
        scope: 'devolutionItens',
        hasCancel: false,
        hasSave: false,
        dataModal: {
          venda,
          itens: venda?.vendaWithItem,
          moduloDevolucao: true
        }
      }
    })
      .onOk((itensSelecionadosModal) => {
        itensSelecionados = itensSelecionadosModal.reduce((result, item) => {
          if (parseFloat(item.qtdeDevolvida) <= 0) return result;

          const tempItem = {
            ...item,
            fiscal: {
              codCfop: item?.vendaItemWithFiscal?.codCfop ?? '',
              codCstCsosn: item?.vendaItemWithFiscal?.codCstCsosn ?? ''
            },
            descricaoCodigo: item?.vendaItemWithProduto?.descricaoCodigo,
            controle: null,
            codItemReferenciado: item.controle,
            // qtdeVenda: item.qtde + item.qtdeDevolvida,
            qtde: item.qtdeDevolvida,
            tipoValorAcrescimo: item?.tipoValorAcrescimo
              ? Number(item.tipoValorAcrescimo)
              : 0,
            valorAcrescimo: item?.valorAcrescimo
              ? (Number(item.valorAcrescimo) / item.qtdeVenda) *
                item.qtdeDevolvida
              : 0,
            tipoValorDesconto: item?.tipoValorAcrescimo
              ? Number(item.tipoValorAcrescimo)
              : 0,
            valorDesconto: item?.valorDesconto
              ? (Number(item.valorDesconto) / item.qtdeVenda) *
                item.qtdeDevolvida
              : 0,
            grade: item?.vendaItemWithGrade
              ? item.vendaItemWithGrade.map((grade) => {
                  const tempGrade = {
                    ...grade,
                    codGradeReferenciada: grade.controle,
                    quantidade: grade.qtdeDevolvida,
                    controle: null
                  };
                  return register.schemaItemGrade.cast(tempGrade);
                })
              : null,
            usaGrade: item?.vendaItemWithGrade?.length > 0
          };

          result.push(register.schemaItem.cast(tempItem));
          return result;
        }, []);
        resolve();
      })
      .onCancel(() => resolve());
  });

  return itensSelecionados;
};

const handleGerarNota = async (controleFinalizado) => {
  await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'geraNota',
        title: 'Gerar nota fiscal',
        description: 'Deseja gerar a nota fiscal agora?',
        classCardSection: 'lg:tw-w-[450px]',
        hasCloseIcon: false
      }
    })
      .onOk(async () => {
        isWarningAnimationDisabled.value = true;
        const endLoading = $q.notify({
          position: 'top',
          color: 'gray',
          message: 'Gerando nota fiscal...',
          spinner: true
        });
        setTimeout(async () => {
          endLoading();
          await router.push('/vendas/nf/editar/' + controleFinalizado);
        }, 2000);
      })
      .onCancel(async () => {
        await cancel();
      })
      .onDismiss(resolve);
  });
};

const desejaImprimir = async function () {
  return await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'imprimirDevolucao',
        title: 'Imprimir devolução',
        description: 'Deseja imprimir a devolução agora?',
        classCardSection: 'lg:tw-w-[450px]',
        hasCloseIcon: false
      }
    })
      .onOk(async () => {
        resolve(true);
      })
      .onCancel(async () => {
        resolve(false);
      });
  });
};

const getComprovante = async function (controleFinalizado) {
  const dialog = $q.dialog({
    message: 'Gerando impressão...',
    progress: true,
    persistent: true,
    ok: false,
    class: '!tw-flex !tw-flex-col tw-items-center tw-justify-center tw-py-4'
  });
  try {
    const response = await apiAxios.get(
      `/api/venda/devolucao/comprovante/${controleFinalizado}?bloquearImpressao=${false}`,
      {
        responseType: 'arraybuffer'
      }
    );
    setTimeout(() => {
      dialog.hide();
    }, 3000);

    return response;
  } catch (error) {
    notify('Erro ao carregar impressão. Tente novamente mais tarde.');
    dialog.hide();
    return false;
  }
};

const handleImpressao = async (controleFinalizado) => {
  await new Promise(async (resolve) => {
    const { isMobile } = usePlataformInfo();

    if (!isMobile) {
      const desejaImprimirResponse = await desejaImprimir();
      if (!desejaImprimirResponse) return resolve();

      const response = await getComprovante(controleFinalizado);
      if (response.status == 200) {
        const base64String = await arrayBufferToBase64(response.data);
        printBase64Pdf(base64String);
      }
    } else {
      const response = await getComprovante(controleFinalizado);
      if (response.status == 200) {
        const shouldPrint = !(await processMobilePrint(response.data, true));
        if (shouldPrint) {
          const base64String = await arrayBufferToBase64(response.data);
          printBase64Pdf(base64String);
        }
      }
    }
    return resolve();
  });
};

const handleSave = async () => {
  let request;
  let aReceber = 0;
  let {
    vendaReferenciadaJaDevolvida: devolvida,
    valorVendaReferenciada: valorReferenciada,
    saldoFinanceiroVendaReferenciada: saldoReferenciada,
    tipo: tipoDevolucao,
    tipoVenda
  } = values.devolucao;
  saldoReferenciada = Number(saldoReferenciada);

  const isDevolucaoManual = tipoDevolucao == '1';
  const isNfe = tipoVenda == '2';

  let valorLiquidoDevolucao =
    values.venda?.valorFinal > 0
      ? values.venda?.valorFinal
      : values.venda?.valorLiquido;

  if (devolvida && values.item?.every((it) => it.qtde === it.qtdeDevolvida)) {
    return openDevolvidaTotalModal();
  }

  if (!isDevolucaoManual) {
    aReceber = valorReferenciada - valorLiquidoDevolucao;
  }

  if (saldoReferenciada > 0 && !isDevolucaoManual) {
    let valorAEstornar = Math.min(saldoReferenciada, valorLiquidoDevolucao);

    if (saldoReferenciada >= valorLiquidoDevolucao) {
      aReceber -= saldoReferenciada - valorAEstornar;
    }
  }

  let payload = extractSchemaFields(values, register.schema);

  const NATUREZA_OPERACAO_DEVOLUCAO_VENDA = 19;
  payload.venda.codNaturezaOperacao = NATUREZA_OPERACAO_DEVOLUCAO_VENDA; // DEFINE UMA NATUREZA DE OPERAÇÃO PADRÃO PARA CRIAÇÃO DA NF,
  let status = payload.devolucao?.codSituacao;
  const isDevolucaoFinalizada = status == '2';

  // Para fazer o POST sem finalizar devolução.
  if (payload.devolucao?.codSituacao === '2') {
    payload.devolucao.codSituacao = '1';
  }

  if (route?.path.includes('editar') && controle) {
    payload = convertEmptyToNull(payload);
    request = await api.put(payload, `/geral`);
  } else {
    request = await api.post(payload, '/geral');
  }

  // Se finalizada, gera impressao do comprovante / nota fiscal
  if (!request?.success) return;

  let impostos = null;
  if (isNfe) {
    impostos = await calcularImposto(request?.data?.venda?.controle);
    if (!impostos) return;
  }

  let geraFinanceiro = false;
  if (isDevolucaoFinalizada) {
    geraFinanceiro = await handlePerguntaFinanceiro();

    const vNFMaiorSaldoFinanceiro = isNfe
      ? impostos?.total?.vNF > saldoReferenciada
      : true;

    if (
      aReceber > 0 &&
      !isDevolucaoManual &&
      geraFinanceiro &&
      vNFMaiorSaldoFinanceiro
    ) {
      await handleGerarFinanceiro(aReceber);
    }
  }

  const devolucao = {
    ...payload.devolucao,
    financeiro: values.devolucao.financeiro,
    codSituacao: status,
    geraFinanceiro
  };

  await api.put(
    {
      devolucao,
      venda: { controle: request?.data?.venda?.controle }
    },
    `/geral`
  );

  if (devolucao.codSituacao === '2') {
    await handleImpressao(request?.data?.venda?.controle);
  }
  if (
    devolucao.codSituacao === '2' &&
    devolucao.tipoVenda !== '3' &&
    devolucao.tipo === '2' &&
    global.roles?.includes('VENDA.DEVOLUCAO:EMITIR-NFE')
  ) {
    await handleGerarNota(request?.data?.venda?.controle);
  }

  await cancel();
};

async function openDevolvidaTotalModal() {
  return await new Promise((resolve) => {
    Dialog.create({
      title: 'Venda devolvida totalmente',
      message:
        'Essa venda foi devolvida totalmente, não é possível devolve-la novamente.',
      persistent: true,
      class: 'q-dialog-plugin tw-w-[362px] !tw-rounded-lg tw-pb-1 tw-pr-1',
      ok: {
        label: 'VOLTAR',
        class: '!tw-min-w-[94px] !tw-rounded-lg'
      }
    }).onDismiss(() => resolve());
  });
}

const save = handleSubmit(handleSave, register.scrollToError);

const calcularImposto = async (controle) => {
  if (!controle) return false;

  return new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: TotalTaxes,
        scope: 'totais-impostos',
        hasCancel: false,
        hasSave: false,
        hasCloseIcon: true,
        title: 'Totais e impostos da nota',
        dataModal: {
          controle
        }
      }
    })
      .onOk((payload) => resolve(payload))
      .onCancel(() => resolve(false));
  });
};

function reset() {
  // Abre um modal de confirmação para redefinir os valores
  $q.dialog({
    component: RegisterUndo
  }).onOk(handleReset);
}

const completed = computed(() => !isSubmitting.value || !isValidating.value);

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: {
    modal: props.modal,
    completed,
    isWarningAnimationDisabled
  },
  events: {
    save: {
      callback: save
    },
    cancel: {
      callback: cancel
    },
    reset: {
      callback: reset
    }
  }
});

const atalhos = [
  {
    key: 'alt+i',
    event: () => handleSelecionarTipoVenda(),
    condition: computed(() => values.devolucao.tipo === '2')
  }
];

// Definir atalhos
useScopedHotkeys(atalhos, 'register-venda-devolucao-formulario');

// PROVIDERS
provide('model', register);
</script>

<style></style>
