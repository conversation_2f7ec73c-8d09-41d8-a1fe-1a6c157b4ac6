<template>
  <div class="devolucao-itens tw-flex tw-flex-col">
    <div
      v-if="attributes?.searchable ?? true"
      class="tw-relative tw-mb-1 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-justify-center tw-py-2"
    >
      <q-icon
        v-if="$q.screen.gt.sm"
        name="info"
        color="primary"
        size="18px"
        dense
        unelevated
        flat
        padding="2px 2px"
        class="tw-absolute -tw-top-3 tw-right-0"
      >
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -5]"
          text-tooltip="Digite '/' antes do texto para pesquisar por nome."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -40]"
          text-tooltip="Ao digitar R25*1 e pressionar 'Enter', será adicionado 25 reais do produto de código 1."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -90]"
          text-tooltip="Ao digitar 25*1 e pressionar 'Enter', será adicionado 25 unidades do produto de código 1."
        />
      </q-icon>
      <ProductSelect
        v-if="!props.readonly"
        scope="register-venda-devolucao-formulario"
        ref="productSearchRef"
        class="productInput tw-w-full"
        :store="stock"
        :loading="false"
        placeholder="Buscar Item"
        :filters="stockFilters"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'nome']"
        :value="stockEditValue"
        :default-search="1"
        @searching="(isSearching) => (isTableLoading = isSearching)"
        @imported-products="handleImportedProducts"
        @product-found="handleAddItem"
      />
    </div>
    <div
      class="customScroll tw-relative tw-flex tw-flex-col tw-items-start tw-justify-start tw-gap-2 tw-overflow-auto tw-pb-4"
      ref="itemsScrollContainerRef"
    >
      <div
        class="customGrid tw-sticky tw-top-0 tw-z-[3] tw-w-full tw-min-w-fit tw-items-end tw-justify-start tw-gap-customGridGapCards tw-bg-SGBRGrayBG tw-px-2 tw-py-1 tw-pb-2 md:tw-grid"
        v-if="$q.screen.gt.sm"
      >
        <p
          class="tw-pl-[10px] tw-text-textsSGBR-gray"
          v-for="column in columns"
          :key="column.id"
        >
          {{ column.replace('*', '')
          }}<span v-if="column.indexOf('*') != -1" class="require">*</span>
        </p>
      </div>
      <div v-if="totalDeItens > 0" class="!tw-max-h-[310px] tw-w-full">
        <div ref="itemsContainerRef">
          <div
            v-for="(_, index) in model"
            :key="model[index]"
            class="customGrid davInputs tw-flex tw-w-full tw-flex-col tw-gap-customGridGapCards tw-gap-y-0 md:tw-grid md:tw-bg-transparent"
          >
            <template v-if="!model[index]?.deletar">
              <template
                v-for="input in attributes.inputs"
                :key="index + input.id"
              >
                <InputBuilderUseField
                  :input="input"
                  :array="attributes.id"
                  :index="index"
                  :error-message="
                    attributes.errors[`${attributes.id}[${index}].${input.id}`]
                  "
                  v-model="model[index][input.id]"
                  v-model:extra="model[index][input?.extraSchemaName]"
                  :father="model[index]"
                />
              </template>
              <div v-if="attributes?.totalItem">
                <Money
                  :model-value="attributes.totalItem(model[index])"
                  outlined
                  readonly
                  borderless
                  dense
                  label="Total"
                  class="tw-bg-white"
                />
              </div>
              <div
                class="tw-flex tw-flex-row tw-items-end tw-justify-end tw-gap-2 md:tw-justify-start md:tw-gap-0"
                v-if="deletar"
              >
                <q-btn
                  v-if="model[index]?.usaGrade"
                  @click="() => openGradeTable(model[index].grade, index)"
                  color="primary"
                  icon="dry_cleaning"
                  unelevated
                  :readonly="props.readonly"
                  dense
                  class="tw-my-auto tw-bg-gray-300 md:tw-bg-transparent"
                  flat
                  size="15px"
                  padding="0px 5px"
                >
                  <TooltipCustom text-tooltip="Grade" />
                </q-btn>
                <q-btn
                  @click="() => removeItem(model[index], index)"
                  v-if="!props.readonly"
                  color="red-5"
                  icon="delete"
                  unelevated
                  dense
                  class="tw-my-auto tw-bg-gray-300 md:tw-ml-auto md:tw-bg-transparent"
                  flat
                  size="12px"
                  padding="0px 5px"
                >
                  <TooltipCustom text-tooltip="Remover item" />
                </q-btn>
              </div>
            </template>
          </div>
        </div>
      </div>

      <div
        v-else
        class="tw-flex tw-w-full tw-flex-row tw-flex-wrap tw-items-center tw-justify-center"
      >
        <span class="tw-my-4 tw-font-bold tw-text-SGBRGray">
          {{ emptyMessage }}
        </span>
      </div>
    </div>
    <div
      v-if="attributes?.totais"
      class="tw-mb-5 tw-mt-4 tw-flex tw-w-full tw-flex-row tw-items-center tw-pt-4 md:tw-justify-end"
    >
      <div
        class="tw-flex tw-w-full tw-grid-cols-4 tw-flex-col tw-gap-4 sm:tw-w-fit md:tw-grid"
      >
        <template v-for="(total, index) in attributes.totais" :key="total.id">
          <InputBuilderUseField
            :input="attributes.totais[index]"
            :error-message="attributes.errors[total.id]"
            :v-model="attributes.totais[index].bind.modelValue"
            v-model:extra="attributes.totais[index].bind.extra"
          />
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
/* eslint-disable */
import { watch, ref, nextTick, computed, toRefs } from 'vue';
import { useStock } from 'stores/api/stock';
import Money from 'components/generic/input/Money.vue';
import { useQuasar } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { usePdvConfig } from 'stores/api/pdv/pdvConfig';
import { storeToRefs } from 'pinia';
import ProductSelect from 'components/generic/input/ProductSelect.vue';
import InputBuilderUseField from 'src/core/components/Inputs/BuilderUseField.vue';
import { calcularAjustePorcentagemOuReal } from 'src/core/utils';
import { useSplitAttrs } from 'quasar';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import ItensGrade from 'src/modules/vendas/devolucao/components/modals/ItensGrade.vue';
import GradeDevolucao from 'src/modules/vendas/devolucao/components/modals/GradeDevolucao.vue';
import RegisterDelete from 'src/components/modal/global/RegisterDelete.vue';

const $q = useQuasar();
const { attributes } = useSplitAttrs();

const model = defineModel({ type: Array, required: true, default: [] });
const totalDeItens = computed(
  () => model.value.filter((item) => !item?.deletar).length
);

const pdvConfig = usePdvConfig();
const { data: pdvConfigData } = storeToRefs(pdvConfig);

nextTick(async () => {
  await pdvConfig.get(null, false, false);
});

// Select de produto
const productSearchRef = ref();
const stock = useStock();
const stockFilters = {
  deleted_at: {
    filterType: 'NULL'
  }
};

const stockEditValue = ref();

const props = defineProps({
  dataItems: { type: Array, default: () => [] },
  btnAddSelect: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  deletar: {
    type: Boolean,
    default: true
  },
  emptyMessage: {
    type: String,
    default: 'Nenhum item adicionado'
  }
});

const { deletar, emptyMessage } = toRefs(props);

const emit = defineEmits(['total', 'updatedTodo', 'onClickAdd', 'propsDone']);

const itemsContainerRef = ref();

const itemsScrollContainerRef = ref();

watch(
  model,
  (newModel) => {
    const total = newModel.reduce(
      (total, item) => {
        if (item?.deletar)
          return {
            valorBrutoTotal: total.valorBrutoTotal,
            valorLiquidoTotal: total.valorLiquidoTotal
          };

        const valorBase = item.valorUnitario * item.qtde;
        const acrescimo = calcularAjustePorcentagemOuReal(
          item.valorAcrescimo,
          item.tipoValorAcrescimo,
          valorBase
        );
        const desconto = calcularAjustePorcentagemOuReal(
          item.valorDesconto,
          item.tipoValorDesconto,
          valorBase
        );
        const valorLiquido = valorBase + acrescimo - desconto;

        return {
          valorBrutoTotal: total.valorBrutoTotal + valorBase,
          valorLiquidoTotal: total.valorLiquidoTotal + valorLiquido
        };
      },
      { valorBrutoTotal: 0, valorLiquidoTotal: 0 }
    );

    emit('total', total);
  },
  {
    deep: true
  }
);

const columns = computed(() => {
  const array =
    Object.values(attributes.value?.inputs)?.map((input) => input.bind.label) ??
    [];

  if (attributes.value?.totalItem) array.push('Total (R$)');
  if (deletar.value) array.push('');

  return array;
});

const newItem = async (item) => {
  const newItemTemp = attributes.value?.cast
    ? await attributes.value?.cast(item, { assert: false })
    : item;

  model.value = [...model.value, newItemTemp];
};

const isCstCsosnValid = (product) => {
  // const codCstCsosn = product.produtoWithFiscal.codCstCsosn;
  // const crt = companyData.value.empresaWithEmpresaFiscal?.crt;
  //
  // if (crt == 1 || crt == 5) {
  //   if (!codCstCsosn) {
  //     clearText();
  //     notify('O campo CSOSN no cadastro do produto é obrigatório');
  //     return false;
  //   } else if (!isCstCsosn(codCstCsosn, 'csosn')) {
  //     clearText();
  //     notify(
  //       `O CSOSN informado no cadastro do produto é inválido. (${codCstCsosn})`
  //     );
  //     return false;
  //   }
  // } else {
  //   if (!codCstCsosn) {
  //     clearText();
  //     notify('O campo CST no cadastro do produto é obrigatório');
  //     return false;
  //   } else if (!isCstCsosn(codCstCsosn, 'cst')) {
  //     clearText();
  //     notify(
  //       `O CST informado no cadastro do produto é inválido. (${codCstCsosn})`
  //     );
  //     return false;
  //   }
  // }

  return true;
};

const clearText = () => productSearchRef?.value.clearText();

// Abrir tela de cadastro de impostos ao abrir um item, descomentar quando nescessário

// const openRegisterTaxes = async (product, index = null) => {
//   const modalPromise = new Promise((resolve) => {
//     $q.dialog({
//       component: DialogModalLayout,
//       componentProps: {
//         componentRef: Taxes,
//         data: model.value[index] || product,
//         readonly: props.readonly
//       }
//     })
//       .onOk((payload) => {
//         if (!index && index !== 0) {
//           resolve(payload);
//         } else {
//           model.value[index].fiscal = payload?.fiscal;
//           model.value[index].qtde = parseFloat(payload?.qtdeCompra);
//           model.value[index].valorUnitario = payload?.valorUnitario;
//         }
//       })
//       .onCancel(() => resolve(false));
//   });
//   return await modalPromise;
// };

const procuraGradesAdicionadas = async (product, adding = true) => {
  const existingItems = model.value.filter(
    (item) => item.codProduto == (product.controle ?? product.codProduto)
  );

  product.grade =
    product?.grade?.map((grade) => ({
      ...grade,
      codProdutoGrade: grade.controle
    })) ?? [];

  if (!existingItems.length) return product;

  // Itera sobre produtos já adicionados
  product.grade = existingItems.reduce((acc, item) => {
    if (item.codProduto == (product.codProduto ?? product.controle)) {
      // Caso o produto já possua uma grade (buscaram por codigo de barras da grade)
      if (product.grade.length && item.grade.length && adding) {
        // Procura o index dessa grade nas já adicionadas
        const indexGradeExistente = item.grade.findIndex(
          (grade) =>
            grade.cor == product.grade[0]?.cor &&
            grade.tamanho == product.grade[0]?.tamanho
        );

        // Se não encontra, concatena as grades ou então ajusta as quantidades
        if (indexGradeExistente == -1) {
          return acc.concat(product.grade, item.grade);
        } else if (adding) {
          /*
          Se houver quantidade máxima (bloqueia estoque negativo true)
          verifica se a soma das quantidades ultrapassa o limite e ajusta,
          caso contrário, soma normalmente
          */

          const qtde = Number(item.grade[indexGradeExistente].quantidade);
          const quantidadeMaiorQueMaxima =
            product.grade[0]?.quantidadeMaxima &&
            qtde + Number(product.grade[0]?.quantidade) >
              product.grade[0]?.quantidadeMaxima;

          if (quantidadeMaiorQueMaxima) {
            // A soma da adição + existente é superior à máxima
            item.grade[indexGradeExistente].quantidade =
              Number(item.grade[indexGradeExistente].quantidade) +
              Number(product.grade[0]?.quantidadeMaxima) -
              Number(qtde);
          } else {
            item.grade[indexGradeExistente].quantidade =
              Number(item.grade[indexGradeExistente].quantidade) +
              Number(product.grade[0]?.quantidade);
          }
        }
      }
      return acc.concat(item.grade);
    }
  }, []);

  return product;
};

const handleAddItem = async (product, importing = false) => {
  let addingItem = product;
  if (product?.grade?.length) {
    addingItem = await procuraGradesAdicionadas(product);
  } else if (product?.produtoWithGrade?.length) {
    addingItem = await openGradeModal(product);
    if (!addingItem) return clearText();
  }

  addingItem = {
    ...addingItem,
    ...(addingItem?.produtoWithCaracteristica ?? {}),
    ...(addingItem?.produtoWithEstoque ?? {})
  };

  addingItem.codProduto = product?.controle;
  addingItem.valorUnitario = product?.produtoWithEstoque?.precoVenda;
  addingItem.controle = null;
  addingItem.deletar = null;
  addingItem.valorAcrescimo = 0;
  addingItem.tipoValorAcrescimo = 0;
  addingItem.valorDesconto = 0;
  addingItem.tipoValorDesconto = 0;
  addingItem.qtde = importing ? 1 : addingItem.qtde;

  await newItem(addingItem);

  clearText();

  // Scroll para baixo quando adiciona item
  nextTick(() => {
    const elementHeight =
      itemsContainerRef.value?.getBoundingClientRect()?.height;

    if (elementHeight && itemsScrollContainerRef.value) {
      itemsScrollContainerRef.value?.scrollTo(0, elementHeight);
    }
  });
};

const handleImportedProducts = async (items) => {
  for (const item of items) {
    await handleAddItem(item, true);
  }
};

const removeItem = async (modelRow, indexItem) => {
  const confirmed = await confirmDelete();
  if (!confirmed) return;

  if (modelRow.controle) model.value[indexItem].deletar = true;
  else
    model.value = model.value.filter((row, rowIndex) => rowIndex !== indexItem);
};

/**
 * Abre um modal para confirmar a exclusão de um item e atualiza a tabela após a exclusão bem-sucedida.
 * @param {Object} item - O objeto representando o item a ser excluído.
 * @param {string} item.controle - O identificador do item a ser excluído.
 * @param {string} item.razaoSocial - A razão social do item a ser excluído.
 */
const confirmDelete = async () => {
  // Deve perguntar para remover.
  if (!attributes?.value?.confirmToDelete) return true;

  return new Promise((resolve) => {
    $q.dialog({
      component: RegisterDelete
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const openGradeTable = (grade, indexItem) => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      cancelLabel: 'FECHAR',
      componentRef: ItensGrade,
      scope: 'itensGrade',
      classCardSection: 'lg:tw-min-w-[800px]',
      hasCancel: true,
      hasSave: false,
      hasCloseIcon: true,
      title: 'Grade do produto',
      dataModal: {
        grade
      }
    }
  }).onDismiss(() => {
    model.value[indexItem].qtde = grade.reduce(
      (total, grad) => total + Number(grad.quantidade),
      0
    );
  });
};

const openGradeModal = async (item) => {
  let tempItem = { ...item };

  if (tempItem?.produtoWithGrade?.length) {
    // Modal de grade
    await new Promise((resolve) => {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: GradeDevolucao,
          scope: 'gradeDevolucaoModal',
          hasSave: false,
          hasCancel: false,
          dataModal: {
            isModuloDevolucao: true,
            item: { ...tempItem, grade: tempItem.produtoWithGrade }
          }
        }
      })
        .onOk((novaGrade) => {
          tempItem.grade = novaGrade
            .filter((grad) => Number(grad.qtdeDevolvida) > 0)
            .map((grad) => ({
              ...grad,
              codProdutoGrade: grad.controle,
              quantidade: grad.qtdeDevolvida
            }));
          resolve();
        })
        .onCancel(() => {
          tempItem = false;
          resolve();
        });
    });
  }

  return tempItem;
};
</script>

<style scoped>
@screen md {
  .customGrid {
    /* adicionar icone do modal de impostos */
    /* grid-template-columns: minmax(170px, 220px) 80px repeat(3, 140px) 120px 55px; */

    grid-template-columns: minmax(90px, 1fr) 130px 130px repeat(2, 170px) 120px 50px;
  }
}
</style>
