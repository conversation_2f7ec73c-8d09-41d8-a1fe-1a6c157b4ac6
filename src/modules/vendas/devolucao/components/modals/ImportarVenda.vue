<template>
  <SGPage id="table-budget-page">
    <SGCard id="table-budget-page-card">
      <div
        class="tw-mb-1 tw-flex tw-flex-col tw-items-center tw-justify-start sm:tw-flex-row"
      >
        <h1>Importar {{ tipoVenda[props.data.tipoVenda] }}</h1>
      </div>
      <!----- INÍCIO TABELA ----->
      <SGTable
        :store="vendaStore"
        :columns="columnsRef"
        v-model:selected="tableSelectedItems"
        :table-bind="tableBind"
        search-input-column-name="codCliente"
      >
        <template #extra-filter>
          <SGDateFilters :date-fields="dateFields" :store="vendaStore" />
        </template>
      </SGTable>
    </SGCard>
    <ActionBar
      v-if="tableSelectedItems.length !== 0"
      :action-text="selectedLengthText"
      :buttons="actionBarButtons"
      scope="importar-venda-action"
    />
  </SGPage>
</template>

<script setup>
/* eslint-disable */
import ActionBar from 'src/components/actionBar/index.vue';
import SGCard from 'components/generic/SGCard.vue';
import SGPage from 'components/generic/SGPage.vue';
import useImportVendaTable from '../../composables/useImportVendaTable';
import { ref, computed, toRaw } from 'vue';
import { currencyFormat } from 'src/components/utils/currencyFormat';
import { applyEllipsisToString, isDateValid } from 'src/services/utils';
import { Dialog } from 'quasar';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import { useVendaStore } from 'src/stores/api/venda/useVendaStore';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import SGDateFilters from 'src/core/components/SG/Filters/SGDateFilters.vue';
import {
  getDateTimeFromString,
  getFirstAndCurrentDayOfCurrentMonth
} from 'src/components/utils/dates';
import ProdutoOuServicoImportDav from 'src/components/modal/pdv/ProdutoOuServicoImportDav.vue';

// Props do componente.
const props = defineProps({
  data: { type: Object, required: true }
});

// Emits do componente.
const emits = defineEmits(['ok']);
const dateFields = ref([
  {
    label: 'Data da criação',
    field: 'created_at'
  },
  {
    label: 'Data de emissão',
    field: 'vendaWithDfe.dataHoraEmissao'
  }
]);
const vendaStore = useVendaStore();
vendaStore.clearFilters();

// Initial date filter
const { firstDay, lastDay } = getFirstAndCurrentDayOfCurrentMonth();
vendaStore.table.filters = {
  created_at: {
    field: 'created_at',
    filterType: 'DATE_TIME_RANGE',
    filterValue: {
      startDate:
        isDateValid(firstDay) && firstDay <= lastDay
          ? firstDay + ' 00:00:00'
          : '',
      endDate:
        isDateValid(lastDay) && firstDay <= lastDay ? lastDay + ' 23:59:59' : ''
    }
  },
  modulo: {
    filterType: 'EQUALS',
    filterValue: props.data.tipoVenda
  }
};
if (!props.data.isDav) {
  vendaStore.table.filters = {
    ...vendaStore.table.filters,
    tipoEmissao: {
      filterType: 'EQUALS',
      filterValue: props.data.tipoVenda == '3' ? 2 : 1
    },
    'vendaWithDfe.status': {
      filterType: 'EQUALS',
      filterValue: '2'
    }
  };
}

// CASO SEJA TIPO 3 (NOTA MANUAL), ALTERA FILTROS
if (props.data.tipoVenda == '3') {
  dateFields.value = [{ label: '', field: 'created_at' }];
  vendaStore.table.filters = {
    created_at: {
      field: 'created_at',
      filterType: 'DATE_TIME_RANGE',
      filterValue: {
        startDate:
          isDateValid(firstDay) && firstDay <= lastDay
            ? firstDay + ' 00:00:00'
            : '',
        endDate:
          isDateValid(lastDay) && firstDay <= lastDay
            ? lastDay + ' 23:59:59'
            : ''
      }
    },
    modulo: {
      filterType: 'EQUALS',
      filterValue: '1'
    },
    confirmada: {
      filterType: 'EQUALS',
      filterValue: true
    },
    tipoEmissao: {
      filterType: 'EQUALS',
      filterValue: 2
    },
    vendaWithDfe: {
      filterType: 'RELATION_DOESNT_EXIST'
    }
  };
}

if (props.data.tipoVenda == '5') {
  vendaStore.table.filters = {
    ...vendaStore.table.filters,
    cancelada: {
      filterType: 'EQUALS',
      filterValue: false
    }
  };
}

vendaStore.get();

const tipoVenda = {
  1: 'NFC-e',
  2: 'NF-e',
  3: 'nota manual'
};

const tableSelectedItems = ref([]);
const tableBind = ref({
  selection: 'single',
  relatorioRapido: false
});

const getDestinoNota = ['', 'Produção', 'Homologação'];
// Status possiveis da nota fiscal.
const getStatusNota = [
  '',
  'Aberta',
  'Autorizada',
  'Rejeitada',
  'Denegada',
  'Inutilizada',
  'Cancelada'
];

function cellColorStatusOs(row) {
  const cor = row.vendaWithOs?.situacao?.cor ?? '#868686';

  return `color: ${cor};`;
}

function detectarCor(hex, amount = 30) {
  let color = hex.replace('#', '');
  if (color.length === 3) {
    color = color
      .split('')
      .map((c) => c + c)
      .join('');
  }

  const num = parseInt(color, 16);
  let r = (num >> 16) - amount;
  let g = ((num >> 8) & 0x00ff) - amount;
  let b = (num & 0x0000ff) - amount;

  r = Math.max(0, r);
  g = Math.max(0, g);
  b = Math.max(0, b);

  return `rgb(${r}, ${g}, ${b})`;
}

function cellColorStatusBG(row) {
  const backgroundColor = row.vendaWithOs?.situacao?.cor ?? '#ccc';
  const textColor = detectarCor(backgroundColor, 110);

  return `
    display: block;
    width: 100%;
    border-radius: 0.375rem;
    padding: 4px;
    background-color: ${backgroundColor};
    color: ${textColor};
    font-weight: 700;
  text-align: center;
  `;
}

const cellColorStatus = (row) =>
  row?.vendaDevolvida
    ? row?.vendaWithItem?.every((item) => item.qtde === item.qtdeDevolvida)
      ? 'status-cel-red'
      : 'status-cel-yellow'
    : '';

// COLUNAS PADRÃO PARA NFC-e
let columns = [
  {
    name: 'controle',
    field: 'controle',
    required: true,
    label: 'Cód. venda',
    align: 'left',
    sort: 'none',
    filterType: 'EQUALS',
    cellClasses: cellColorStatus
  },
  {
    name: 'vendaWithDfe.sequencia',
    field: (row) => {
      return row?.vendaWithDfe?.status === '3' || !row.vendaWithDfe?.sequencia
        ? '-'
        : row.vendaWithDfe?.sequencia;
    },
    required: true,
    label: 'Nº nota',
    align: 'left',
    sort: 'none',
    filterType: 'EQUALS',
    cellClasses: cellColorStatus
  },
  {
    name: 'vendaWithDfe.chaveAcesso',
    field: (row) => {
      return row?.vendaWithDfe?.status === '3' || !row.vendaWithDfe?.chaveAcesso
        ? ''
        : row.vendaWithDfe?.chaveAcesso;
    },
    required: true,
    label: 'Chave acesso',
    align: 'left',
    sort: 'none',
    filterType: 'EQUALS',
    enableTooltip: true,
    copyToClipboard: true,
    format: (val) => applyEllipsisToString(val, 10) || '-',
    cellClasses: cellColorStatus
  },
  {
    name: 'codCliente',
    field: (row) => row?.pessoa,
    tooltip: (row) => row?.pessoa?.descricaoCodigo,
    format: (row) => row?.descricaoCodigo,
    required: true,
    label: 'Cliente',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE',
    extraFilters: {
      codCliente: {
        filterType: 'ILIKE_OR',
        filterValue: {
          fields: ['pessoa.razaoSocial', 'pessoa.controle']
        }
      }
    },
    cellClasses: cellColorStatus
  },
  {
    name: 'valorLiquido',
    field: 'valorLiquido',
    required: true,
    label: 'Valor líquido',
    align: 'left',
    sort: 'none',
    filterType: 'VALUE_RANGE',
    isMoney: true,
    format: (value) => {
      return 'R$ ' + currencyFormat(value);
    },
    cellClasses: cellColorStatus
  },
  {
    name: 'created_at',
    field: (row) => row.createdAt.substr(0, 10).split('-').reverse().join('/'),
    required: true,
    label: 'Data venda',
    align: 'left',
    sort: 'none',
    cellClasses: cellColorStatus
  },
  {
    name: 'vendaWithDfe.dataHoraEmissao',
    field: (row) => {
      const [date, time] = row?.vendaWithDfe?.dataHoraEmissao?.split(' ') || '';

      const newDate = date?.split('-').reverse().join('/') || '';
      const newTime = time?.split('-')[0];

      const [createdAtDate, createdAtTime] = row?.createdAt?.split(' ') || '';
      const newCreatedAtDate =
        createdAtDate?.split('-').reverse().join('/') || '';

      return newDate
        ? `${newDate} ${newTime}`
        : `${newCreatedAtDate} ${createdAtTime}` || '';
    },
    required: true,
    label: 'Data emissão',
    enableTooltip: true,
    // tooltip: true,
    align: 'left',
    sort: 'none',
    format: (row) => row?.substr(0, 10).split('-').reverse().join('/') || '-',
    cellClasses: cellColorStatus
  },
  {
    name: 'vendaWithDfe.destino',
    field: (row) => {
      return row?.vendaWithDfe?.destino
        ? getDestinoNota[Number(row?.vendaWithDfe?.destino)]
        : '-';
    },
    required: true,
    filterType: 'IN_ARRAY',
    selectType: 'radio',
    label: 'Ambiente',
    cod: 'vendaWithDfe.destino',
    filterOptions: [
      {
        label: 'Produção',
        controle: 1
      },
      {
        label: 'Homologação',
        controle: 2
      }
    ],
    align: 'left',
    sort: 'disabled',
    cellClasses: cellColorStatus
  },
  {
    name: 'saldoFinanceiro',
    field: (row) =>
      row?.vendaDevolvida &&
      row?.vendaWithItem?.some((item) => item.qtde !== item.qtdeDevolvida)
        ? 'SIM'
        : '',
    required: true,
    label: 'Devolvida parcialmente',
    align: 'center',
    classes: `sticky-table-column-with-line !tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-right-36`,
    headerClasses: `sticky-table-column-header-with-line !tw-right-36`,
    cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
    sort: 'disabled',
    cellClasses: cellColorStatus
  },
  {
    name: 'vendaWithDfe?.status',
    field: (row) =>
      row?.vendaDevolvida &&
      row?.vendaWithItem?.every((item) => item.qtde === item.qtdeDevolvida)
        ? 'SIM'
        : 'NÃO',
    required: true,
    label: 'Devolvida totalmente',
    align: 'center',
    sort: 'disabled',
    classes: 'sticky-table-column-with-line !tw-right-[0px]',
    headerClasses: 'sticky-table-column-header-with-line !tw-right-[0px]',
    cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
    cellClasses: cellColorStatus
  }
];

// SE FOR NF-e, ALTERA COLUMNS
if (props.data.tipoVenda == '2') {
  columns = [
    {
      name: 'controle',
      field: 'controle',
      required: true,
      label: 'Cód. venda',
      align: 'left',
      sort: 'none',
      filterType: 'EQUALS',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe.sequencia',
      field: (row) => {
        return row?.vendaWithDfe?.status === '3' || !row.vendaWithDfe?.sequencia
          ? '-'
          : row.vendaWithDfe?.sequencia;
      },
      required: true,
      label: 'Nº nota',
      align: 'left',
      sort: 'none',
      filterType: 'EQUALS',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe.chaveAcesso',
      field: (row) => {
        return row?.vendaWithDfe?.status === '3' ||
          !row.vendaWithDfe?.chaveAcesso
          ? ''
          : row.vendaWithDfe?.chaveAcesso;
      },
      required: true,
      label: 'Chave acesso',
      align: 'left',
      sort: 'none',
      enableTooltip: true,
      copyToClipboard: true,
      filterType: 'EQUALS',
      format: (val) => applyEllipsisToString(val, 10) || '-',
      cellClasses: cellColorStatus
    },
    {
      name: 'codCliente',
      field: (row) => row?.pessoa,
      tooltip: (row) => row?.pessoa?.descricaoCodigo,
      format: (row) => row?.descricaoCodigo,
      required: true,
      label: 'Cliente',
      align: 'left',
      sort: 'none',
      filterType: 'ILIKE',
      extraFilters: {
        codCliente: {
          filterType: 'ILIKE_OR',
          filterValue: {
            fields: ['pessoa.razaoSocial', 'pessoa.controle']
          }
        }
      },
      cellClasses: cellColorStatus
    },
    {
      name: 'valorLiquido',
      field: 'valorLiquido',
      required: true,
      label: 'Valor líquido',
      align: 'left',
      sort: 'none',
      filterType: 'VALUE_RANGE',
      isMoney: true,
      format: (value) => {
        return 'R$ ' + currencyFormat(value);
      },
      cellClasses: cellColorStatus
    },
    {
      name: 'created_at',
      field: (row) =>
        row.createdAt.substr(0, 10).split('-').reverse().join('/'),
      required: true,
      label: 'Data venda',
      align: 'left',
      sort: 'none',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe.dataHoraEmissao',
      field: (row) => {
        const [date, time] =
          row?.vendaWithDfe?.dataHoraEmissao?.split(' ') || '';

        const newDate = date?.split('-').reverse().join('/') || '';
        const newTime = time?.split('-')[0];

        const [createdAtDate, createdAtTime] = row?.createdAt?.split(' ') || '';
        const newCreatedAtDate =
          createdAtDate?.split('-').reverse().join('/') || '';

        return newDate
          ? `${newDate} ${newTime}`
          : `${newCreatedAtDate} ${createdAtTime}` || '';
      },
      required: true,
      label: 'Data emissão',
      enableTooltip: true,
      align: 'left',
      sort: 'none',
      format: (row) => row?.substr(0, 10) || '-',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe.destino',
      field: (row) => {
        return row?.vendaWithDfe?.destino
          ? getDestinoNota[Number(row?.vendaWithDfe?.destino)]
          : '-';
      },
      required: true,
      filterType: 'IN_ARRAY',
      selectType: 'radio',
      label: 'Ambiente',
      cod: 'vendaWithDfe.destino',
      filterOptions: [
        {
          label: 'Produção',
          controle: 1
        },
        {
          label: 'Homologação',
          controle: 2
        }
      ],
      align: 'left',
      sort: 'disabled',
      cellClasses: cellColorStatus
    },
    {
      name: 'devolvidaParcialmente',
      field: (row) =>
        row?.vendaDevolvida &&
        row?.vendaWithItem?.some((item) => item.qtde !== item.qtdeDevolvida)
          ? 'SIM'
          : 'NÃO',
      required: true,
      label: 'Devolvida parcialmente',
      align: 'center',
      classes: `sticky-table-column-with-line !tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-right-36`,
      headerClasses: `sticky-table-column-header-with-line !tw-right-36`,
      cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
      sort: 'disabled',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe?.status',
      field: (row) =>
        row?.vendaDevolvida &&
        row?.vendaWithItem?.every((item) => item.qtde === item.qtdeDevolvida)
          ? 'SIM'
          : 'NÃO',
      required: true,
      label: 'Devolvida totalmente',
      align: 'center',
      sort: 'disabled',
      classes: 'sticky-table-column-with-line !tw-right-[0px]',
      headerClasses: 'sticky-table-column-header-with-line !tw-right-[0px]',
      cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
      cellClasses: cellColorStatus
    }
  ];
}

// SE FOR NOTA MANUAL, ALTERA COLUMNS
if (props.data.tipoVenda == '3') {
  columns = [
    {
      name: 'controle',
      field: 'controle',
      required: true,
      label: 'Cód. venda',
      align: 'left',
      sort: 'none',
      filterType: 'EQUALS',
      cellClasses: cellColorStatus
    },
    {
      name: 'codCliente',
      field: (row) => row?.pessoa,
      tooltip: (row) => row?.pessoa?.descricaoCodigo,
      format: (row) => row?.descricaoCodigo,
      required: true,
      label: 'Cliente',
      align: 'left',
      sort: 'none',
      filterType: 'ILIKE',
      extraFilters: {
        codCliente: {
          filterType: 'ILIKE_OR',
          filterValue: {
            fields: ['pessoa.razaoSocial', 'pessoa.controle']
          }
        }
      },
      cellClasses: cellColorStatus
    },
    {
      name: 'valorLiquido',
      field: 'valorLiquido',
      required: true,
      label: 'Valor líquido',
      align: 'left',
      sort: 'none',
      filterType: 'VALUE_RANGE',
      isMoney: true,
      format: (value) => {
        return 'R$ ' + currencyFormat(value);
      },
      cellClasses: cellColorStatus
    },
    {
      name: 'created_at',
      field: (row) =>
        row.createdAt.substr(0, 10).split('-').reverse().join('/'),
      required: true,
      label: 'Data venda',
      align: 'left',
      sort: 'none',
      cellClasses: cellColorStatus
    },
    {
      name: 'devolvidaParcialmente',
      field: (row) =>
        row?.vendaDevolvida &&
        row?.vendaWithItem?.some((item) => item.qtde !== item.qtdeDevolvida)
          ? 'SIM'
          : 'NÃO',
      required: true,
      label: 'Devolvida parcialmente',
      align: 'center',
      classes: 'sticky-table-column-with-line !tw-right-40',
      headerClasses: 'sticky-table-column-header-with-line !tw-right-40',
      cellClass:
        '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center !tw-mr-[5px]',
      sort: 'disabled',
      cellClasses: cellColorStatus
    },
    {
      name: 'vendaWithDfe?.status',
      field: (row) =>
        row?.vendaDevolvida &&
        row?.vendaWithItem?.every((item) => item.qtde === item.qtdeDevolvida)
          ? 'SIM'
          : 'NÃO',
      required: true,
      label: 'Devolvida totalmente',
      align: 'center',
      sort: 'disabled',
      classes: 'sticky-table-column-with-line !tw-right-[0px]',
      headerClasses: 'sticky-table-column-header-with-line !tw-right-[0px]',
      cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
      cellClasses: cellColorStatus
    }
  ];
}

if (props.data.tipoVenda == '5') {
  columns = [
    {
      name: 'controle',
      field: 'controle',
      required: true,
      label: 'Código',
      sortable: true,
      align: 'left',
      filterType: 'EQUALS',
      filtersV2: true,
      cellStyle: (row) => cellColorStatusOs(row)
    },
    {
      name: 'pessoa.razaoSocial',
      field: (row) => row?.pessoa,
      tooltip: (row) => row?.pessoa?.descricaoCodigo,
      format: (row) => row?.descricaoCodigo,
      label: 'Cliente',
      sortable: true,
      align: 'left',
      filterType: 'ILIKE',
      filtersV2: true,
      group: true,
      cellStyle: (row) => cellColorStatusOs(row),
      extraFilters: [
        {
          field: 'pessoa.razaoSocial',
          filterType: 'ILIKE',
          filterValue: 'pessoa.razaoSocial'
        },
        {
          operator: 'OR',
          field: 'codCliente',
          filterType: 'EQUALS',
          filterValue: 'pessoa.controle'
        }
      ]
    },
    {
      name: 'codFuncionarioResponsavel',
      field: (row) => row?.vendaWithFuncionarioResponsavel,
      tooltip: (row) => row?.vendaWithFuncionarioResponsavel?.descricaoCodigo,
      format: (row) => row?.descricaoCodigo,
      label: 'Funcionário',
      sortable: true,
      align: 'left',
      filterType: 'ILIKE',
      filtersV2: true,
      group: true,
      cellStyle: (row) => cellColorStatusOs(row),
      extraFilters: [
        {
          field: 'vendaWithFuncionarioResponsavel.nome',
          filterType: 'ILIKE',
          filterValue: 'vendaWithFuncionarioResponsavel.nome'
        },
        {
          operator: 'OR',
          field: 'codFuncionarioResponsavel',
          filterType: 'EQUALS',
          filterValue: 'vendaWithFuncionarioResponsavel.controle'
        }
      ]
    },
    {
      name: 'vendaWithCreatedBy.nome',
      field: (row) =>
        row?.vendaWithCreatedBy
          ? `${row.vendaWithCreatedBy?.controle} - ${row.vendaWithCreatedBy?.nome} ${row.vendaWithCreatedBy?.sobrenome}`
          : '-',
      label: 'Usuário',
      sortable: true,
      align: 'left',
      filtersV2: true,
      filterType: 'ILIKE',
      group: true,
      deleteFilterKey: 'vendaWithCreatedBy.nome',
      cellStyle: (row) => cellColorStatusOs(row),
      extraFilters: [
        {
          central: true,
          relationship: 'vendaWithCreatedBy',
          fieldRelation: 'createdBy',
          filtersV2: [
            {
              field: 'nome',
              filterType: 'ILIKE'
            },
            {
              operator: 'OR',
              field: 'controle',
              filterType: 'EQUALS'
            },
            {
              operator: 'OR',
              field: 'sobrenome',
              filterType: 'ILIKE'
            }
          ]
        }
      ]
    },
    {
      name: 'created_at',
      field: (row) =>
        row?.createdAt?.substr(0, 10).split('-').reverse().join('/'),
      tooltip: (row) => getDateTimeFromString(row?.createdAt),
      label: 'Data venda',
      filterType: 'DATE_RANGE',
      cellStyle: (row) => cellColorStatusOs(row),
      sortable: true,
      align: 'left',
      filtersV2: true
    },
    {
      name: 'vendaWithOs.dataHoraEntrega',
      field: (row) => {
        if (row?.vendaWithOs) {
          const hasDataHoraEntrega = row?.vendaWithOs?.dataHoraEntrega;

          if (hasDataHoraEntrega) {
            return new Date(hasDataHoraEntrega).toLocaleDateString('pt-BR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            });
          } else {
            return '-';
          }
        }
        return '-';
      },
      tooltip: (row) =>
        row?.vendaWithOtica?.dataHoraEntrega
          ? formatarDataHoraTimezone(row.vendaWithOtica.dataHoraEntrega)
          : '-',
      label: 'Data/hora entrega',
      filterType: 'DATE_RANGE',
      cellStyle: (row) => cellColorStatusOs(row),
      sortable: true,
      align: 'left',
      filtersV2: true
    },
    {
      name: 'valorLiquido',
      field: (row) => 'R$ ' + currencyFormat(row.valorLiquido, 2),
      label: 'Valor líquido',
      filterType: 'VALUE_RANGE',
      sortable: true,
      align: 'left',
      cellStyle: (row) => cellColorStatusOs(row),
      isMoney: true,
      filtersV2: true
    },
    {
      name: 'vendaWithOs.situacao.descricao',
      field: (row) => {
        const descricao = row?.vendaWithOs?.situacao?.descricao;
        if (!descricao) return '-';
        return (
          descricao.charAt(0).toUpperCase() + descricao.slice(1).toLowerCase()
        );
      },
      label: 'Situação',
      align: 'center',
      sort: 'disabled',
      fixed: true,
      required: true,

      filtersV2: true,
      cellStyle: (row) => cellColorStatusBG(row, '-with-bg')
    }
  ];
}

const columnsRef = ref(columns);
const isImportOsModalOpen = ref(false);

function importarVenda() {
  if (isImportOsModalOpen.value) return;

  isImportOsModalOpen.value = true;

  const possuiServicos = tableSelectedItems.value[0]?.vendaWithItem.some(
    (item) =>
      item?.vendaItemWithProduto?.produtoWithCaracteristica?.codTipoUso == '10'
  );
  if (
    tableSelectedItems.value[0]?.vendaDevolvida &&
    tableSelectedItems.value[0]?.vendaWithItem?.every(
      (item) => item.qtde === item.qtdeDevolvida
    )
  ) {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'vendaDevolvida',
        title: 'Venda devolvida totalmente',
        description:
          'Essa venda foi devolvida totalmente, não é possível importa-la.',
        classCardSection: 'lg:tw-w-[400px]'
      }
    })
      .onOk(() => {
        tableSelectedItems.value = [];
        isImportOsModalOpen.value = false;
      })
      .onCancel(() => {
        isImportOsModalOpen.value = false;
      });
  } else if (
    tableSelectedItems.value[0]?.vendaDevolvida &&
    tableSelectedItems.value[0]?.vendaWithItem?.some(
      (item) => item.qtde !== item.qtdeDevolvida
    )
  ) {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'vendaDevolvida',
        title: 'Venda devolvida parcialmente',
        description:
          'A venda selecionada foi devolvida parcialmente. Deseja importar os itens restantes?',
        classCardSection: 'lg:tw-w-[400px]'
      }
    }).onOk(() => {
      emits('ok', tableSelectedItems.value[0]);
      isImportOsModalOpen.value = false;
    });
  } else if (tableSelectedItems.value[0]?.vendaWithOs) {
    if (possuiServicos && tableSelectedItems.value[0]?.valorPago == '0.00') {
      Dialog.create({
        component: MiniModalLayout,
        componentProps: {
          componentRef: ProdutoOuServicoImportDav,
          scope: 'produto-servico',
          title: 'Importar OS',

          hasCancel: false,
          hasSave: false,
          hasCloseIcon: true,
          dataModal: {
            subTitle: 'Essa OS possui produtos e serviços, deseja importar:'
          }
        }
      })
        .onOk((payload) => {
          emits('ok', {
            controle: toRaw(tableSelectedItems?.value?.[0].controle ?? []),
            importarProdutoServicoOuApenasProduto: payload || null,
            isOs: true,
            refOS: tableSelectedItems?.value
          });
          isImportOsModalOpen.value = false; // Limpa o estado aqui
        })
        .onCancel(() => {
          isImportOsModalOpen.value = false; // Limpa o estado aqui
        });
    } else {
      emits('ok', {
        controle: toRaw(tableSelectedItems?.value?.[0].controle ?? []),
        importarProdutoServicoOuApenasProduto: null,
        isOs: true,
        refOS: tableSelectedItems?.value
      });
      isImportOsModalOpen.value = false;
    }
  } else {
    emits('ok', tableSelectedItems.value[0]);
    isImportOsModalOpen.value = false;
  }
}

// Eventos e respectivos callbacks para a actionbar.
const actionBarEvents = {
  importar: {
    callback: () => importarVenda(tableSelectedItems.value[0])
  },
  cancel: {
    callback: () => (tableSelectedItems.value = [])
  }
};

// Instancia o composable useTable para ativar a actionbar na tabela.
const actionBarButtons = useImportVendaTable({
  params: tableSelectedItems,
  modulo: props.data.tipoVenda,
  events: actionBarEvents
});

// Texto mostrado na actionbar ao selecionar dados da tabela.
const selectedLengthText = computed(() => {
  if (tableSelectedItems.value.length > 1) {
    return `${tableSelectedItems.value.length} registros selecionados`;
  } else {
    return `${tableSelectedItems.value.length} registro selecionado`;
  }
});
</script>
