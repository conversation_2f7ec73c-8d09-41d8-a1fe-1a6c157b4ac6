<template>
  <div>
    <RegisterFormSkeleton :has-nav-menu="false" v-if="isLoadingPage" />

    <NewSGRegisterPage
      v-else
      ref="registerPageRef"
      title="Configurações da DAV"
      id="dav-settings-page"
      :buttons="actionBarButtons"
      :disable-warning-animation="isWarningAnimationDisabled"
      :hide-side-nav="true"
      action-text="Gostaria de salvar?"
    >
      <section class="tw-col-span-full">
        <q-tabs
          v-model="tab"
          dense
          class="tw-col-span-full tw-rounded-t-md tw-text-textsSGBR-gray"
          align="left"
          narrow-indicator
          active-color="primary"
          indicator-color="primary"
        >
          <q-tab
            name="user"
            label="Usuário"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
          />
          <q-tab
            name="company"
            label="Empresa"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
            :disable="!global?.roles?.includes('VENDA.DAV:CONFIGURACAO')"
          >
            <TooltipCustom
              v-if="!global?.roles?.includes('VENDA.DAV:CONFIGURACAO')"
              text-tooltip="Você não possui permissão para alterar as
            configurações da empresa"
            />
          </q-tab>
        </q-tabs>

        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tab"
          animated
          class="tw-col-span-full tw-bg-inherit"
          transition-prev="jump-right"
          transition-next="jump-left"
          :transition-duration="120"
          @transition="tabChange"
        >
          <q-tab-panel
            name="user"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <NewSGCard
              id="sessao-dav-usuario"
              title="Configurações do usuário"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              has-title-separator
              remove-gap
              has-circle
            >
              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Tipo impressão</p>
                <q-option-group
                  v-model="fields.usuario.dav.tipoImpressao.value"
                  :error="!!fields.usuario.dav.tipoImpressao.errorMessage"
                  :error-message="fields.usuario.dav.tipoImpressao.errorMessage"
                  :valid="fields.usuario.dav.tipoImpressao.meta.valid"
                  :options="PRINT_FORMATS"
                  color="primary"
                  size="30px"
                  inline
                  class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <Logotipo
                v-model:fields="fields.usuario.dav"
                :has-default="true"
                class="tw-col-span-full"
              />

              <div
                class="tw-col-span-full tw-flex tw-flex-wrap tw-gap-customGridGapCards"
              >
                <Margins
                  title="Margens impressão (A4)"
                  v-model:fields="fields.usuario.dav"
                  :has-margin-default="true"
                  type="A4"
                />

                <Margins
                  title="Margens impressão térmica (80mm)"
                  v-model:fields="fields.usuario.dav"
                  :has-margin-default="true"
                />

                <Margins
                  title="Margens impressão térmica (58mm)"
                  v-model:fields="fields.usuario.dav"
                  :has-margin-default="true"
                  type="58"
                />
              </div>
            </NewSGCard>

            <NewSGCard
              title="Impressão rápida"
              id="fast-print-integration-dav"
              :schema-errors="errors"
              :error-keys="['usuario.dav.impressaoRapida.impressora']"
              :meta="meta"
              :cols="8"
              remove-gap
              has-circle
              has-title-separator
            >
              <q-checkbox
                @update:model-value="handleEnableFastPrint"
                v-model="
                  fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                    .value
                "
                :error="
                  !!fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :error-message="
                  fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :valid="
                  fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                    .meta.valid
                "
                size="2rem"
                label="Habilitar impressão rápida"
                class="tw-col-span-full"
              />

              <q-checkbox
                v-model="
                  fields.usuario.dav.impressaoRapida.usarPadraoUsuario.value
                "
                :error="
                  !!fields.usuario.dav.impressaoRapida.usarPadraoUsuario
                    .errorMessage
                "
                :error-message="
                  fields.usuario.dav.impressaoRapida.usarPadraoUsuario
                    .errorMessage
                "
                :valid="
                  fields.usuario.dav.impressaoRapida.usarPadraoUsuario.meta
                    .valid
                "
                :disable="
                  !fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                    .value
                "
                size="2rem"
                label="Usar impressão rápida padrão do usuário"
                class="tw-col-span-full tw-mb-4"
              />

              <div
                class="tw-col-span-full tw-flex tw-flex-col tw-gap-4"
                :class="{
                  'tw-pointer-events-none tw-opacity-40':
                    !fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                      .value ||
                    fields.usuario.dav.impressaoRapida.usarPadraoUsuario.value
                }"
              >
                <FastPrintSteps
                  v-model:fields="fields.usuario.dav.impressaoRapida"
                  :is-fast-print-enabled="
                    fields.usuario.dav.impressaoRapida.habilitarImpressaoRapida
                      .value &&
                    !fields.usuario.dav.impressaoRapida.usarPadraoUsuario.value
                  "
                />
              </div>
            </NewSGCard>
          </q-tab-panel>

          <q-tab-panel
            name="company"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <NewSGCard
              title="Configurações gerais"
              id="dav-settings-page-general"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              :has-circle="!readonlyOrDisable"
              has-title-separator
              remove-gap
              container-class="tw-pb-2"
            >
              <q-checkbox
                v-model="fields.gerarEspelhoAposGravar.value"
                :error="!!fields.gerarEspelhoAposGravar.errorMessage"
                :error-message="fields.gerarEspelhoAposGravar.errorMessage"
                :valid="fields.gerarEspelhoAposGravar.meta.valid"
                label="Gerar espelho após gravar"
                class="tw-col-span-full !tw-h-fit !tw-w-fit"
                size="2rem"
                v-authorized="'VENDA.DAV:IMPRIMIR'"
              />

              <q-checkbox
                v-model="fields.habilitarTermoDevolucao.value"
                :error="!!fields.habilitarTermoDevolucao.errorMessage"
                :error-message="fields.habilitarTermoDevolucao.errorMessage"
                :valid="fields.habilitarTermoDevolucao.meta.valid"
                label="Habilitar termos de devolução no condicional"
                class="tw-col-span-full !tw-h-fit !tw-w-fit"
                size="2rem"
              />
            </NewSGCard>
            <EmailConfig
              v-model:fields="fields"
              v-model:errors="errors"
              v-model:values="values"
              :validate="validate"
              :show-default="true"
            />

            <NewSGCard
              title="WhatsApp"
              id="whatsapp-config"
              :cols="12"
              :schema-errors="errors"
              remove-gap
              has-circle
              has-title-separator
            >
              <WhatsApp
                v-model:fields="fields"
                :errors="errors"
                :show-default="true"
                :config-type="5"
                :show-send-message-button="true"
              />
            </NewSGCard>
          </q-tab-panel>
        </q-tab-panels>
      </section>
      <!--  -->
    </NewSGRegisterPage>
  </div>
</template>

<script setup>
import { useQuasar } from 'quasar';
import NewSGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import EmailConfig from 'src/core/components/SG/Email/EmailConfig.vue';
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';
import { ref, toRaw } from 'vue';
import { useRouter } from 'vue-router';

import useRegister from 'components/actionBar/composables/useRegister';
import RegisterFormSkeleton from 'components/generic/skeletons/RegisterFormSkeleton.vue';
import Margins from 'src/core/components/config/Margins.vue';
import { watch, toRefs } from 'vue';
import Logotipo from 'src/core/components/config/Logotipo.vue';
import FastPrintSteps from 'src/modules/navbarUsuario/usuario/config/components/FastPrintSteps.vue';

import { useUserConfigStore } from 'src/modules/navbarUsuario/usuario/config/store/useUserConfigStore';
import { useFormSchemaConfig } from 'src/modules/vendas/dav/config/useFormSchema.js';
import { useDavConfigStore } from 'src/modules/vendas/dav/store/useDavConfigStore.js';
import { useEmailConfigStore } from 'src/modules/vendas/pdv/store/useEmailConfigStore';
import { useGlobal } from 'src/stores/global';

// Props do componente.
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

// Emits do componente.
const emit = defineEmits(['cancel', 'ok']);

const $q = useQuasar();
const router = useRouter();
const global = useGlobal();
const { modal } = toRefs(props);

let editValues = false;
const registerPageRef = ref();
const tab = ref('user');
const isWarningAnimationDisabled = ref(true);
const isLoadingPage = ref(true);

// Stores
const userConfigStore = useUserConfigStore();
const davConfigStore = useDavConfigStore();
const emailConfigStore = useEmailConfigStore();

davConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  }
};

emailConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  },
  tipo: {
    filterValue: 2,
    filterType: 'EQUALS'
  }
};

async function getEmailConfig() {
  if (!global?.roles?.includes('VENDA.DAV:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }
  return emailConfigStore.get();
}

async function getDavConfig() {
  if (!global?.roles?.includes('VENDA.DAV:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }
  return davConfigStore.get();
}

function handleEnableFastPrint(enableFastPrint) {
  if (!enableFastPrint) {
    setFieldValue('usuario.dav.impressaoRapida.usarPadraoUsuario', true);
  }
}

const PRINT_FORMATS = [
  { label: 'A4', value: '1' },
  { label: 'A4 resumida', value: '2' },
  { label: 'Térmica (80mm)', value: '3' },
  { label: 'Térmica resumida (80mm)', value: '4' },
  { label: 'Térmica (58mm)', value: '5' },
  { label: 'Térmica resumida (58mm)', value: '6' }
];

async function getConfig() {
  isLoadingPage.value = true;

  // Requisições em paralelo para buscar as configuracoes (user, pdv e email)
  const [userConfigPromise, davConfigPromise, emailConfigPromise] =
    await Promise.all([
      userConfigStore.get({ deletedAt: false }),
      getDavConfig(),
      getEmailConfig()
    ]);

  isLoadingPage.value = false;

  const { success: userSuccess, data: userConfigData } = userConfigPromise;
  const { success: davSuccess, data: davConfigData } = davConfigPromise;
  const { success: emailSuccess, data: emailConfigData } = emailConfigPromise;

  if (!davSuccess || !emailSuccess || !userSuccess) cancel();

  const { rowsData: userConfigs } = userConfigData;
  const userConfig = userConfigs?.[0] ?? {};

  const { rowsData: emailConfigs } = emailConfigData;
  const { configEmail } = emailConfigs?.[0] ?? {};

  const { rowsData: configs } = davConfigData ?? {};
  const config = configs?.[0] ?? {};

  const { impressaoRapida } = userConfigs?.[0]?.dav ?? {};

  editValues = {
    ...config,
    email: configEmail,
    usuario: {
      ...userConfig,
      dav: {
        ...userConfig.dav,
        impressaoRapida: {
          ...impressaoRapida,
          modulo: 9,
          habilitarImpressaoRapida:
            impressaoRapida?.habilitarImpressaoRapida ?? false,
          usarPadraoUsuario: impressaoRapida?.usarPadraoUsuario ?? true
        }
      }
    }
  };
}

async function tabChange() {
  setTimeout(() => {
    registerPageRef.value.reMountCards('.q-tab-panel');
  }, 255);
}

function resetForm() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    handleReset();
  });
}

function cancel() {
  isWarningAnimationDisabled.value = true;

  if (modal.value) emit('cancel');
  else router.push('/vendas/dav');
}
// Função para salvar os dados do formulário

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

async function sendDavConfigRequest(initialValues, payload) {
  if (!global?.roles?.includes('VENDA.DAV:CONFIGURACAO'))
    return { success: true };

  if (initialValues.controle) {
    return davConfigStore.put({
      payload,
      controle: payload.controle,
      silence: true
    });
  } else {
    return davConfigStore.post({ payload, silence: true });
  }
}

async function sendUserConfigRequest(userPayload) {
  if (userPayload?.createdAt) {
    return userConfigStore.put({
      payload: userPayload,
      silence: true
    });
  }
  return userConfigStore.post({
    payload: userPayload,
    silence: true
  });
}

async function sendEmailConfigRequest(emailPayload) {
  if (!global?.roles?.includes('VENDA.DAV:CONFIGURACAO'))
    return { success: true };
  return await emailConfigStore.put({
    payload: { configEmail: emailPayload },
    silence: true
  });
}

async function save() {
  if (!(await isFormValid())) return;

  let payload = toRaw(values);

  const emailPayload = payload.email;
  const userPayload = {
    ...payload?.usuario,
    impressaoRapida: payload?.usuario?.dav?.impressaoRapida
  };

  delete payload.email;
  delete payload.usuario;

  // Envia as configurações de dav, usuário, e-mail e XML de e-mail em paralelo
  const [davPromise, userPromise, emailPromise] = await Promise.all([
    sendDavConfigRequest(initialValues.value, payload),
    sendUserConfigRequest(userPayload),
    sendEmailConfigRequest(emailPayload)
  ]);

  if (davPromise?.success && userPromise?.success && emailPromise?.success) {
    notify('Dados salvos com sucesso!', 'positive');
    // Forçar recarregamento dos dados antes de fechar
    await getConfig(); // Adicionado
    cancel();
  }
  // Se a operação foi bem-sucedida, desabilita a animação de aviso e redireciona conforme necessário
  if (davPromise?.success && userPromise?.success && emailPromise?.success) {
    notify('Dados salvos com sucesso!', 'positive');
    cancel();
  }
}

// Objeto que mapeia os eventos do actionBar aos seus respectivos callbacks.
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: resetForm
  }
};

//// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});

await getConfig();
const {
  fields,
  values,
  initialValues,
  setFieldValue,
  errors,
  meta,
  isSubmitting,
  validate,
  handleReset
} = useFormSchemaConfig(editValues);

// Desfazer alterações.
watch(
  values,
  async () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
