<template>
  <div class="tw-flex tw-flex-col">
    <div
      class="tw-relative tw-mb-1 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-justify-center tw-py-2"
    >
      <q-icon
        v-if="!props.readonly && $q.screen.gt.sm"
        name="info"
        color="primary"
        size="18px"
        dense
        unelevated
        flat
        padding="2px 2px"
        class="tw-absolute -tw-top-3 tw-right-0"
      >
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -5]"
          text-tooltip="Digite '/' antes do texto para pesquisar por nome."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -40]"
          text-tooltip="Ao digitar R25*1 e pressionar 'Enter', será adicionado 25 reais do produto de código 1."
        />
        <TooltipCustom
          max-width="300px"
          :custom-offset="[-25, -90]"
          text-tooltip="Ao digitar 25*1 e pressionar 'Enter', será adicionado 25 unidades do produto de código 1."
        />
      </q-icon>
      <ProductSelect
        v-if="!props.readonly"
        :valid="true"
        ref="productSearchRef"
        scope="register-register-conditional-page"
        class="productInput tw-w-full"
        :store="stock"
        :loading="false"
        placeholder="Buscar Item"
        :filters="stockFilters"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'nome']"
        :value="stockEditValue"
        :default-search="Number(davConfig?.[0]?.buscaPadrao || 1) ?? 1"
        @searching="(isSearching) => (isTableLoading = isSearching)"
        @imported-products="handleImportedProducts"
        @product-found="handleAddItem"
      />
    </div>
    <!-- inicio inputs -->
    <!-- <div
        class="customScroll tw-flex tw-flex-col tw-items-start tw-justify-start tw-gap-2 tw-overflow-x-auto tw-pb-4"
      >
        <q-virtual-scroll
          :items="itens"
          class="customScroll customScrollItems !tw-max-h-[800px] !tw-min-w-full md:!tw-max-h-[300px]"
          v-slot="{ item, index }"
          :virtual-scroll-slice-size="8"
          v-if="itens.length > 0"
        >
          <div
            :key="index"
            v-if="!itens[index]?.deletar"
            class="customGrid davInputs tw-flex tw-w-full tw-flex-col tw-gap-customGridGapCards tw-gap-y-0 md:tw-grid md:tw-bg-transparent"
          >
            <TooltipCustom v-if="item?.devolvido" text-tooltip="Item devolvido" />
            <q-input
              type="text"
              label="Produto/Serviço"
              :model-value="item.selectData.descricaoCodigo"
              dense
              outlined
              readonly
              stack-label
            />

            <Money
              v-model="item.qtde"
              outlined
              dense
              label="Qtde."
              :max="9999999.9999"
              stack-label
              :decimals-quantity="4"
              :readonly="item?.usaSerial || item?.usaGrade || item?.devolvido"
              @focus="(ev) => handleQuantidadeFocus(ev, item, index)"
              label-slot
              required
              :rules="[
                (val) => (val && val != 0 ? true : 'O campo é obrigatório')
              ]"
              :disabled="props.readonly"
            />

            <Money
              type="text"
              v-model="item.valorUnitario"
              outlined
              required
              dense
              label="Valor unitário"
              class="tw-bg-white"
              :rules="[
                (val) =>
                  val && toFloat(val) > 0 ? true : 'O campo é obrigatório'
              ]"
              :disabled="props.readonly || item?.devolvido"
            />
            <InputPriceOrPercent
              label="Acréscimo"
              :model-value="item.valorAcrescimo"
              :toggle-type="item.tipoValorAcrescimo"
              outlined
              stack-label
              dense
              @update:model-value="
                (value) => {
                  item.valorAcrescimo = value;
                }
              "
              @toggle-click="
                (toggleType) => {
                  item.valorAcrescimo = 0.0;
                  item.tipoValorAcrescimo = toggleType.value;
                }
              "
              :readonly="props.readonly || item?.devolvido"
            />
            <InputPriceOrPercent
              label="Desconto"
              :key="item.valorUnitario"
              :model-value="item.valorDesconto"
              :toggle-type="item.tipoValorDesconto"
              outlined
              stack-label
              dense
              @update:model-value="
                (value) => {
                  item.valorDesconto = value;
                }
              "
              :percentage-rules="descontoPercentageRules"
              :money-rules="[(val) => descontoMoneyRules(val, item)]"
              @toggle-click="
                (toggleType) => {
                  item.valorDesconto = 0.0;
                  item.tipoValorDesconto = toggleType.value;
                }
              "
              :readonly="props.readonly || item?.devolvido"
            />
            <Money
              :model-value="
                round(
                  item.qtde * item.valorUnitario -
                    (item.tipoValorDesconto == '0'
                      ? item.valorDesconto
                      : (item.valorDesconto / 100) *
                        (item.valorUnitario * item.qtde)) +
                    (item.tipoValorAcrescimo == '0'
                      ? item.valorAcrescimo
                      : (item.valorAcrescimo / 100) *
                        (item.valorUnitario * item.qtde)),
                  2
                )
              "
              outlined
              readonly
              dense
              label="Total"
              class="tw-bg-white"
              :disabled="props.readonly || item?.devolvido"
            />
            <div
              class="tw-flex tw-flex-col tw-items-end tw-justify-end tw-gap-2 md:tw-flex-row"
            >
              <q-icon
                v-if="item?.devolvido"
                color="primary"
                name="info"
                unelevated
                dense
                class="tw-relative tw-my-auto !tw-min-h-[26px] tw-w-full tw-justify-center tw-bg-gray-300 tw-leading-5 md:!tw-min-h-full md:tw-w-fit md:tw-max-w-[30px] md:tw-bg-transparent"
                flat
                size="22px"
                padding="0px 5px"
              >
                <TooltipCustom text-tooltip="Item devolvido" />
              </q-icon>
              <q-btn
                v-if="
                  (item?.grade?.length || item?.serial?.length) &&
                  !item?.devolvido
                "
                @click="() => verifySerialGradeLote(item)"
                color="primary"
                :icon="detailIcon(item)"
                unelevated
                dense
                class="tw-relative tw-my-auto !tw-min-h-[26px] tw-w-full tw-justify-center tw-bg-gray-300 tw-leading-5 md:!tw-min-h-full md:tw-w-fit md:tw-max-w-[30px] md:tw-bg-transparent"
                flat
                size="12px"
                padding="0px 5px"
              >
                <TooltipCustom text-tooltip="Detalhes do item" />
              </q-btn>
              <q-btn
                v-if="!props.readonly && !item?.devolvido"
                @click="removeItem(index, itens[index]?.controle)"
                color="red-5"
                icon="delete"
                :label="$q.screen.gt.sm ? '' : 'Remover item'"
                unelevated
                dense
                class="tw-my-auto !tw-min-h-[26px] tw-w-full tw-bg-gray-300 md:!tw-min-h-full md:tw-w-fit md:tw-max-w-[30px] md:tw-bg-transparent"
                flat
                size="12px"
                padding="0px 5px"
              >
                <TooltipCustom
                  text-tooltip="Remover item"
                  class="tw-bg-textsSGBR-gray"
                />
              </q-btn>
            </div>
          </div>
        </q-virtual-scroll>

        <div
          v-if="itens.filter((item) => !item.deletar).length < 1"
          class="tw-flex tw-w-full tw-flex-row tw-flex-wrap tw-items-center tw-justify-center"
        >
          <span class="tw-my-4 tw-font-bold tw-text-SGBRGray">
            Nenhum item adicionado
          </span>
        </div>
      </div> -->
    <!-- fim inputs -->

    <!----- INÍCIO Q-TABLE ----->
    <q-table
      ref="davTableRef"
      :rows="
        Array.isArray(itens.value)
          ? itens.value.filter((item) => !item.deletar)
          : []
      "
      :valid="itens?.meta?.valid"
      :columns="columns"
      :wrap-cells="true"
      :hide-pagination="true"
      :selected-rows-label="() => ''"
      :rows-per-page-options="[0]"
      class="tw-mt-3 tw-grow"
      :table-class="
        itens.value && itens.value.filter((item) => !item.deletar).length > 0
          ? 'pdv-table customScroll tw-text-textsSGBR-gray tw-z-20 relative tw-h-full'
          : 'customScroll'
      "
      table-header-class="tw-font-bold tw-text-xl tw-bg-SGBRGrayBG tw-rounded-xl"
      selection="none"
      row-key="index"
      flat
      dense
    >
      <template #no-data>
        <div
          class="tw-flex tw-w-full tw-items-center tw-justify-center tw-pb-0 tw-pt-7"
        >
          <span class="tw-font-bold tw-text-SGBRGray"
            >Nenhum item adicionado</span
          >
        </div>
      </template>
      <template #header-cell-actions="data">
        <q-th
          :style="{ padding: '0.3rem 0.5rem' }"
          :class="{
            'sticky-table-column-header-with-line !tw-right-0 !tw-w-[40px] !tw-min-w-[40px] tw-text-center':
              data.col.name == 'actions' && !data.col.headerClasses
          }"
          :props="data"
        >
          {{ data.col.label }}
        </q-th>
      </template>
      <!----- INÍCIO CÉLULA QUANTIDADE ----->
      <template #body-cell-qtde="data">
        <q-td
          :props="data"
          class="tw-text-sm"
          style="padding-top: 0px !important"
        >
          <p
            class="tw-min-w-[90px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <Money
              @blur="handleQtdeBlur($event, data)"
              v-model="data.row.qtde"
              :max="99999999.9999"
              :decimals-quantity="4"
              :valid="true"
              :readonly="
                data.row?.usaSerial ||
                data.row?.usaGrade ||
                data.row?.devolvido ||
                props.readonly
              "
              ref="qtdeRef"
              class="tw-text-textsSGBR-gray"
              input-class="tw-text-[12px] tw-text-center tw-p-0"
              hide-bottom-space
              dense
            />
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA QUANTIDADE ----->

      <!----- INÍCIO CÉLULA VALOR UNITÁRIO ----->
      <template #body-cell-valorUnitario="data">
        <q-td
          :props="data"
          class="!tw-min-w-max tw-text-sm"
          style="padding-top: 0px !important"
        >
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <Money
              @blur="handleValorUnitarioBlur($event, data)"
              v-model="data.row.valorUnitario"
              :readonly="props.readonly || data.row?.devolvido"
              :max="99999999.99999"
              :decimals-quantity="global.qtdCasasDecimais"
              currency-display="symbol"
              ref="valorUnitarioRef"
              dense
              :valid="true"
              hide-bottom-space
              class="tw-text-textsSGBR-gray"
              input-class="tw-text-[12px] tw-text-center tw-p-0"
            />
          </p>
        </q-td>
      </template>

      <template #body-cell-valorDesconto="data">
        <q-td
          :props="data"
          class="!tw-min-w-max tw-text-sm"
          style="padding-top: 0px !important"
        >
          <p
            class="tw-min-w-[155px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <InputPricePercent
              v-model="data.row.valorDesconto"
              v-model:toggle-model="data.row.tipoValorDesconto"
              @blur="(ev) => handleBlurDesconto(ev, data)"
              :readonly="props.readonly || data.row?.devolvido || tipoDav == 2"
              currency-display="symbol"
              :max="99999999.99"
              :outlined="false"
              :valid="true"
              :bottom-slots="false"
              toggle-class="tw-h-fit"
              toggle-padding="4px"
              class="no-label tw-p-0"
              input-class="tw-text-center tw-text-[12px] !tw-p-2"
              hide-bottom-space
              show-toggle-on-focus
              no-validation
            />
          </p>
        </q-td>
      </template>

      <template #body-cell-valorAcrescimo="data">
        <q-td
          :props="data"
          class="!tw-min-w-max tw-text-sm"
          style="padding-top: 0px !important"
        >
          <p
            class="tw-min-w-[155px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap"
          >
            <InputPricePercent
              v-model="data.row.valorAcrescimo"
              v-model:toggle-model="data.row.tipoValorAcrescimo"
              currency-display="symbol"
              @blur="(ev) => handleBlurAcrescimo(ev, data)"
              :readonly="props.readonly || data.row?.devolvido || tipoDav == 2"
              :max="99999999.99"
              :outlined="false"
              :valid="true"
              :bottom-slots="false"
              toggle-class="tw-h-fit"
              toggle-padding="4px"
              class="no-label tw-p-0"
              input-class="tw-text-center tw-text-[12px] !tw-p-2"
              hide-bottom-space
              show-toggle-on-focus
              no-validation
            />
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA VALOR UNITÁRIO ----->

      <!----- INÍCIO CÉLULA VALOR TOTAL ----->
      <template #body-cell-valorTotal="data">
        <q-td :props="data" class="!tw-min-w-max tw-text-sm">
          <p
            class="tw-min-w-[115px] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap tw-text-center"
          >
            <span class="">
              {{ 'R$ ' + calcValorTotal(data.row, 2) }}
            </span>
          </p>
        </q-td>
      </template>
      <!----- FIM CÉLULA VALOR TOTAL ----->

      <!----- INÍCIO CÉLULA AÇÕES ----->
      <template #body-cell-actions="data">
        <q-td
          :props="data"
          :class="{
            'sticky-table-column-with-line !tw-right-0 !tw-w-[40px] !tw-min-w-[40px] !tw-px-2 tw-text-center':
              !data.col.classes
          }"
        >
          <div
            class="tw-flex tw-flex-row tw-items-center tw-justify-center tw-gap-2"
          >
            <div
              v-for="action in getFilteredActions(data.row)"
              class="tw-flex tw-flex-row tw-items-center"
              :key="action.name"
            >
              <template v-if="!action.isMenu">
                <component
                  v-if="iconComponents[action.icon]"
                  :is="iconComponents[action.icon]"
                  @click="action.action(data.row, data)"
                  :class="{
                    'is-disabled':
                      action?.condition && !action.condition(data.row, data)
                  }"
                >
                  <TooltipCustom
                    :text-tooltip="action.tooltip"
                    class="tw-bg-textsSGBR-gray"
                  />
                </component>
              </template>
              <template v-else-if="action.menuItems.length">
                <q-icon
                  :name="action.icon"
                  color="primary"
                  size="16px"
                  class="tw-h-[0.9rem] tw-w-[0.9rem] tw-cursor-pointer tw-rounded-[0.3rem] tw-border-2 tw-border-SGBRGrayLighten"
                  @mouseenter="menuOver[data.rowIndex] = true"
                  @mouseleave="menuOver[data.rowIndex] = false"
                >
                  <q-menu
                    :offset="[0, 5]"
                    v-model="menu[data.rowIndex]"
                    @click="
                      menuOver[data.rowIndex] = false;
                      listOver[data.rowIndex] = false;
                    "
                    @mouseenter="listOver[data.rowIndex] = true"
                    @mouseleave="listOver[data.rowIndex] = false"
                    class="tw-z-20 tw-rounded-md"
                  >
                    <q-list style="min-width: 120px">
                      <q-item
                        class="tw-px-2 tw-py-2"
                        v-for="menuItem in action.menuItems"
                        :key="menuItem.name"
                        @click="menuItem.action(data.row, data.rowIndex)"
                        clickable
                        v-close-popup
                      >
                        <q-item-section side>
                          <component :is="iconComponents[menuItem.icon]">
                            <TooltipCustom
                              :text-tooltip="menuItem.tooltip"
                              class="tw-bg-textsSGBR-gray"
                            />
                          </component>
                        </q-item-section>
                        <q-item-section> {{ menuItem.label }}</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-icon>
              </template>
            </div>
          </div>
        </q-td>
      </template>
      <!----- FIM CÉLULA AÇÕES ----->

      <!----- Seleção ----->

      <!----- LOADER ----->
      <template #loading>
        <q-inner-loading showing color="primary" />
      </template>
    </q-table>
    <!----- FIM Q-TABLE ----->
  </div>
</template>

<script setup>
import ProductSelect from 'components/generic/input/ProductSelect.vue';
import GradeVenda from 'components/modal/dav/GradeVenda.vue';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import _ from 'lodash';
import { storeToRefs } from 'pinia';
import { debounce, useQuasar } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import GradeIcon from 'src/components/icons/GradeIcon.vue';
import TrashIcon from 'src/components/icons/TrashIcon.vue';
import { currencyFormat } from 'src/components/utils/currencyFormat';
import notify from 'src/components/utils/notify';
import round from 'src/components/utils/round';
import InputPricePercent from 'src/core/components/Inputs/Money/InputPricePercent.vue';
import { useSalesConfig } from 'src/stores/api/sales/config';
import { useStock } from 'stores/api/stock';
import { ref, shallowRef, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import Money from 'src/core/components/Inputs/Money/InputMoney.vue';
import { useGlobal } from 'src/stores/global';
import EditProductIcon from 'src/components/icons/EditProductIcon.vue';

const global = useGlobal();

const props = defineProps({
  dataItems: { type: Array, default: () => [] },
  btnAddSelect: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  tipoDav: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits([
  'updatedTodo',
  'onClickAdd',
  'propsDone',
  'openInfoAdicional'
]);

const itens = defineModel('itens', {
  type: Array,
  required: true,
  default: () => []
});

const davTableRef = ref();

const $q = useQuasar();
const davConfigStore = useSalesConfig();
const { data: davConfig } = storeToRefs(davConfigStore);
const stockEditValue = ref();

const route = useRoute();
const { params } = route;

// Select de produto
const productSearchRef = ref();
const stock = useStock();
const stockFilters = {
  deleted_at: {
    filterType: 'NULL'
  }
};

const menu = ref(Array(1000).fill(false));
const menuOver = ref(Array(1000).fill(false));
const listOver = ref(Array(1000).fill(false));

watch(
  () => menuOver.value,
  debounce(() => {
    menu.value = menuOver.value.map(
      (over, index) => over || listOver.value[index]
    );
  }, 40),
  { deep: true }
);

watch(
  () => listOver.value,
  debounce(() => {
    menu.value = menuOver.value.map(
      (over, index) => over || listOver.value[index]
    );
  }, 40),
  { deep: true }
);

const iconComponents = shallowRef({
  delete: TrashIcon,
  grade: GradeIcon,
  informacoes: EditProductIcon
});

function transformCurrency(input) {
  return input
    .replaceAll(/R\$\s?/g, '') // Remove the R$ symbol and any spaces
    .replaceAll(/\./g, '') // Replace period with temporary placeholder
    .replaceAll(/,/g, '.'); // Replace comma with period
}

function handleBlurAcrescimo(ev, data) {
  if (ev.target.value === '') {
    data.row.valorAcrescimo = 0;
    return;
  }
}
function handleBlurDesconto(ev, data) {
  if (ev.target.value === '') {
    data.row.valorDesconto = 0;
    return;
  }

  if (data.row.tipoValorDesconto == '1' && round(ev.target.value, 2) > 99.99) {
    data.row.valorDesconto = 99.99;
    notify('Desconto máximo de 99,99%');
  }
  const bruto = round(data.row.qtde * data.row.valorUnitario, 2);
  if (
    data.row.tipoValorDesconto == '0' &&
    Number(transformCurrency(ev.target.value)) > bruto - 0.01
  ) {
    data.row.valorDesconto = round(bruto - 0.01, 2);
    notify('Desconto não pode ser superior ao valor bruto');
  }
}

const handleQtdeBlur = (ev, data) => {
  if (!ev.target.value || ev.target.value === '0,0000') {
    data.row.qtde = 1;
    calcValorUnitario(data.row);
    notify('Não é possível zerar a quantidade de um item');
  }
};

function handleValorUnitarioBlur(ev, data) {
  const valor = ev.target.value?.replaceAll(/R\$\s?/g, '');
  if (!valor || valor === '0,0000') {
    data.row.valorUnitario =
      data.row.itemWithProduto?.produtoWithEstoque?.precoVenda ?? 1;
    notify('Não é possível zerar o valor unitário de um item');
  }
}

const calcValorUnitario = (row) => {
  let valorUnitario = round(round(row.valorBruto, 4) / round(row.qtde, 4), 4);
  if (!valorUnitario) {
    valorUnitario = row.itemWithProduto?.produtoWithEstoque.precoVenda;
  }
  return valorUnitario;
};

function calcValorTotal(item) {
  const bruto = Number(item.qtde) * Number(item.valorUnitario);

  let desconto = 0;
  let acrescimo = 0;

  if (item.tipoValorDesconto === 0) {
    desconto = round(item.valorDesconto ?? 0, 2);
  } else {
    desconto = round((bruto * item.valorDesconto) / 100, 2);
  }

  if (item.tipoValorAcrescimo === 0) {
    acrescimo = round(item.valorAcrescimo, 2);
  } else {
    acrescimo = round((bruto * item.valorAcrescimo) / 100, 2);
  }

  const valorTotal = round(bruto - desconto + acrescimo, 2);
  return currencyFormat(valorTotal, 2);
}

// Objeto que estrutura as colunas da tabela.
const columns = ref([
  {
    name: 'nome',
    align: 'left',
    label: 'Produtos/Serviços*',
    field: (row) => {
      return row?.nome
        ? `${row?.product?.controle} - ${row?.nome}`
        : `${row?.product?.controle} - ${row?.product?.nome}`;
    },
    tooltip: (row) => row?.nome || row.product?.nome,
    sortable: false,
    classes: 'tw-min-w-[220px]'
  },
  {
    name: 'qtde',
    label: 'Qtde.',
    align: 'center'
  },
  {
    name: 'valorUnitario',
    label: 'Valor unitário',
    align: 'center',
    sortable: false,
    field: (row) => row.valorUnitario
  },
  {
    name: 'valorDesconto',
    label: 'Desconto',
    align: 'center',
    sortable: false,
    field: (row) => row.valorDesconto
  },
  {
    name: 'valorAcrescimo',
    label: 'Acréscimo',
    align: 'center',
    sortable: false,
    field: (row) => row.valorAcrescimo
  },
  {
    name: 'valorTotal',
    label: '(R$) Total',
    align: 'center',
    sortable: false,
    field: (row) => {
      return calcValorTotal(row);
    }
  }
]);
if (!props.readonly) {
  columns.value.push({
    name: 'actions',
    field: 'actions',
    required: true,
    label: 'Ações',
    align: 'center'
  });
}

const actions = ref([
  {
    name: 'delete',
    icon: 'delete',
    tooltip: 'Excluir item',
    disableOnCondition: false,
    condition: () => !props.readonly,
    action: (item, data) => {
      removeItem(data.rowIndex, item.controle);
    }
  },
  {
    name: 'more',
    icon: 'more_vert',
    tooltip: '',
    isMenu: true,
    menuItems: [
      {
        name: 'grade',
        icon: 'grade',
        label: 'Grades',
        condition: (row) =>
          (row?.grade?.length || row?.serial?.length) && !row?.devolvido,
        action: (row) => verifySerialGradeLote(row)
      },
      {
        name: 'info',
        icon: 'informacoes',
        label: 'Detalhes produto',
        // condition: () =>
        action: (row) => emit('openInfoAdicional', row)
      }
    ]
  }
]);

const getFilteredActions = (row) => {
  return actions.value
    ?.filter((action) =>
      action.disableOnCondition
        ? true
        : !action.condition || action.condition(row)
    )
    .map((action) => {
      const newAction = { ...action };

      if (newAction.isMenu) {
        newAction.menuItems = action.menuItems.filter(
          (menuItem) => !menuItem.condition || menuItem.condition(row)
        );
      }

      return newAction;
    });
};

/**
 * Cria um novo item na DAV
 */
const newItem = (item) => {
  if (!item) return;

  delete item.controle;
  const itensAtuais = itens.value.value;
  itensAtuais.push(item);
  itens.value.setValue(itensAtuais);
};

const handleImportedProducts = async (items) => {
  for (const item of items) {
    item.produtoWithEstoque.qtde = 1;
    await handleAddItem(item);
  }
};

const handleAddItem = async (product) => {
  if (product.grade?.length === 1) {
    // Cria uma cópia
    const gradeAtualizada = _.cloneDeep(product.grade[0]);
    gradeAtualizada.quantidade = product.produtoWithEstoque?.qtde;
    gradeAtualizada.codProdutoGrade = gradeAtualizada.controle;
    delete gradeAtualizada.controle;

    // Substitui a grade
    product.grade = [gradeAtualizada];
  }

  let addingItem = {};
  (addingItem.index = itens.value.value.length),
    (addingItem.controle = null),
    (addingItem.nome = product?.nome);
  addingItem.qtde = parseFloat(product?.produtoWithEstoque?.qtde) || 1;
  addingItem.valorUnitario = product?.produtoWithEstoque?.precoVenda;
  addingItem.valorAcrescimo = 0;
  addingItem.tipoValorAcrescimo = 0;
  addingItem.valorDesconto = 0;
  addingItem.tipoValorDesconto = 0;
  addingItem.codProduto = product?.controle;
  addingItem.codVenda = params.controle;
  addingItem.product = product;
  // addingItem.selectData = product;
  addingItem.cfopEditValue = product?.produtoWithFiscal?.produtoWithCfop;
  addingItem.cfop = product?.produtoWithFiscal?.produtoFiscalWithCfop?.codCfop;
  addingItem.usaGrade = product?.produtoWithCaracteristica?.usaGrade;
  addingItem.produtoWithGrade = product?.produtoWithGrade;

  // por enquanto não usa serial
  addingItem.usaSerial = false;
  addingItem.usaLote = product?.produtoWithCaracteristica?.usaLote;
  // para depois
  // addingItem.serial = [];
  addingItem.grade = Array.isArray(product?.grade) ? product.grade : [];
  addingItem.lote = [];

  let shouldAddItem = true;

  if (addingItem.grade.length) {
    addingItem = await procuraGradesAdicionadas(addingItem, true, true);
    addingItemRef.value = _.cloneDeep(addingItem);
    handleConfirm('grade');
    shouldAddItem = false;
    productSearchRef?.value.clearText();
  } else if (!addingItem.produtoWithGrade.length && addingItem.usaGrade) {
    // Verifica se produto usa grade e não possui grades disponiveis
    notify('Não há grades em estoque para este item');
    shouldAddItem = false;
  } else {
    shouldAddItem = await verifySerialGradeLote(addingItem);
  }

  if (shouldAddItem) {
    newItem(addingItem);
  }

  productSearchRef?.value.clearText();

  // Scroll para baixo quando adiciona item
  nextTick(() => {
    const childrenHeightOfChildren =
      davTableRef.value?.$el?.children?.[0]?.children?.[0]?.getBoundingClientRect()
        ?.height;

    if (childrenHeightOfChildren) {
      davTableRef.value?.$el?.children?.[0]?.scrollTo(
        0,
        childrenHeightOfChildren
      );
    }
  });
};

const addingItemRef = ref();
// utilizar dps
// const seriaisRemovidos = ref([]);
// const serialStore = useSerial();

/**
 * Verifica a existência de grade serial e lote para o produto adicionado
 * @param product
 * @returns {Promise<boolean>}
 */
const verifySerialGradeLote = async (product) => {
  let shouldAddItem = true;

  // Verifica se já existe esse item com esta grade adicionado
  if (product && product.usaGrade) {
    const res = await procuraGradesAdicionadas(product);
    if (!res) return false;
    addingItemRef.value = _.cloneDeep(res);
    shouldAddItem = false;

    // Modal de grade
    await openGradeModal();
    productSearchRef?.value.clearText();
  }

  productSearchRef?.value?.clearText();
  return shouldAddItem;
};

const procuraGradesAdicionadas = async (product, adding = false) => {
  const existingItems = itens.value.value.filter(
    (item) => item.codProduto == (product.controle ?? product.codProduto)
  );

  // Verifica se há um item igual e verifica itens devolvidos.
  const matchingItemIndex = itens.value.value.findIndex(
    (i) => i.codProduto == product.codProduto
  );

  if (matchingItemIndex > -1) {
    const item = itens.value.value[matchingItemIndex];
    if (item?.devolvido) return item;
    product = await verificaDevolvido(item);
    if (!product) return false;
  }

  if (!existingItems.length) return product;

  // Itera sobre produtos já adicionados
  product.grade = existingItems.reduce((acc, item) => {
    if (item.codProduto == (product.codProduto ?? product.controle)) {
      // Caso o produto já possua uma grade (buscaram por codigo de barras da grade)
      if (product.grade.length && item.grade.length && adding) {
        // Procura o index dessa grade nas já adicionadas
        const indexGradeExistente = item.grade.findIndex(
          (grade) =>
            grade.cor == product.grade[0]?.cor &&
            grade.tamanho == product.grade[0]?.tamanho
        );

        // Se não encontra, concatena as grades ou então ajusta as quantidades
        if (indexGradeExistente == -1) {
          return acc.concat(product.grade, item.grade);
        } else if (adding) {
          /*
            Se houver quantidade máxima (bloqueia estoque negativo true)
            verifica se a soma das quantidades ultrapassa o limite e ajusta,
            caso contrário, soma normalmente
            */
          const qtde = Number(item.grade[indexGradeExistente].quantidade);
          const quantidadeMaiorQueMaxima =
            product.grade[0]?.quantidadeMaxima &&
            qtde + Number(product.grade[0]?.quantidade) >
              product.grade[0]?.quantidadeMaxima;

          if (quantidadeMaiorQueMaxima) {
            // A soma da adição + existente é superior à máxima
            item.grade[indexGradeExistente].quantidade =
              Number(item.grade[indexGradeExistente].quantidade) +
              Number(product.grade[0]?.quantidadeMaxima) -
              Number(qtde);
          } else {
            item.grade[indexGradeExistente].quantidade =
              Number(item.grade[indexGradeExistente].quantidade) +
              Number(product.grade[0]?.quantidade);
          }
        }
      }
      return acc.concat(item.grade);
    }
  }, []);

  return product;
};

const verificaDevolvido = async (product) => {
  if (Number(product?.qtdeDevolvida ?? 0) > 0) {
    const continuar = await modalDevolvido(product);
    if (!continuar) return false;

    product.qtde =
      Number(product?.qtde ?? 0) + Number(product?.qtdeDevolvida ?? 0);
    product.qtdeDevolvida = 0;

    if (product?.usaGrade) {
      product?.grade.forEach((grade) => {
        grade.quantidade =
          Number(grade?.quantidade ?? 0) + Number(grade?.qtdeDevolvida ?? 0);
        grade.qtdeDevolvida = 0;
      });
    }
  }
  return product;
};

/**
 * Abre modal de item parcialmente devolvido.
 * @returns {Promise<unknown>}
 */
const modalDevolvido = async (product) => {
  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        title: 'Item parcialmente devolvido',
        scope: 'modalDevolvido',
        description: `O item ${
          product?.descricaoCodigo || product?.nome
        } possui quantidades já devolvidas, ao prosseguir todas as quantidades devolvidas serão estornadas.`,
        classesTopBox: 'lg:tw-w-[450px]'
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const openGradeModal = async () => {
  // Modal de grade
  await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: GradeVenda,
        hasSave: false,
        hasCancel: false,
        scope: 'grandeVenda',
        dataModal: {
          isPdv: true,
          vendaItem: addingItemRef.value,
          addCallback: (res) => {
            addingItemRef.value.grade = [...addingItemRef.value.grade, res];
          },
          updateCallback: ({ payload, index }) => {
            addingItemRef.value.grade.splice(index, 1, payload);
          },
          removeCallback: (index) => {
            if (addingItemRef.value.grade[index].controle) {
              addingItemRef.value.grade[index].deletar = true;
            } else {
              addingItemRef.value.grade.splice(index, 1);
            }
          }
        }
      }
    })
      .onOk(() => {
        handleConfirm('grade');
        resolve();
      })
      .onCancel(() => resolve());
  });
};

const handleConfirm = (type) => {
  const existingIndex = itens.value.value.findIndex((item) => {
    return item.codProduto == addingItemRef.value.codProduto;
  });

  let qtde = 1;

  if (type === 'grade') {
    qtde = addingItemRef.value[type].reduce((acc, grade) => {
      if (!grade?.deletar) {
        acc += Number(grade.quantidade);
      }
      return acc;
    }, 0);
  }

  const itensAtuais = itens.value.value;

  if (existingIndex === -1) {
    addingItemRef.value.qtde = qtde;
    itensAtuais.push(_.cloneDeep(addingItemRef.value));
    productSearchRef.value.clearText();
  } else {
    itensAtuais[existingIndex][type] = [...addingItemRef.value[type]];
    itensAtuais[existingIndex].qtde = qtde;
    productSearchRef.value.clearText();
  }

  itens.value.setValue(itensAtuais);
  addingItemRef.value = {};
};

/**
 * Remove o item da DAV.
 * @param {number} indexItem
 */
const removeItem = (indexItem, controle = null) => {
  const itensAtuais = itens.value.value;

  if (controle) {
    const item = itensAtuais.find((item) => item.controle === controle);
    if (item) item.deletar = true;
  } else {
    itensAtuais.splice(indexItem, 1);
  }

  itens.value.setValue(itensAtuais);
};

/**
 * Reseta os itens da DAV.
 */
function reset() {
  itens.value.length = 0;
}
//Limpar campos de desconto e acrecimo ao trocar tipo Dav
//Não limpar em modo edição
const clearDiscountsAndAdditions = () => {
  const isEditMode = itens.value.value.some((item) => item.controle);
  if (!isEditMode) {
    const updatedItems = itens.value.value.map((item) => {
      return {
        ...item,
        valorDesconto: 0,
        valorAcrescimo: 0,
        tipoValorDesconto: 0,
        tipoValorAcrescimo: 0
      };
    });
    itens.value.setValue(updatedItems);
  }
};

watch(
  () => props.tipoDav,
  () => {
    clearDiscountsAndAdditions();
  },
  { immediate: true }
);

// /**
//  * Abre modal para verificar itens devolvidos sem grade.
//  */
// const handleQuantidadeFocus = async (ev, item, index) => {
//   ev.preventDefault();
//   ev.stopPropagation();
//   ev.target.blur();

//   if (!Number(item.qtdeDevolvida ?? 0) > 0 || item.devolvido) {
//     ev.target.select();
//     return;
//   }

//   const continuar = await modalDevolvido(item);
//   if (continuar) {
//     itens.[index].qtde =
//       Number(item?.qtde ?? 0) + Number(item?.qtdeDevolvida ?? 0);
//     itens.[index].qtdeDevolvida = 0;
//     ev.target.select();
//   }
// };

defineExpose({ reset });
</script>

<style scoped>
@screen md {
  .customGrid {
    grid-template-columns: minmax(160px, 1fr) 140px 140px repeat(2, 150px) 120px 55px;
    grid-auto-rows: auto;
  }
}
</style>
