<template>
  <div>
    <!----- INÍCIO SKELETON DE FORMULÁRIO  ----->
    <RegisterFormSkeleton v-if="isLoadingPage || loadingXml" />
    <!----- FIM SKELETON DE FORMULÁRIO  ----->
    <NewSGRegisterPage
      v-else
      ref="registerPageRef"
      id="register-conditional-page"
      :focus-on-mounted="false"
      :buttons="actionBarButtons"
      :disable-warning-animation="isWarningAnimationDisabled"
      :modal="modal"
      :title="
        $route.path.includes('editar') && !props.modal
          ? 'Editar documento auxiliar de vendas'
          : readonlyOrDisable
          ? 'Visualizar documento auxiliar de vendas'
          : 'Cadastro de documento auxiliar de vendas'
      "
      :action-text="
        readonlyOrDisable ? 'Visualizando documento auxiliar de vendas' : null
      "
    >
      <NewSGCard
        id="register-dav-register"
        title="Dados cadastrais"
        :schema-errors="errors"
        :meta="meta"
        :cols="9"
        :has-circle="!readonlyOrDisable"
        has-title-separator
        remove-gap
      >
        <InputSelect
          ref="davTipoRef"
          label="Tipo de DAV"
          :model-value="fields.dav.codTipoDav.value"
          :error="!!fields.dav.codTipoDav.errorMessage"
          :error-message="fields.dav.codTipoDav.errorMessage"
          :valid="fields.dav.codTipoDav.meta.valid"
          :refetch-on-change="refetchOnChange"
          behavior="menu"
          v-bind="{
            ...ModelInputSelectSearch,
            params: {
              filters: {
                deleted_at: {
                  filterType: 'NULL'
                }
              },
              orderBy: {
                descricao: 'asc'
              }
            }
          }"
          @on-add="
            () => {
              openRegisterPeople = true;
            }
          "
          :options="[
            { label: 'Orçamento', value: 1 },
            { label: 'Condicional', value: 2 },
            { label: 'Pedido de Venda', value: 3 }
          ]"
          @update:model-value="
            (value) => {
              handleModuloChange({ controle: value });
            }
          "
          option-label="label"
          option-value="value"
          :clearable="false"
          class="tw-col-span-3"
          required
          :readonly="!!controle || readonlyOrDisable"
        />
        <InputSelect
          ref="davSituacaoRef"
          label="Situação"
          v-model="fields.dav.codSituacao.value"
          :error="!!fields.dav.codSituacao.errorMessage"
          :error-message="fields.dav.codSituacao.errorMessage"
          :valid="fields.dav.codSituacao.meta.valid"
          :refetch-on-change="refetchOnChange"
          v-bind="{
            ...ModelInputSelect,
            params: {
              apiRoute: '/api/situacao',
              central: true,
              filters: {
                codTipoDav: {
                  filterType: 'EQUALS',
                  filterValue: values?.dav?.codTipoDav
                },
                visivel: {
                  filterType: 'EQUALS',
                  filterValue: true
                },

                deleted_at: {
                  filterType: 'NULL'
                }
              },
              orderBy: {
                ordem: 'asc'
              }
            }
          }"
          option-label="descricao"
          option-value="controle"
          class="tw-col-span-3"
          :readonly="readonlyOrDisable"
          required
        />

        <InputSelect
          ref="peopleInputRef"
          label="Cliente"
          v-model="fields.venda.codCliente.value"
          :error="!!fields.venda.codCliente.errorMessage"
          :error-message="fields.venda.codCliente.errorMessage"
          :valid="fields.venda.codCliente.meta.valid"
          :refetch-on-change="refetchOnChange"
          :block-editable="['1']"
          v-bind="{
            ...ModelInputSelectSearch,
            params: {
              apiRoute: '/api/pessoa',
              global: true,
              filterOr: ['controle', 'razaosocial', 'cnpjcpf'],
              filters: {
                'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao': {
                  filterType: 'ILIKE',
                  filterValue: 'Cliente'
                },
                deleted_at: {
                  filterType: 'NULL'
                }
              },
              orderBy: {
                controle: 'desc'
              },
              optionDescription: (opt) => {
                if (!opt || !opt?.cnpjCpf) return '';
                return formatCPFCNPJ(opt?.cnpjCpf);
              },
              optionDetails: (opt) => {
                const endereco = opt?.pessoaWithEndereco?.endereco
                  ? opt?.pessoaWithEndereco?.endereco + ', '
                  : '';
                const cidade = opt?.pessoaWithEndereco?.cidade ?? '';
                const uf = opt?.pessoaWithEndereco?.uf
                  ? ' - ' + opt?.pessoaWithEndereco?.uf
                  : '';

                return endereco + cidade + uf;
              }
            }
          }"
          add-button
          edit-button
          @on-add="() => openRegisterPeople()"
          @on-edit="openRegisterPeople"
          role-prefix="PESSOA"
          option-label="descricaoCodigo"
          option-value="controle"
          class="tw-col-span-3"
          :readonly="readonlyOrDisable"
          required
        />
        <InputSelect
          ref="employeeInputRef"
          label="Funcionário responsável"
          v-model="fields.venda.codFuncionarioResponsavel.value"
          :error="!!fields.venda.codFuncionarioResponsavel.errorMessage"
          :error-message="fields.venda.codFuncionarioResponsavel.errorMessage"
          :valid="fields.venda.codFuncionarioResponsavel.meta.valid"
          :refetch-on-change="refetchOnChange"
          v-bind="{
            ...ModelInputSelectSearch,
            params: {
              apiRoute: '/api/funcionario/select',
              filterOr: ['controle', 'nome'],
              filtersV2: [
                {
                  field: 'deleted_at',
                  filterType: 'NULL'
                },
                {
                  not_deleted: true,
                  field: 'codUsuario',
                  filterType: 'FIXED',
                  filterValue: global?.user?.controle
                },
                {
                  not_deleted: true,
                  field: 'controle',
                  filterType: 'FIXED',
                  filterValue: 1
                }
              ],
              orderBy: {
                controle: 'asc'
              }
            }
          }"
          add-button
          @on-add="() => openRegisterEmployee()"
          role-prefix="FUNCIONARIO"
          option-label="descricaoCodigo"
          option-value="controle"
          class="tw-col-span-3"
          :readonly="readonlyOrDisable"
          required
        />
        <DatePicker
          :value="fields.dav.dataValidade.value"
          @value="(val) => fields.dav.dataValidade.setValue(val)"
          :error="!!fields.dav.dataValidade.errorMessage"
          :error-message="fields.dav.dataValidade.errorMessage"
          :valid="fields.dav.dataValidade.meta.valid"
          v-if="values.dav.codTipoDav == 1"
          validate-on-mount
          ref="date1Ref"
          v-bind="ModelInputText"
          :custom-rules="
            () =>
              validateDate(values.dav.dataValidade) ||
              'Validade do orçamento não pode ser menor que a data atual.'
          "
          label="Validade do orçamento"
          class="tw-relative tw-col-span-3"
          :readonly="readonlyOrDisable"
        />

        <DatePicker
          :value="fields.dav.dataDevolucao.value"
          @value="(val) => fields.dav.dataDevolucao.setValue(val)"
          :error="!!fields.dav.dataDevolucao.errorMessage"
          :error-message="fields.dav.dataDevolucao.errorMessage"
          :valid="fields.dav.dataDevolucao.meta.valid"
          v-if="values.dav.codTipoDav == 2"
          ref="date1Ref"
          v-bind="ModelInputText"
          validate-on-mount
          :custom-rules="
            () =>
              validateDate(values.dav.dataDevolucao) ||
              'Data de devolução não pode ser menor que a data atual.'
          "
          label="Data de devolução"
          class="tw-relative tw-col-span-3"
          :readonly="readonlyOrDisable"
        />
        <DatePicker
          :value="fields.dav.dataPrevisaoEntrega.value"
          @value="(val) => fields.dav.dataPrevisaoEntrega.setValue(val)"
          :error="!!fields.dav.dataPrevisaoEntrega.errorMessage"
          :error-message="fields.dav.dataPrevisaoEntrega.errorMessage"
          :valid="fields.dav.dataPrevisaoEntrega.meta.valid"
          v-if="values.dav.codTipoDav == 3 || values.dav.codTipoDav == 2"
          ref="date1Ref"
          v-bind="ModelInputText"
          validate-on-mount
          :custom-rules="
            () =>
              validateDate(values.dav.dataPrevisaoEntrega) ||
              readonlyOrDisable ||
              'Previsão da entrega não pode ser menor que a data atual.'
          "
          :label="
            values.dav.codTipoDav == 3
              ? 'Previsão da entrega'
              : values.dav.codTipoDav == 2 && 'Previsão de devolução'
          "
          class="tw-relative tw-col-span-3"
          :readonly="readonlyOrDisable"
        />
      </NewSGCard>
      <NewSGCard
        id="register-dav-items"
        title="Itens"
        :cols="12"
        container-class="tw-pb-4"
        :schema-errors="errors"
        :meta="meta"
        :has-circle="!readonlyOrDisable"
        has-title-separator
        remove-gap
        :error-keys="['item']"
      >
        <DavItems
          v-model:itens="fields.item"
          :readonly="readonlyOrDisable"
          ref="productInputRefs"
          class="tw-col-span-12"
          @open-info-adicional="(row) => openInfoAdicionalModal(row)"
          :tipo-dav="values?.dav?.codTipoDav"
        />
        <div
          class="tw-col-span-full tw-flex tw-grid-cols-12 tw-flex-col tw-items-end tw-gap-4 lg:tw-grid"
          v-if="hasItems && values?.dav?.codTipoDav != 2"
        >
          <div :class="'tw-col-span-3'"></div>
          <InputPricePercent
            @on-toggle-click="onFreteToggleClick"
            v-model="fields.venda.frete.value"
            v-model:toggle-model="fields.venda.tipoValorFrete.value"
            :error="!!fields.venda.frete.errorMessage"
            :error-message="fields.venda.frete.errorMessage"
            :valid="fields.venda.frete.meta.valid"
            v-if="values?.dav?.codTipoDav == 3 || values?.dav?.codTipoDav == 1"
            class="tw-col-span-3"
            label="Frete"
            stack-label
            v-bind="ModelInputText"
            :disable-percent="true"
            :readonly="readonlyOrDisable"
          />

          <InputPricePercent
            @on-toggle-click="onAcrescimoToggleClick"
            v-model="fields.venda.acrescimo.value"
            v-model:toggle-model="fields.venda.tipoValorAcrescimo.value"
            :error="!!fields.venda.acrescimo.errorMessage"
            :error-message="fields.venda.acrescimo.errorMessage"
            :valid="fields.venda.acrescimo.meta.valid"
            ref="acrescimoInputRef"
            class="tw-col-span-3"
            label="Acréscimo"
            stack-label
            v-bind="ModelInputText"
            :readonly="readonlyOrDisable"
          />

          <InputPricePercent
            v-model="fields.venda.desconto.value"
            v-model:toggle-model="fields.venda.tipoValorDesconto.value"
            :error="!!fields.venda.desconto.errorMessage"
            :error-message="fields.venda.desconto.errorMessage"
            :valid="fields.venda.desconto.meta.valid"
            class="tw-col-span-3"
            label="Desconto"
            stack-label
            ref="descontoInputRef"
            :readonly="readonlyOrDisable"
          />
        </div>

        <ResultTable
          v-if="hasItems && values?.dav?.codTipoDav == 1"
          :principal-text="orcamentoPriceTable?.principalText"
          :labels-and-prices="orcamentoPriceTable?.labelsAndPrices"
        />
        <ResultTable
          v-if="hasItems && values?.dav?.codTipoDav == 2"
          :principal-text="condicionalPriceTable?.principalText"
          :labels-and-prices="condicionalPriceTable?.labelsAndPrices"
        />
        <ResultTable
          v-if="hasItems && values?.dav?.codTipoDav == 3"
          :principal-text="pedidoPriceTable?.principalText"
          :labels-and-prices="pedidoPriceTable?.labelsAndPrices"
        />
      </NewSGCard>

      <NewSGCard
        id="register-dav-obs"
        title="Observações"
        :schema-errors="errors"
        :meta="meta"
        :cols="12"
        :has-circle="!readonlyOrDisable"
        has-title-separator
        remove-gap
      >
        <q-input
          v-model="fields.venda.observacoes.value"
          :error="!!fields.venda.observacoes.errorMessage"
          :error-message="fields.venda.observacoes.errorMessage"
          :valid="fields.venda.observacoes.meta.valid"
          v-bind="ModelInputText"
          type="textarea"
          outlined
          autogrow
          class="tw-col-span-full"
          maxlength="255"
          input-class="tw-text-SGBRGray tw-p-0"
          input-style="min-height: 7rem"
          stack-label
          :readonly="readonlyOrDisable"
          @focus="
            (e) => {
              hotkeys.filter = function (e) {
                var tagName = e.target.tagName;

                if (global.isPlatformMobile) return false;

                return !(tagName == 'TEXTAREA');
              };
            }
          "
          @blur="
            () => {
              hotkeys.filter = () => (global.isPlatformMobile ? false : true);
            }
          "
        />
      </NewSGCard>
    </NewSGRegisterPage>
  </div>
</template>

<script setup>
import DatePicker from 'src/core/components/Inputs/Date/DatePicker.vue';
import InputPricePercent from 'src/core/components/Inputs/Money/InputPricePercent.vue';
import DavItems from 'src/modules/vendas/dav/components/DavItems.vue';
import ResultTable from 'components/generic/items/Total.vue';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import NewSGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import RegisterFormSkeleton from 'components/generic/skeletons/RegisterFormSkeleton.vue';
import RegisterUndo from 'components/modal/global/RegisterUndo.vue';
import { dataAtual } from 'components/utils/dates';
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import { default as RegisterEmployee } from 'src/modules/funcionarios/views/register.vue';
import { storeToRefs } from 'pinia';
import { useQuasar, Dialog } from 'quasar';
import { notifyLoading } from 'src/components/utils/notify';
import round from 'src/components/utils/round';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import RegisterPeople from 'src/modules/pessoa/views/register.vue';
import { returnObjOrNull } from 'src/services/utils';
import { fetchComprovanteDav } from 'src/stores/api/sales/comprovanteDav';
import { useSalesConfig } from 'src/stores/api/sales/config';
import { useGlobal } from 'src/stores/global';
import { useSalesStore } from 'src/modules/vendas/dav/store/useSalesStore';
import { computed, ref, toRaw, toRefs, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useFormSchema } from 'src/modules/vendas/dav/models/useFormSchema.js';
import hotkeys from 'hotkeys-js';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputSelect from 'src/core/models/inputs/Select';
import { formatCPFCNPJ } from 'src/services/utils';
import { diffObjects } from 'src/components/utils/tests';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import InfoAdicionalModal from 'src/modules/vendas/dav/components/modal/InfosAdicionalModal.vue';
import { api } from 'src/boot/axios';
import useDavRegisterActionbar from '../composables/useDavRegisterActionbar';

const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const { modal } = toRefs(props);

const isWarningAnimationDisabled = ref(true);
const isLoadingPage = ref(false);

const $q = useQuasar();
const global = useGlobal();

const router = useRouter();
const route = useRoute();
const { params } = route;
const readonlyOrDisable = ref(route.path.includes('visualizar'));
//store de produto
const refetchOnChange = ref(false);

// Input refs
const davSituacaoRef = ref();
const davSituacaoEditValue = ref();
const peopleInputRef = ref();
const employeeInputRef = ref();
const date1Ref = ref();
const productInputRefs = ref();
const acrescimoInputRef = ref();
const descontoInputRef = ref();
const davTipoRef = ref();

const employeeEditValue = ref();
const gradesRemovidas = ref([]);

const registerPageRef = ref();
// const seriaisRemovidos = ref([]);
// const deletedItems = ref([]);

const DAV_FINALIZADA = ['2', '7', '11', '4', '9', '12'];

const isCondicionalDuplicada = ref(false);

// Instanciar variáveis de edição
const clientEditValue = ref({
  controle: '1',
  razaoSocial: 'CADASTRO PADRÃO',
  descricaoCodigo: '1 - CADASTRO PADRÃO'
});

// Store de sales
const sales = useSalesStore();
const salesConfig = useSalesConfig();
const { data: configData } = storeToRefs(salesConfig);

salesConfig.get(null, true, false);
salesConfig.resetData();

let controle =
  route?.params?.controle ??
  props?.data?.controle ??
  $q.sessionStorage.getItem('cloneId');
let editValues = false;

async function onCreated() {
  if (controle) {
    const { success, data } = await sales.get({ controle });
    const isDuplicating = $q.sessionStorage.getItem('cloneId');
    if (!success) cancel();
    const { rowsData: raw } = data;
    editValues = await treatRawData(raw, isDuplicating);
    if (isDuplicating) {
      controle = null;
      $q.sessionStorage.remove('cloneId');
    }
  } else {
    editValues = {
      venda: {
        codFuncionarioResponsavel: await getDefaultEmployeeValue()
      }
    };
  }
}
async function getDefaultEmployeeValue() {
  let filters = [
    {
      not_deleted: true,
      field: 'codUsuario',
      filterType: 'FIXED',
      filterValue: global?.user?.controle
    },
    {
      not_deleted: true,
      field: 'controle',
      filterType: 'FIXED',
      filterValue: 1
    }
  ];

  const response = await api.get('/api/funcionario/select', {
    params: { filters, paginate: 20, page: 1 }
  });

  return response?.data?.fixed?.controle || '1';
}

async function treatRawData(raw, isDuplicating) {
  if (isDuplicating) {
    delete raw.controle;
  }
  const {
    pessoa: cliente,
    codTipoDav,
    tipoValorFrete,
    tipoValorAcrescimo,
    tipoValorDesconto,
    vendaWithItem: itens,
    vendaWithDav: dav,
    vendaWithFuncionarioResponsavel: funcionario
  } = raw;

  isCondicionalDuplicada.value = dav?.codTipoDav == '2' && isDuplicating;

  redirectIfFinished(isDuplicating, dav);

  let davItems = handleItemStructure(itens, isDuplicating);

  return {
    venda: {
      ...raw,
      cliente,
      funcionario,
      // davSituacao: raw.vendaWithDav?.davWithSituacao,
      tipoValorFrete:
        codTipoDav == 3 || codTipoDav == 1 ? Number(tipoValorFrete) : null,
      tipoValorAcrescimo: codTipoDav != 2 ? Number(tipoValorAcrescimo) : null,
      tipoValorDesconto: codTipoDav != 2 ? Number(tipoValorDesconto) : null
    },

    dav: {
      codTipoDav: dav?.codTipoDav,
      codSituacao: dav?.codSituacao,
      codVenda: controle,
      // codTipoDav: codTipoDav,
      dataPrevisaoEntrega: dav.dataPrevisaoEntrega,
      dataDevolucao: dav.dataDevolucao,
      dataValidade: dav.dataValidade
    },
    item: [...davItems]
  };
}

const handleItemStructure = (itens, isDuplicating) => {
  if (!itens) return [];

  return itens.map((item, index) => {
    let grade = item?.vendaItemWithGrade ?? [];
    if (isDuplicating) grade = handleGradeStructure(grade);

    return {
      ...item,
      controle: isDuplicating ? '' : item.controle,
      codVenda: isDuplicating ? '' : item.controle,
      qtde: isCondicionalDuplicada.value
        ? parseFloat(item?.qtde ?? 0) + parseFloat(item?.qtdeDevolvida ?? 0)
        : parseFloat(item?.qtde ?? 0),
      qtdeDevolvida: isCondicionalDuplicada.value
        ? 0
        : parseFloat(item?.qtdeDevolvida),
      devolvido: isCondicionalDuplicada.value
        ? false
        : Number(item?.qtde ?? 0) <= 0
        ? Number(item?.qtdeDevolvida ?? 0) > 0
        : false,
      // Sempre garantir que grade seja um array, nunca null
      grade: Array.isArray(grade) ? grade : [],
      product: item.vendaItemWithProduto,
      fiscal: item.vendaItemWithFiscal,
      index
    };
  });
};

const handleGradeStructure = (grades) => {
  if (!grades) return [];

  return grades.map((grade) => {
    return {
      ...grade,
      quantidade: isCondicionalDuplicada.value
        ? parseFloat(grade?.quantidade) + parseFloat(grade?.qtdeDevolvida)
        : grade.quantidade,
      qtdeDevolvida: isCondicionalDuplicada.value ? 0 : grade.qtdeDevolvida,
      controle: '',
      codVenda: '',
      codVendaItem: ''
    };
  });
};

const redirectIfFinished = (isDuplicating, dav) => {
  if (
    DAV_FINALIZADA.includes(dav?.codSituacao) &&
    !route.path.includes('visualizar') &&
    !isDuplicating
  ) {
    router.push('/vendas/dav');
  }
};

// Função para validar datePicker
const validateDate = (date) => {
  return (
    Date.parse(date).toString().slice(0, 5) >=
    Date.parse(dataAtual().split('/').reverse().join('-'))
      .toString()
      .slice(0, 5)
  );
};

const updateEmployeeOptions = async () => {
  employeeInputRef.value?.mountSelectOptions();
};

const openRegisterEmployee = () => {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterEmployee,
      scope: 'register-employee'
    }
  }).onOk(async () => {
    await updateEmployeeOptions();
  });
};

const updateDavSituacaoOptions = async () => {
  // davSituacaoRef.value?.mountSelectOptions(situacaoFilters);
  const codTipoDav = fields.value.dav.codTipoDav?.value;

  switch (codTipoDav) {
    case 1:
      davSituacaoEditValue.value = {
        controle: '3',
        descricao: 'ABERTO',
        codTipoDav: '1',
        visivel: true
      };
      setFieldValue('dav.codSituacao', '3');
      break;
    case 2:
      davSituacaoEditValue.value = {
        controle: '8',
        descricao: 'AGUARDANDO RETIRADA',
        codTipoDav: '2',
        visivel: true
      };
      setFieldValue('dav.codSituacao', '8');
      break;
    case 3:
      davSituacaoEditValue.value = {
        controle: '10',
        descricao: 'ABERTO',
        codTipoDav: '3',
        visivel: true
      };
      setFieldValue('dav.codSituacao', '10');
      break;
    default:
      davSituacaoEditValue.value = '';
      setFieldValue('dav.codSituacao', '');
      break;
  }
};

function calcValorTotalItem(item) {
  const bruto = Number(item.qtde) * Number(item.valorUnitario);

  let desconto = 0;
  let acrescimo = 0;

  if (item.tipoValorDesconto === 0) {
    desconto = round(item.valorDesconto ?? 0, 2);
  } else {
    desconto = round((bruto * item.valorDesconto) / 100, 2);
  }

  if (item.tipoValorAcrescimo === 0) {
    acrescimo = round(item.valorAcrescimo, 2);
  } else {
    acrescimo = round((bruto * item.valorAcrescimo) / 100, 2);
  }

  return round(bruto - desconto + acrescimo, 2);
}

// // Regras de validação para desconto em Dinheiro e Porcentagem
// const descontoPercentageRules = [
//   (val) => {
//     return toFloat(val) < 100 || 'Desconto não pode ser maior que 99,9%';
//   }
// ];

/**
 * Verifica os itens originais e os itens atualmente mostrados para mapear removidos.
 */
// const verifyItemsToDelete = () => {
//   if (itens.value.length < initialValues?.item?.length) {
//     const controleSet = new Set(itens.value.map((item) => item.controle));
//     deletedItems.value = initialValues.item.filter(
//       (originalItem) =>
//         originalItem.controle && !controleSet.has(originalItem.controle)
//     );
//   }
// };

//---------------------------------------------------------
// Select de pessoas
//---------------------------------------------------------

const updatePeopleOptions = async () => {
  peopleInputRef.value?.mountSelectOptions();
};

const openRegisterPeople = (controle = null) => {
  let props = {};

  if (controle) {
    props = {
      data: {
        isEditing: true,
        controle
      },
      modal: true
    };
  }

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'register-people',
      ...props
    }
  }).onOk(async () => {
    await updatePeopleOptions();
  });
};

// const handleDavItems = async (formsFinal) => {
//   // Verifica itens removidos para deletar.
//   verifyItemsToDelete();

//   const forms_final = toRaw(formsFinal);
//   const added = itens.value.filter((item) => !item.controle);

//   if (added?.length > 0 || deletedItems.value?.length > 0) {
//     for await (const remove of deletedItems.value) {
//       if (remove.codProduto) {
//         await sales.delete(remove.controle, 'item', true);
//       }
//     }

//     for await (const add of added) {
//       if (add.codProduto && add.qtde > 0 && add.valorDesconto >= 0) {
//         await sales.post(add, 'item', true);
//       }
//     }
//   }

//   forms_final.item = forms_final.item.filter((item) => item.controle);
//   return forms_final;
// };

// Função para cancelar o cadastro e sair da página
function cancel() {
  isWarningAnimationDisabled.value = true;
  if (modal.value) emit('cancel');
  else router.push('/vendas/dav');
}

// Função para resetar o formulário aos valores iniciais
function reset() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(async () => {
    // Caso não tenha nenhuma data, zerar campo
    if (
      initialValues?.dav &&
      !initialValues.dav.dataDevolucao &&
      !initialValues.dav.dataPrevisaoEntrega &&
      !initialValues.dav.dataValidade
    ) {
      date1Ref.value?.reset();
    }

    if (!route.path.includes('editar') && !params.controle) {
      productInputRefs.value?.reset();
    }

    clientEditValue.value = initialValues.venda?.cliente;
    davSituacaoEditValue.value = initialValues.venda?.vendaWithSituacao ?? {
      controle: '3',
      descricao: 'ABERTO',
      codTipoDav: '1',
      visivel: true
    };
    employeeEditValue.value = initialValues?.venda?.funcionario;

    resetForm();
  });
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

//Função para salvar os dados do formulário
async function save() {
  if (!(await isFormValid())) return;
  notifyLoading();

  setValues({
    dav: {
      codTipoDav: values.dav?.codTipoDav
    }
  });

  if (values.dav?.codTipoDav == 1) {
    setValues({
      venda: {
        valorBruto: round(orcamentoPriceTable.value.grossTotalSum, 2),
        valorLiquido: round(orcamentoPriceTable.value.total, 2),
        valorProdutos: round(orcamentoPriceTable.value.productTotalSum, 2),
        valorServicos: round(orcamentoPriceTable.value.serviceTotalSum, 2)
      }
    });
  } else if (values.dav?.codTipoDav == 2) {
    setValues({
      venda: {
        acrescimo: null,
        frete: null,
        desconto: null,
        valorBruto: round(condicionalPriceTable.value.grossTotalSum, 2),
        valorLiquido: round(condicionalPriceTable.value.total, 2),
        valorProdutos: round(condicionalPriceTable.value.productTotalSum, 2),
        valorServicos: round(condicionalPriceTable.value.serviceTotalSum, 2)
      }
    });
  } else if (values.dav?.codTipoDav == 3) {
    setValues({
      venda: {
        valorBruto: round(pedidoPriceTable.value.grossTotalSum, 2),
        valorLiquido: round(pedidoPriceTable.value.total, 2),
        valorProdutos: round(pedidoPriceTable.value.productTotalSum, 2),
        valorServicos: round(pedidoPriceTable.value.serviceTotalSum, 2)
      }
    });
  }

  const formatDate = (date) => {
    if (date) {
      return date.split('/').reverse().join('-');
    } else {
      return '';
    }
  };

  let response;

  /* CASO SEJA ROTA EDITAR */
  if (route.path.includes('editar') && !props.modal) {
    const forms_final = toRaw(values);

    forms_final.item.forEach((item) => {
      if (item.grade) {
        item.grade = [...gradesRemovidas.value, ...item.grade];
      } else {
        // Se grade for null ou undefined, inicializar como array vazio
        item.grade = [];
      }

      // para depois
      // if (item.serial){
      //   item.serial = [...seriaisRemovidos.value, ...item.serial];
      // }

      item.grade = Array.isArray(item.grade) ? item.grade : [];

      // Verificar se a grade contém apenas um objeto vazio [{}]
      if (item.grade.length === 1 && Object.keys(item.grade[0]).length === 0) {
        item.grade = [];
      }

      // para depois
      // item.serial = returnObjOrNull(item.serial);

      item.lote = returnObjOrNull(item.lote);
    });

    const rawForms = toRaw(forms_final);

    const payload = { ...rawForms };

    payload.dav.dataDevolucao = formatDate(payload.dav.dataDevolucao);
    payload.dav.dataPrevisaoEntrega = formatDate(
      payload.dav.dataPrevisaoEntrega
    );
    payload.dav.dataValidade = formatDate(payload.dav.dataValidade);
    response = await sales.put({ payload });
  } else {
    const forms_final = toRaw(values);
    // Garantir que grade nunca seja null para novos registros também
    if (forms_final.item && Array.isArray(forms_final.item)) {
      forms_final.item.forEach((item) => {
        if (item) {
          item.grade = Array.isArray(item.grade) ? item.grade : [];
        }
      });
    }

    const payload = { ...forms_final };

    payload.dav.dataDevolucao = formatDate(payload.dav.dataDevolucao);
    payload.dav.dataPrevisaoEntrega = formatDate(
      payload.dav.dataPrevisaoEntrega
    );
    payload.dav.dataValidade = formatDate(payload.dav.dataValidade);
    response = await sales.post({ payload });
  }

  notifyLoading({ done: true });

  if (response?.success) {
    isWarningAnimationDisabled.value = true;
    if (modal.value) {
      emit('ok');
    } else {
      const controle = response.data.venda.controle;

      // CASO TENHA ESPELHO A GRAVAR COMO CONFIGURAÇÃO
      if (
        configData.value[0]?.gerarEspelhoAposGravar &&
        global.roles?.includes('VENDA.DAV:IMPRIMIR')
      ) {
        fetchComprovanteDav(controle);
      }

      router.push('/vendas/dav');
    }
  }
}

const openInfoAdicionalModal = (row) => {
  const produto = fields.value.item.value[row.index];

  const item = {
    index: row.index,
    controle: produto?.controle,
    produto: produto?.product,
    fiscal: {
      infosAdicionais: produto?.fiscal?.infosAdicionais
    }
  };

  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: InfoAdicionalModal,
      scope: 'Infos-adicionais',
      title: 'Dados do produto',
      classesTopBox: '!tw-justify-start !tw-mb-2 ',
      hasSave: false,
      hasCancel: false,
      dataModal: {
        item
      }
    }
  }).onOk(({ index, controle, data }) => {
    applyItemInfoAdicional(index, controle, data);
  });
};

/**
 * Altera informacoes adicionais do item
 */
const applyItemInfoAdicional = (index, controle, data) => {
  // Itera sobre os itens para encontrar o item correspondente ao controle

  fields.value.item.value.forEach((item, i) => {
    if (i === index && item.controle === controle) {
      if (!item.fiscal) item.fiscal = {};
      item.fiscal.infosAdicionais = data.infosAdicionais;
    }
  });
};

const atalhos = [
  {
    key: 'num_add,=',
    event: () => {
      acrescimoInputRef.value.focusInput();
    }
  },
  {
    key: 'num_subtract,-',
    event: () => {
      descontoInputRef.value.focusInput();
    }
  }
];

// Definir atalhos
useScopedHotkeys(atalhos, 'register-register-conditional-page');

// Definir ações da action bar
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset
  }
};

const actionBarEventsReadOnly = {
  goBack: {
    callback: cancel
  }
};

await onCreated();
const {
  values,
  fields,
  initialValues,
  meta,
  errors,
  resetForm,
  setFieldValue,
  setValues,

  isSubmitting,
  validate
} = useFormSchema(editValues);

// Definir eventos da action bar
const actionBarButtons = useDavRegisterActionbar({
  params: { modal: props.modal, isWarningAnimationDisabled, errors },
  readonly: readonlyOrDisable.value,
  events: readonlyOrDisable.value ? actionBarEventsReadOnly : actionBarEvents
});
// Computed que retorna informações de valores dos itens do orçamento
const orcamentoPriceTable = computed(() => {
  let productTotal = [];
  let serviceTotal = [];
  let grossTotal = [];

  // tipoValor Desconto/Acrescimo/Frete = { 0: Dinheiro, 1: Porcentagem}

  values?.item?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorTotalItem(item);
    }

    const calcGross = item.codProduto
      ? round(Number(item.qtde) * Number(item.valorUnitario), 2)
      : 0;

    // Verificar se é um serviço, cujo controle é igual a 10
    if (item?.product?.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);

      grossTotal.push(calcGross);
    } else {
      productTotal.push(calc);

      grossTotal.push(calcGross);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );
  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  let grossTotalSum = grossTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const itemsTotal = round(productTotalSum + serviceTotalSum, 2);

  const acrescimo = ref(0);
  const desconto = ref(0);
  const frete = ref(0);

  // Calcula o valor do acréscimo
  if (values.venda.tipoValorAcrescimo === 1 && values.venda.acrescimo > 0) {
    const percentage = values.venda.acrescimo / 100;
    acrescimo.value = Number(round(percentage * itemsTotal, 2));
  } else if (
    values.venda.tipoValorAcrescimo === 0 &&
    values.venda.acrescimo > 0
  ) {
    acrescimo.value = values.venda.acrescimo;
  }

  // Calcula o valor do desconto
  if (values.venda.tipoValorDesconto === 1 && values.venda.desconto > 0) {
    const percentage = values.venda.desconto / 100;

    desconto.value = Number(round(percentage * itemsTotal, 2));
  } else if (
    values.venda.tipoValorDesconto === 0 &&
    values.venda.desconto > 0
  ) {
    desconto.value = values.venda.desconto;
  }

  // Calcula o valor do frete
  if (values.venda.tipoValorFrete === 1 && values.venda.frete > 0) {
    const percentage = values.venda.frete / 100;
    frete.value = round(percentage * itemsTotal, 2);
  } else if (values.venda.tipoValorFrete === 0 && values.venda.frete > 0) {
    frete.value = values.venda.frete;
  }

  const total = round(
    itemsTotal + acrescimo.value + frete.value - desconto.value || 0,
    2
  );

  grossTotalSum = round(grossTotalSum, 2);
  setFieldValue('venda.valorLiquido', total);
  setFieldValue('venda.valorBruto', grossTotalSum);
  setFieldValue('venda.valorProdutos', productTotalSum);
  setFieldValue('venda.valorServicos', serviceTotalSum);

  return {
    itemsTotal,
    grossTotalSum,
    productTotalSum,
    serviceTotalSum,
    total,
    principalText: 'Totais do orçamento',
    labelsAndPrices: [
      {
        label: 'Valor dos produtos',
        value: productTotalSum || 0,
        isMoney: true
      },
      {
        label: 'Valor dos serviços',
        value: serviceTotalSum || 0,
        isMoney: true
      },
      { label: 'Valor do frete', value: frete.value, isMoney: true },
      { label: 'Acréscimo', value: acrescimo.value, isMoney: true },
      { label: 'Desconto', value: desconto.value, isMoney: true },
      {
        label: 'Total',
        value: itemsTotal + acrescimo.value + frete.value - desconto.value || 0,
        isMoney: true
      }
    ]
  };
});

// Computed que retorna informações de valores dos itens do pedido de venda
const pedidoPriceTable = computed(() => {
  let productTotal = [];
  let serviceTotal = [];
  let grossTotal = [];

  // tipoValor Desconto/Acrescimo/Frete = { 0: Dinheiro, 1: Porcentagem}
  values?.item?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorTotalItem(item);
    }

    const calcGross = item.codProduto
      ? round(Number(item.qtde) * Number(item.valorUnitario), 2)
      : 0;

    // Verificar se é um serviço, cujo controle é igual a 10
    if (item?.product?.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);

      grossTotal.push(calcGross);
    } else {
      productTotal.push(calc);

      grossTotal.push(calcGross);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );
  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  let grossTotalSum = grossTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const itemsTotal = round(productTotalSum + serviceTotalSum, 2);

  const acrescimo = ref(0);
  const desconto = ref(0);
  const frete = ref(0);

  // Calcula o valor do acréscimo
  if (values.venda.tipoValorAcrescimo === 1 && values.venda.acrescimo > 0) {
    const percentage = values.venda.acrescimo / 100;
    acrescimo.value = round(percentage * itemsTotal, 2);
  } else if (
    values.venda.tipoValorAcrescimo === 0 &&
    values.venda.acrescimo > 0
  ) {
    acrescimo.value = values.venda.acrescimo;
  }

  // Calcula o valor do desconto
  if (values.venda.tipoValorDesconto === 1 && values.venda.desconto > 0) {
    const percentage = values.venda.desconto / 100;
    desconto.value = round(percentage * itemsTotal, 2);
  } else if (
    values.venda.tipoValorDesconto === 0 &&
    values.venda.desconto > 0
  ) {
    desconto.value = values.venda.desconto;
  }

  // Calcula o valor do frete
  if (values.venda.tipoValorFrete === 1 && values.venda.frete > 0) {
    const percentage = values.venda.frete / 100;
    frete.value = round(percentage * itemsTotal, 2);
  } else if (values.venda.tipoValorFrete === 0 && values.venda.frete > 0) {
    frete.value = values.venda.frete;
  }

  const total = round(
    itemsTotal + acrescimo.value + frete.value - desconto.value || 0,
    2
  );
  grossTotalSum = round(grossTotalSum, 2);
  setFieldValue('venda.valorLiquido', total);
  setFieldValue('venda.valorBruto', grossTotalSum);
  setFieldValue('venda.valorProdutos', productTotalSum);
  setFieldValue('venda.valorServicos', serviceTotalSum);

  return {
    itemsTotal,
    grossTotalSum,
    productTotalSum,
    serviceTotalSum,
    total,
    principalText: 'Totais do pedido',
    labelsAndPrices: [
      {
        label: 'Valor dos produtos',
        value: productTotalSum || 0,
        isMoney: true
      },
      {
        label: 'Valor dos serviços',
        value: serviceTotalSum || 0,
        isMoney: true
      },
      { label: 'Valor do frete', value: frete.value, isMoney: true },
      { label: 'Acréscimo', value: acrescimo.value, isMoney: true },
      { label: 'Desconto', value: desconto.value, isMoney: true },
      {
        label: 'Total',
        value: itemsTotal + frete.value + acrescimo.value - desconto.value || 0,
        isMoney: true
      }
    ]
  };
});

// Computed que retorna informações de valores dos itens do condicional
const condicionalPriceTable = computed(() => {
  let productTotal = [];
  let serviceTotal = [];
  let grossTotal = [];
  // tipoValor Desconto/Acrescimo/Frete = { 0: Dinheiro, 1: Porcentagem}

  values?.item?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorTotalItem(item);
    }

    const calcGross = item.codProduto
      ? round(Number(item.qtde) * Number(item.valorUnitario), 2)
      : 0;

    //Verificar se é um serviço, cujo controle é igual a 10
    if (item?.product?.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);

      grossTotal.push(calcGross);
    } else {
      productTotal.push(calc);

      grossTotal.push(calcGross);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );
  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  let grossTotalSum = grossTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const itemsTotal = round(productTotalSum + serviceTotalSum, 2);

  const qtdeTotal =
    values?.item?.length > 0
      ? values?.item
          .map((item) => item.qtde)
          .reduce((ac, current) => parseFloat(ac) + parseFloat(current), 0)
      : 0;

  const total = round(itemsTotal, 2);
  grossTotalSum = round(grossTotalSum, 2);
  setFieldValue('venda.valorLiquido', total);
  setFieldValue('venda.valorBruto', grossTotalSum);
  setFieldValue('venda.valorProdutos', productTotalSum);
  setFieldValue('venda.valorServicos', serviceTotalSum);

  return {
    itemsTotal,
    productTotalSum,
    grossTotalSum,
    serviceTotalSum,
    total: itemsTotal || 0,
    principalText: 'Totais da condicional',
    labelsAndPrices: [
      {
        label: 'Valor dos produtos',
        value: productTotalSum || 0,
        isMoney: true
      },
      {
        label: 'Valor dos serviços',
        value: serviceTotalSum || 0,
        isMoney: true
      },
      { label: 'Qtde de itens', value: qtdeTotal, isMoney: false },
      { label: 'Total', value: itemsTotal || 0, isMoney: true }
    ]
  };
});
// Desfazer alterações.
watch(
  values,
  async () => {
    if (
      (Number(localStorage?.disableUndoEdit) && controle) ||
      (Number(localStorage?.disableUndoCreate) && !controle)
    ) {
      isWarningAnimationDisabled.value = true;
    } else {
      isWarningAnimationDisabled.value = !Object.keys(
        diffObjects(initialValues.value, values)
      ).length;
    }
  },
  {
    immediate: true,
    deep: true
  }
);

const hasItems = computed(() => {
  return fields.value.item.value?.filter((item) => !item.deletar).length > 0;
});

const handleModuloChange = async (module) => {
  if (!fields.value?.dav?.codTipoDav) {
    return;
  }
  const previousModel = fields.value.dav.codTipoDav?.value;

  const hasDiscountOrAddition =
    fields.value.venda.desconto.value !== 0 ||
    fields.value.venda.acrescimo.value !== 0 ||
    fields.value.item.value.some(
      (item) => item.valorDesconto !== 0 || item.valorAcrescimo !== 0
    );

  const shouldAskConfirmation =
    (previousModel === 1 || previousModel === 3) &&
    module.controle === 2 &&
    hasDiscountOrAddition;

  // Remover o foco antes de abrir o modal
  if (davTipoRef.value?.$el) {
    const inputElement = davTipoRef.value.$el.querySelector('input');
    if (inputElement) {
      inputElement.blur();
    }
  }

  if (shouldAskConfirmation) {
    const confirmed = await openModuleChangeConfirmationModal();
    if (!confirmed) {
      // Se o usuário cancelar, manter o valor anterior
      setFieldValue('dav.codTipoDav', previousModel);
      davTipoRef.value?.setModel(previousModel);
      return;
    }
  }

  // Atualizar o tipo de DAV
  setFieldValue('dav.codTipoDav', module.controle);
  // Limpar campos de data
  setFieldValue('dav.dataValidade', null);
  setFieldValue('dav.dataDevolucao', null);
  setFieldValue('dav.dataPrevisaoEntrega', null);

  if (module.controle === 2) {
    setFieldValue('venda.desconto', 0);
    setFieldValue('venda.acrescimo', 0);
    setFieldValue('venda.frete', 0);
    setFieldValue('venda.tipoValorDesconto', 0);
    setFieldValue('venda.tipoValorAcrescimo', 0);
    setFieldValue('venda.tipoValorFrete', 0);

    fields.value.item.value = fields.value.item.value.map((item) => ({
      ...item,
      valorDesconto: 0,
      valorAcrescimo: 0,
      tipoValorDesconto: 0,
      tipoValorAcrescimo: 0
    }));
  } else if (
    (previousModel === 1 && module.controle === 3) ||
    (previousModel === 3 && module.controle === 1)
  ) {
    setFieldValue('venda.desconto', 0);
    setFieldValue('venda.acrescimo', 0);
    setFieldValue('venda.frete', 0);
    setFieldValue('venda.tipoValorDesconto', 0);
    setFieldValue('venda.tipoValorAcrescimo', 0);
    setFieldValue('venda.tipoValorFrete', 0);
  }
  // Atualizar a situação do DAV
  await updateDavSituacaoOptions();
};

const openModuleChangeConfirmationModal = async () => {
  return await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'limpeza-dsconto-acrescimos',
        title: 'Mudança de módulo',
        description:
          'Todos os campos de desconto, acréscimo e frete serão perdidos. Deseja continuar?',
        classCardSection: 'lg:tw-w-[450px]',
        dataModal: {}
      }
    })
      .onOk(() => {
        resolve(true);
      })
      .onCancel(() => {
        resolve(false);
      });
  });
};
</script>
