<template>
  <SGPage id="condicional-page" title="DAV" desc="Documento auxiliar de vendas">
    <template #description>
      <div v-authorized="'VENDA.DAV:CONFIGURACAO'">
        <SGConfigButton
          route="/vendas/dav/configuracoes"
          text-tooltip="Abrir configurações da dav"
        />
      </div>
    </template>

    <template #buttons>
      <template v-if="loading.page">
        <q-skeleton animation="wave" width="9rem" height="2rem" />
        <q-skeleton animation="wave" width="9rem" height="2rem" />
      </template>

      <template v-else>
        <div class="tw-mb-2 tw-flex tw-items-center tw-gap-3">
          <SGButton
            v-authorized="'RELATORIO.MODELO:GERAR'"
            color="primary"
            shortkey="alt+r"
            title="Relatórios"
            @click="handleGerarRelatoriosModal"
          />

          <SGButton
            v-authorized="'VENDA.DAV:VISUALIZAR-HISTORICO-EMAIL'"
            @click="handleHistoricoEmail"
            title="Histórico e-mail"
            :shortkey="`${altOrOption} + H`"
            color="primary"
          />

          <SGRegisterButton
            v-authorized="'VENDA.DAV:CADASTRAR'"
            title="CADASTRAR DAV"
            to="/vendas/dav/cadastro"
          />
        </div>
      </template>
    </template>

    <SGIndexSkeleton v-if="loading.page" />

    <NewSGCard v-else id="condicional-page-table">
      <!----- INÍCIO GRID DE CONTAGEM ----->
      <Totalizadores
        class="tw-grow"
        @summary-click="(val) => handleSummaryGridFilter(val.activeIndex)"
        :store="salesStore"
      />
      <!----- INÍCIO GRID DE CONTAGEM ----->
      <SGTable
        id="davIndex"
        v-model:selected="tableSelectedItems"
        :store="salesStore"
        :columns="columns"
        :actions="tableActions"
        search-input-column-name="pessoa.razaoSocial"
        :table-bind="tableBind"
        :filters-v2="true"
      >
        <template #extra-filter>
          <div class="tw-flex tw-flex-col tw-gap-4">
            <SGDateFilters
              ref="dateFiltersRef"
              :date-fields="dateFields"
              :store="salesStore"
            />

            <div class="-tw-mb-2 tw-flex tw-flex-row tw-items-center">
              <span class="tw-text-xs tw-font-bold">Mostrar:</span>
              <q-btn
                :label="activeLabel"
                class="tw-mx-2 tw-min-w-fit tw-px-3 tw-py-0 tw-text-SGBRGray"
                style="border: 1px solid #cecece"
                flat
                dense
              >
                <q-menu anchor="bottom start">
                  <q-list style="min-width: 100px">
                    <q-item
                      v-for="col in saleTypes"
                      :key="col.value"
                      clickable
                      v-close-popup
                      @click="
                        handleActiveChange({
                          value: col.value,
                          label: col.label
                        })
                      "
                    >
                      <q-item-section>{{ col.label }}</q-item-section>
                    </q-item>
                  </q-list>
                </q-menu>
                <q-icon
                  class="cursor-pointer tw-ml-1"
                  name="expand_more"
                  size="1.2rem"
                />
              </q-btn>
            </div>
          </div>
        </template>
      </SGTable>
      <!----- FIM TABELA ----->
    </NewSGCard>
    <ActionBar
      v-if="tableSelectedItems.length !== 0"
      :action-text="selectedLengthText"
      :buttons="actionBarButtons"
    />

    <MiniModal
      v-if="loadingReceipt"
      v-model="loadingReceipt"
      scope="loading"
      :has-save="false"
      :has-cancel="false"
      :has-close-icon="false"
    >
      <div
        class="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center"
      >
        <q-spinner
          color="primary"
          size="6em"
          class="tw-mb-18 tw-m-24"
          :thickness="5"
        />
        <p class="tw-pb-4 tw-text-sm tw-text-inherit">
          {{ loadingMessage }}
        </p>
      </div>
    </MiniModal>
  </SGPage>
</template>

<script setup>
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import SGPage from 'src/core/components/SG/Page/SGPage.vue';
import SGButton from 'src/core/components/SG/Buttons/SGButton.vue';
import SGRegisterButton from 'components/generic/SGRegisterButton.vue';
import RestoreUser from 'components/modal/global/RestoreUser.vue';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import ActionBar from 'src/components/actionBar/index.vue';
import { useGlobal } from 'stores/global';
import { computed, onMounted, ref, reactive } from 'vue';
import round from 'components/utils/round';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import MiniModal from 'src/components/modal/MiniModal.vue';
import { api } from 'src/boot/axios';
import HistoricoEmailModal from 'src/components/modal/nfe/historicoEmail/HistoricoEmailModal.vue';
import CancelSale from 'src/components/modal/pdv/CancelSale.vue';
import { currencyFormat } from 'src/components/utils/currencyFormat';
import {
  formatToBrazilDate,
  getDateTimeFromString,
  getFirstAndCurrentDayOfCurrentMonth
} from 'components/utils/dates';
import notify from 'src/components/utils/notify';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import SelecionarItens from 'src/modules/vendas/devolucao/components/modals/SelecionarItens.vue';
import handleApiErrors from 'src/services/handleApiErrors';
import { useSendNote } from 'src/stores/api/emailConfig/sendNote.js';
import { fetchComprovanteDav } from 'src/stores/api/sales/comprovanteDav';
import { useRouter } from 'vue-router';
import SGConfigButton from 'src/core/components/SG/Buttons/SGConfigButton.vue';
import SGIndexSkeleton from 'src/core/components/SG/Skeleton/SGIndexSkeleton.vue';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import Totalizadores from 'src/modules/vendas/dav/components/Totalizadores.vue';
import SGDateFilters from 'src/core/components/SG/Filters/SGDateFilters.vue';
import { useSalesStore } from 'src/modules/vendas/dav/store/useSalesStore';
import { isDateValid } from 'src/services/utils';
import useDavTableActionbar from 'src/modules/vendas/dav/composables/useDavTableActionbar.js';
import TabelaModelosRelatorio from 'src/modules/relatorios/modelos/components/TabelaModelosRelatorio.vue';
const global = useGlobal();
const router = useRouter();
const $q = useQuasar();

// Instância do store de venda e resetar os dados
const salesStore = useSalesStore();
salesStore.loading.page = true;
salesStore.resetData();
const { loading } = storeToRefs(salesStore);

// Variável para saber se mostra os atalhos na tela
const { altOrOption } = storeToRefs(global);

// Ids de situação finalizadas
const DAV_FINALIZADAS = ['2', '7', '11', '4', '9', '12'];
const DAV_CANCELADAS = ['14', '15', '16'];
const DAV_SEM_DEVOLUCAO = ['2', '6', '7', '9'];

//refs
const loadingReceipt = ref(false);
const loadingMessage = ref('');

// Filtros inicias e instancia da store de devolucao
let { firstDay, lastDay } = getFirstAndCurrentDayOfCurrentMonth();

const tableBind = ref({
  rowKey: 'controle',
  selection: 'single',
  relatorioRapido: true,
  roleRelatorioRapido: 'VENDA.DAV:GERAR-RELATORIO-RAPIDO',
  class: '!tw-min-h-fit'
});

const saleTypes = ref([
  {
    label: 'Todas',
    value: 'all'
  },
  {
    label: 'Abertas',
    value: 'aberta'
  },
  {
    label: 'Finalizadas',
    value: 'finalizada'
  },
  {
    label: 'Em andamento',
    value: 'andamento'
  },
  {
    label: 'Retiradas',
    value: 'retirada'
  },
  {
    label: 'Devolvidas',
    value: 'devolvida'
  },
  {
    label: 'Parc. devolvida',
    value: 'devolvidaParcial'
  },
  {
    label: 'Aguard. retirada',
    value: 'aguardando'
  },
  {
    label: 'Cancelada',
    value: 'cancelada'
  },
  {
    label: 'Emitida',
    value: 'emitida'
  },
  {
    label: 'Mesclada',
    value: 'mesclada'
  }
]);

const adicionarFiltro = ({ fieldValue, filterType, filterValue }) => {
  salesStore.table.filters = {
    ...salesStore.table.filters,
    [fieldValue]: {
      field: fieldValue,
      filterType,
      filterValue
    }
  };
};

const activeLabel = ref('Todas');
const handleActiveChange = async ({ value, label }) => {
  activeLabel.value = label;

  const MESCLADA_EQUALS_FALSE = {
    fieldValue: 'vendaWithDav.mesclada',
    filterType: 'EQUALS',
    filterValue: false
  };

  delete salesStore.table.filters['vendaWithDav.codSituacao'];
  delete salesStore.table.filters['vendaWithDav.mesclada'];

  if (value === 'aberta') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [3, 10]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'finalizada') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [2, 7]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'andamento') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [1]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'retirada') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [5]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'devolvida') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [6]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'devolvidaParcial') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [13]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'aguardando') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [8]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'cancelada') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [14, 15, 16]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'emitida') {
    adicionarFiltro({
      field: 'vendaWithDav.codSituacao',
      fieldValue: 'vendaWithDav.codSituacao',
      filterType: 'IN_ARRAY',
      filterValue: [4, 9, 12]
    });
    adicionarFiltro(MESCLADA_EQUALS_FALSE);
  } else if (value === 'mesclada') {
    adicionarFiltro({
      field: 'vendaWithDav.mesclada',
      fieldValue: 'vendaWithDav.mesclada',
      filterType: 'EQUALS',
      filterValue: true
    });
  }
};
const isRelatorioModalOpen = ref(false);

const handleGerarRelatoriosModal = async () => {
  if (isRelatorioModalOpen.value) return;

  isRelatorioModalOpen.value = true;
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: TabelaModelosRelatorio,
      scope: 'TabelaModelosRelatorio',
      hasCancel: false,
      hasSave: false,
      title: 'Relatórios - VENDAS',
      classesTopBox: 'lg:tw-w-[1000px]',
      dataModal: {
        modulo: 'App\\Models\\Cliente\\Venda\\Venda'
      }
    }
  })
    .onOk(() => {
      isRelatorioModalOpen.value = false;
    })
    .onCancel(() => {
      isRelatorioModalOpen.value = false;
    });
};

const handleHistoricoEmail = async () => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: HistoricoEmailModal,
      scope: 'email-historico',
      hasCancel: false,
      hasSave: false,
      title: 'Histórico e-mail',
      dataModal: {
        modulo: 'dav'
      }
    }
  });
};

const initialLoading = ref(true);
onMounted(async () => {
  salesStore.loading.page = true;

  salesStore.resetData();

  salesStore.table.filters = {
    modulo: {
      field: 'modulo',
      filterType: 'EQUALS',
      filterValue: '3'
    },
    created_at: {
      field: 'created_at',
      filterType: 'DATE_TIME_RANGE',
      filterValue: {
        startDate:
          isDateValid(firstDay) && firstDay <= lastDay
            ? firstDay + ' 00:00:00'
            : '',
        endDate:
          isDateValid(lastDay) && firstDay <= lastDay
            ? lastDay + ' 23:59:59'
            : ''
      }
    }
  };

  await salesStore.get({ filtersV2: true });

  salesStore.loading.page = false;

  // Remover id de importar Dav caso exista
  $q.sessionStorage?.remove('cloneId');
  initialLoading.value = false;
});

// Ref para armazenar itens selecionados na tabela e referência do store
const tableSelectedItems = ref([]);

const handleViewRegister = () => {
  const controle = tableSelectedItems.value[0]?.controle;
  if (controle) router.push(`/vendas/dav/visualizar/${controle}`);
};

/**
 * Consome API , que retorna binário do PDF e baixa ou imprime.
 * @param {object} row
 * @param {boolean} isDownload
 */
const handleDav = async (row, isDownload = false, eventType = false) => {
  const controle = row?.controle;

  loadingMessage.value = 'Aguarde, gerando impressão...';
  loadingReceipt.value = true;
  await fetchComprovanteDav(controle, isDownload, row?.chaveAcesso, eventType);
  loadingReceipt.value = false;
};

/**
 * Cancela a venda, dado o controle e motivo de cancelamento.
 * @param {string} controle
 * @param {string} motivoCancelamento
 */
const cancelDav = async (controle, motivoCancelamento) => {
  try {
    const response = await api.delete(`/api/venda/cancelar/${controle}`, {
      params: { motivoCancelamento }
    });

    if (response.status === 204) {
      notify('DAV cancelada com sucesso', 'positive');
      tableSelectedItems.value = [];
      updateFinancialAndSaldo();
    }
  } catch (error) {
    handleApiErrors(error);
  }
};

/**
 * Abre modal para seleção dos itens de devolução
 */
const isDevolucaoModalOpen = ref(false);
const devolucaoCondicional = async (row = null) => {
  if (isDevolucaoModalOpen.value) return;

  isDevolucaoModalOpen.value = true;
  const venda = row ?? tableSelectedItems.value?.[0];
  if (!venda) return;

  const loader = $q.notify({
    position: 'top',
    color: 'gray',
    message: 'Carregando...',
    spinner: true
  });

  let itens = null;
  try {
    const response = await api.get(`/api/venda/${venda?.controle}`);
    const { status, data } = response;

    if (status >= 200 && status <= 204) {
      itens = data?.vendaWithItem;
    }
  } catch (error) {
    handleApiErrors(error);
  } finally {
    loader();
  }

  if (!itens) return;

  let itensDevolucao = await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: SelecionarItens,
        scope: 'selectItensDav',
        title: 'Devolução de itens',
        hasCancel: false,
        hasSave: false,
        dataModal: {
          venda,
          itens
        }
      }
    })
      .onOk((itens) => {
        resolve(itens);
        isDevolucaoModalOpen.value = false;
      })
      .onCancel(() => {
        resolve(false);
        isDevolucaoModalOpen.value = false;
      });
  });

  // Verifica se todos os itens foram devolvidos e calcula valor liquido e bruto.
  let { totalQtde, totalQtdeDevolvida } = itensDevolucao.reduce(
    (acc, item) => {
      acc.totalQtde += Number(item.qtde);
      acc.totalQtdeDevolvida += Number(item.qtdeDevolvida);
      return acc;
    },
    { totalQtde: 0, totalQtdeDevolvida: 0 }
  );

  const { totalBrutoItens, diffLiquidoBruto } =
    calculaValorLiquidoItem(itensDevolucao);

  venda.valorBruto = totalBrutoItens;
  venda.valorLiquido = calcValorLiquidoVenda(venda, diffLiquidoBruto);
  venda.valorProdutos = totalBrutoItens + diffLiquidoBruto;

  const payload = {
    venda: { ...venda },
    dav: { codTipoDav: 2 },
    item: [...itensDevolucao]
  };

  // Devolvido / Parc. devolvido / Aguardando retirada
  payload.dav.codSituacao = totalQtdeDevolvida > 0 ? 13 : 5;
  if (totalQtde === 0) payload.dav.codSituacao = 6;

  await salesStore.put({
    payload
  });

  tableSelectedItems.value = [];
  updateFinancialAndSaldo();
};

/**
 * Calcula o valor bruto e líquido de cada item.
 * @param itensDevolucao
 */
const calculaValorLiquidoItem = (itensDevolucao) => {
  if (!itensDevolucao) return 0;

  return itensDevolucao.reduce(
    (acc, item, index) => {
      if (item.serial?.length) {
        itensDevolucao[index]?.serial?.forEach(
          (serial) => (serial.devolvido = true)
        );
      }

      let descontoItem;
      let acrescimoItem;
      const brutoItem = round(
        Number(item.qtde) * Number(item.valorUnitario),
        2
      );

      if (Number(item.tipoValorDesconto) === 0) {
        descontoItem = round(item?.valorDesconto ?? 0, 2);
      } else {
        descontoItem = round(
          (brutoItem * Number(item?.valorDesconto)) / 100,
          2
        );
      }

      if (Number(item.tipoValorAcrescimo) === 0) {
        acrescimoItem = round(item?.valorAcrescimo ?? 0, 2);
      } else {
        acrescimoItem = round(
          (brutoItem * Number(item?.valorAcrescimo)) / 100,
          2
        );
      }

      const liquidoItem = round(brutoItem + acrescimoItem - descontoItem, 2);
      itensDevolucao[index].valorBruto = brutoItem;
      itensDevolucao[index].valorLiquido = liquidoItem;
      acc.totalBrutoItens += brutoItem;
      acc.diffLiquidoBruto += liquidoItem - brutoItem;
      return acc;
    },
    { totalBrutoItens: 0, diffLiquidoBruto: 0 }
  );
};

/**
 * Calcula o valor líquido da venda, considerando o bruto (total líquido dos itens).
 * @param venda
 */
const calcValorLiquidoVenda = (venda, diffLiquidoBruto) => {
  if (!venda) return 0;

  let totalBruto = Number(venda?.valorBruto ?? 0);
  let acrescimo;
  let desconto;

  if (Number(venda?.tipoValorDesconto) === 0) {
    desconto = round(venda?.desconto ?? 0, 2);
  } else {
    desconto = round((totalBruto * Number(venda?.desconto)) / 100, 2);
  }

  if (Number(venda?.tipoValorAcrescimo) === 0) {
    acrescimo = round(venda?.acrescimo ?? 0, 2);
  } else {
    acrescimo = round((totalBruto * Number(venda?.acrescimo)) / 100, 2);
  }

  return round(totalBruto + acrescimo - desconto + diffLiquidoBruto, 2);
};

const sendNote = useSendNote();

function sendEmail(rowControl = null) {
  const items = rowControl
    ? [rowControl]
    : [...tableSelectedItems.value.map((el) => el?.controle)];

  sendNote.post(
    {
      vendas: items,
      modulo: 'dav'
    },
    null,
    null,
    'Solicitação enviada com sucesso!'
  );
}

/**
 * Abre modal para confirmar cancelamento da venda, se confirmado, invoca disableSale para cancelar a venda.
 * @param {object} row
 */

const isCancelModalOpen = ref(false);

const handleCancelDav = async (row) => {
  if (isCancelModalOpen.value) return;

  isCancelModalOpen.value = true;
  if (!row) return;
  // Abre um diálogo de confirmação
  let emitidas = ['4', '9', '12'];
  if (emitidas.includes(row?.vendaWithDav?.codSituacao)) {
    const askConfirmation = await new Promise((res) => {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          title: 'Cancelamento da DAV',
          description:
            'Esta DAV possui notas fiscais emitidas, ao prosseguir com o cancelamento a venda emitida não sofrerá alterações. Deseja realmente prosseguir?',
          classCardSection: 'lg:tw-w-[500px]',
          classesTopBox: '!tw-justify-start !tw-mb-2'
        }
      })
        .onOk(() => {
          res(true);
          isCancelModalOpen.value = false;
        })
        .onCancel(() => {
          res(false);
          isCancelModalOpen.value = false;
        });
    });

    if (!askConfirmation) return;
  }

  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: CancelSale,
      classesTopBox: '!tw-justify-start !tw-mb-2',
      hasCancel: false,
      hasSave: false,
      title: 'Cancelamento da DAV',
      dataModal: { inputLabel: 'Motivo de cancelamento' }
    }
  })
    .onOk((motivoCancelamento) => {
      cancelDav(row.controle, motivoCancelamento);
      isCancelModalOpen.value = false;
    })
    .onCancel(() => {
      isCancelModalOpen.value = false;
    });
};

// Computed para exibir o texto com o total de registros selecionados
const selectedLengthText = computed(() => {
  if (tableSelectedItems.value.length > 1) {
    return `${tableSelectedItems.value.length} registros selecionados`;
  } else {
    return `${tableSelectedItems.value.length} registro selecionado`;
  }
});

// Abrir modal para restaurar conta
const isRestoreModalOpen = ref(false);
const restoreItem = async (controle) => {
  if (isRestoreModalOpen.value) return;

  isRestoreModalOpen.value = true;
  $q.dialog({
    component: RestoreUser,
    componentProps: { controle: controle.deletedAt }
  })
    .onOk(async () => {
      const response = await salesStore.restore(controle);
      if (response) {
        await updateFinancialAndSaldo();
        isRestoreModalOpen.value = false;
      }
    })
    .onCancel(() => {
      isRestoreModalOpen.value = false;
    });
};

// Cleanup ao desmontar o componente
// onUnmounted(() => {
//   salesStore.resetData();
// });

async function updateFinancialAndSaldo() {
  await Promise.all([salesStore.updateRowsPerPage(false, true)]);
}

const handleDuplicateDav = async (rows) => {
  $q.sessionStorage.set('cloneId', rows.controle);
  return router.push('/vendas/dav/cadastro');
};

const hotkeyScope = 'dav-index';
const atalhos = [
  {
    key: 'f11',
    event: () => router.push('/vendas/dav/configuracoes'),
    condition: computed(() => global.roles?.includes('VENDA.DAV:CONFIGURACAO'))
  },
  {
    key: 'f4',
    event: () => router.push('/vendas/dav/cadastro'),
    condition: computed(() => global.roles?.includes('VENDA.DAV:CADASTRAR'))
  },
  {
    key: 'alt+h',
    event: () => handleHistoricoEmail(),
    condition: computed(() =>
      global.roles?.includes('VENDA.DAV:VISUALIZAR-HISTORICO-EMAIL')
    )
  },
  {
    key: 'alt+r',
    event: () => handleGerarRelatoriosModal(),
    condition: computed(() => global.roles?.includes('RELATORIO.MODELO:GERAR'))
  }
];
// Define os atalhos do site
useScopedHotkeys(atalhos, hotkeyScope);
// Ações da tabela
const tableActions = ref([
  {
    name: 'edit',
    icon: {
      name: 'img:/icons/edit.svg'
    },
    tooltip: 'Editar dav',
    disableOnCondition: true,
    condition: (row) => {
      const { codTipoDav, mesclada, codSituacao } = row.vendaWithDav;

      return (
        global.roles?.includes('VENDA.DAV:EDITAR') &&
        row.deletedAt === null &&
        (codTipoDav == 2 ? codSituacao != 6 : true) &&
        !DAV_FINALIZADAS.includes(codSituacao) &&
        !DAV_CANCELADAS.includes(codSituacao) &&
        !mesclada
      );
    },

    action: (row) => router.push(`/vendas/dav/editar/${row.controle}`)
  },
  {
    name: 'more',
    icon: 'more_vert',
    tooltip: '',
    isMenu: true,
    menuItems: [
      {
        name: 'visualizar',
        icon: {
          name: 'search'
        },
        label: 'Visualizar',
        action: (row) => router.push('/vendas/dav/visualizar/' + row.controle),
        condition: (row) =>
          global.roles?.includes('VENDA.DAV:VISUALIZAR') &&
          !row?.vendaWithDav?.mesclada
      },
      {
        name: 'duplicate',
        icon: {
          name: 'img:/icons/difference.svg'
        },
        label: 'Duplicar',
        condition: (row) =>
          !row.deletedAt &&
          !DAV_CANCELADAS.includes(row?.vendaWithDav?.codSituacao) &&
          global.roles?.includes('VENDA.DAV:DUPLICAR'),
        action: (row) => handleDuplicateDav(row)
      },
      {
        name: 'enviarEmail',
        icon: {
          name: 'email'
        },
        label: 'Enviar e-mail',
        condition: (row) =>
          !row.deletedAt &&
          !DAV_CANCELADAS.includes(row?.vendaWithDav?.codSituacao) &&
          global.roles?.includes('VENDA.DAV:ENVIAR-EMAIL') &&
          !row?.vendaWithDav?.mesclada,
        action: (row) => sendEmail(row.controle)
      },
      {
        name: 'devolucao',
        icon: {
          name: 'swap_horiz'
        },
        label: 'Devolução',
        condition: (row) =>
          !DAV_CANCELADAS.includes(row?.vendaWithDav?.codSituacao) &&
          !DAV_SEM_DEVOLUCAO.includes(row?.vendaWithDav?.codSituacao) &&
          row.vendaWithDav?.codTipoDav == 2 &&
          global.roles?.includes('VENDA.DAV:DEVOLVER') &&
          !row?.vendaWithDav?.mesclada,
        action: (row) => devolucaoCondicional(row)
      },
      {
        name: 'print',
        icon: {
          name: 'print'
        },
        label: 'Imprimir',
        condition: (row) =>
          !row.deletedAt &&
          !DAV_CANCELADAS.includes(row?.vendaWithDav?.codSituacao) &&
          global.roles?.includes('VENDA.DAV:IMPRIMIR') &&
          !row?.vendaWithDav?.mesclada,
        action: (row) => handleDav(row)
      },
      {
        name: 'download',
        icon: {
          name: 'download'
        },
        label: 'Baixar',
        condition: (row) =>
          !row.deletedAt &&
          !DAV_CANCELADAS.includes(row?.vendaWithDav?.codSituacao) &&
          global.roles?.includes('VENDA.DAV:BAIXAR') &&
          !row?.vendaWithDav?.mesclada,
        action: (row) => handleDav(row, true)
      },
      {
        name: 'restore',
        icon: {
          name: 'img:/icons/sync.svg',
          class: 'tw-bg-tablesSGBR-lightGreen tw-rounded-[4px]'
        },
        label: 'Reativar',
        condition: (row) =>
          row.deletedAt !== null &&
          global.roles?.includes('VENDA.DAV:RESTAURAR'),
        action: (row) => restoreItem(row)
      },
      {
        name: 'cancel',
        icon: {
          name: 'cancel',
          color: 'negative'
        },
        label: 'Cancelar',
        condition: (row) =>
          row.modulo == '3' &&
          !row.deletedAt &&
          !['14', '15', '16'].includes(row?.vendaWithDav?.codSituacao) &&
          global.roles?.includes('VENDA.DAV:CANCELAR') &&
          !row?.vendaWithDav?.mesclada,
        action: (row) => handleCancelDav(row)
      }
    ]
  }
]);

function cellColorStatus(row, withBg = '') {
  const status = row?.vendaWithDav?.codSituacao;

  const MESCLADA = row?.vendaWithDav?.mesclada;
  const DAV_FINALIZADAS = ['2', '7', '11', '4', '9', '12'].includes(status);
  const DAV_CANCELADAS = ['14', '15', '16'].includes(status);
  const ABERTO = ['3', '10'].includes(status);
  const DEVOLVIDA = ['6'].includes(status);
  const PARC_DEVOLVIDA = ['13'].includes(status);
  const ANDAMENTO = ['1'].includes(status);
  const RETIRADA = ['5'].includes(status);
  const AGUARDANDO_RETIRADA = ['8'].includes(status);

  if (DAV_FINALIZADAS || MESCLADA) return `status-cel-green${withBg}`;
  if (DAV_CANCELADAS) return `status-cel-yellow${withBg}`;
  if (ABERTO) return `status-cel-blue${withBg}`;
  if (DEVOLVIDA) return `status-cel-black${withBg}`;
  if (PARC_DEVOLVIDA) return `status-cel-gray${withBg}`;
  if (ANDAMENTO) return `status-cel-orange${withBg}`;
  if (RETIRADA) return `status-cel-purple-lighten${withBg}`;
  if (AGUARDANDO_RETIRADA) return `status-cel-purple${withBg}`;

  return `status-cel-default${withBg}`;
}
const statusNotaOptions = [
  { label: 'orcamento', value: 1 },
  { label: 'condicional', value: 2 },
  { label: 'pedido', value: 3 }
];

const handleSummaryGridFilter = async (activeIndex) => {
  let filterValue = [];

  activeIndex.forEach((value) => {
    const selectedGrid = statusNotaOptions[value].value;
    filterValue.push(selectedGrid);
  });

  if (!filterValue || filterValue.length == 0) {
    delete salesStore.table.filters['vendaWithDav.codTipoDav'];
  } else {
    salesStore.table.filters['vendaWithDav.codTipoDav'] = {
      field: 'vendaWithDav.codTipoDav',
      filterType: 'IN_ARRAY',
      filterValue
    };
  }
};

// Definição das colunas da tabela
const columns = reactive([
  {
    name: 'controle',
    field: 'controle',
    required: true,
    label: 'Código',
    sortable: true,
    align: 'left',
    filterType: 'ILIKE',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'pessoa.razaoSocial',
    field: (row) => row?.pessoa,
    tooltip: (row) => row?.pessoa?.descricaoCodigo,
    format: (row) => row?.descricaoCodigo,
    label: 'Cliente',
    sortable: true,
    align: 'left',
    filterType: 'ILIKE',
    cellClasses: cellColorStatus,
    filtersV2: true,
    group: true,
    extraFilters: [
      {
        field: 'pessoa.razaoSocial',
        filterType: 'ILIKE',
        filterValue: 'pessoa.razaoSocial'
      },
      {
        operator: 'OR',
        field: 'codCliente',
        filterType: 'EQUALS',
        filterValue: 'pessoa.controle'
      }
    ]
    // extraFilters: {
    //   codCliente: {
    //     filterType: 'ILIKE_OR',
    //     filterValue: {
    //       fields: ['pessoa.razaoSocial', 'pessoa.controle']
    //     }
    //   }
    // }
  },
  {
    name: 'codFuncionarioResponsavel',
    field: (row) => row?.vendaWithFuncionarioResponsavel,
    tooltip: (row) => row?.vendaWithFuncionarioResponsavel?.descricaoCodigo,
    format: (row) => row?.descricaoCodigo,
    label: 'Funcionário',
    sortable: true,
    align: 'left',
    filterType: 'ILIKE',
    extraFilters: [
      {
        field: 'vendaWithFuncionarioResponsavel.nome',
        filterType: 'ILIKE',
        filterValue: 'vendaWithFuncionarioResponsavel.nome'
      },
      {
        operator: 'OR',
        field: 'codFuncionarioResponsavel',
        filterType: 'EQUALS',
        filterValue: 'vendaWithFuncionarioResponsavel.controle'
      }
    ],
    cellClasses: cellColorStatus,
    filtersV2: true,
    group: true
  },
  {
    name: 'vendaWithDav.codTipoDav',
    field: (row) => row?.vendaWithDav?.davWithTipoDav?.descricao,
    label: 'Tipo DAV',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'valorLiquido',
    field: (row) => 'R$ ' + currencyFormat(row.valorLiquido, 2),
    label: 'Valor total',
    filterType: 'VALUE_RANGE',
    sortable: true,
    align: 'left',
    isMoney: true,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'vendaWithCreatedBy.nome',
    field: (row) =>
      row?.vendaWithCreatedBy
        ? `${row.vendaWithCreatedBy?.controle} - ${row.vendaWithCreatedBy?.nome} ${row.vendaWithCreatedBy?.sobrenome}`
        : '-',
    label: 'Usuário',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true,
    filterType: 'ILIKE',
    group: true,
    deleteFilterKey: 'vendaWithCreatedBy.nome',
    extraFilters: [
      {
        central: true,
        relationship: 'vendaWithCreatedBy',
        fieldRelation: 'createdBy',
        filtersV2: [
          {
            field: 'nome',
            filterType: 'ILIKE'
          },
          {
            operator: 'OR',
            field: 'controle',
            filterType: 'EQUALS'
          },
          {
            operator: 'OR',
            field: 'sobrenome',
            filterType: 'ILIKE'
          }
        ]
      }
    ]
  },
  {
    name: 'created_at',
    field: (row) =>
      row?.createdAt?.substr(0, 10).split('-').reverse().join('/'),
    tooltip: (row) => getDateTimeFromString(row?.createdAt),
    label: 'Data venda',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'vendaWithDav.dataValidade',
    field: (row) => {
      if (row?.vendaWithDav?.codTipoDav) {
        const hasDataValidade = row?.vendaWithDav?.dataValidade;

        if (hasDataValidade) {
          return new Date(hasDataValidade + ' 00:00:00').toLocaleDateString(
            'pt-BR',
            {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            }
          );
        } else {
          return '-';
        }
      }
      return '-';
    },
    tooltip: (row) =>
      row?.vendaWithDav?.dataValidade
        ? formatToBrazilDate(row.vendaWithDav.dataValidade)
        : '-',
    label: 'Data validade',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'vendaWithDav.dataPrevisaoEntrega',
    field: (row) => {
      if (row?.vendaWithDav?.codTipoDav) {
        const hasPrevisaoEntrega = row?.vendaWithDav?.dataPrevisaoEntrega;

        if (hasPrevisaoEntrega) {
          return new Date(hasPrevisaoEntrega + ' 00:00:00').toLocaleDateString(
            'pt-BR',
            {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            }
          );
        } else {
          return '-';
        }
      }
      return '-';
    },
    tooltip: (row) =>
      row?.vendaWithDav?.dataPrevisaoEntrega
        ? formatToBrazilDate(row.vendaWithDav.dataPrevisaoEntrega)
        : '-',
    label: 'Prev. entreg./devo.',
    headerTooltip: 'Data previsão da entrega ou devolução',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'vendaWithDav.dataDevolucao',
    field: (row) => {
      if (row?.vendaWithDav?.dataDevolucao) {
        const hasDataDevolucao = row?.vendaWithDav?.dataDevolucao;

        if (hasDataDevolucao) {
          return new Date(hasDataDevolucao + ' 00:00:00').toLocaleDateString(
            'pt-BR',
            {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            }
          );
        } else {
          return '-';
        }
      }
      return '-';
    },
    label: 'Data devolução',
    tooltip: (row) =>
      row?.vendaWithDav?.dataDevolucao
        ? formatToBrazilDate(row.vendaWithDav.dataDevolucao)
        : '-',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'vendaWithDav.descricaoSituacao',
    field: (row) => {
      if (row?.vendaWithDav?.mesclada) {
        return 'Mesclada';
      }

      const descricao = row?.vendaWithDav?.descricaoSituacao;
      if (!descricao) return '-';
      return (
        descricao.charAt(0).toUpperCase() + descricao.slice(1).toLowerCase()
      );
    },

    label: 'Status',
    align: 'center',
    sort: 'disabled',
    fixed: true,
    required: true,
    classes: `sticky-table-column-with-line !tw-min-w-44 !tw-w-44 !tw-max-w-44 !tw-right-[80px]`,
    headerClasses: `sticky-table-column-header-with-line !tw-right-[80px]`,
    cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center ',
    cellClasses: (row) => cellColorStatus(row, '-with-bg'),
    filtersV2: true
  },
  {
    name: 'actions',
    field: 'actions',
    required: true,
    label: 'Ações',
    align: 'center',
    fixed: true,
    headerClasses: '!tw-min-w-[80px] !tw-w-[80px] !tw-max-w-[80px] ',
    cellClasses: cellColorStatus
  }
]);

// Definir ações da action bar
const actionBarEvents = {
  downloadDav: {
    callback: () => handleDav(tableSelectedItems.value[0], true),
    condition: () => global.roles?.includes('VENDA.DAV:BAIXAR')
  },
  printDav: {
    callback: () => handleDav(tableSelectedItems.value[0]),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  duplicate: {
    callback: () => handleDuplicateDav(tableSelectedItems.value?.[0]),
    condition: () => global.roles?.includes('VENDA.DAV:DUPLICAR')
  },
  sendEmailNote: {
    callback: () => sendEmail(),
    condition: () => global.roles?.includes('VENDA.DAV:ENVIAR-EMAIL')
  },
  view: {
    callback: handleViewRegister,
    condition: () => global.roles?.includes('VENDA.DAV:VISUALIZAR')
  },
  devolucao: {
    callback: () => devolucaoCondicional(),
    condition: () => global.roles?.includes('VENDA.DAV:DEVOLVER')
  },
  cancelDav: {
    callback: () => handleCancelDav(tableSelectedItems.value?.[0]),
    condition: () => global.roles?.includes('VENDA.DAV:CANCELAR')
  },
  printDavA4: {
    callback: () => handleDav(tableSelectedItems.value?.[0], false, 'A4'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  printDavA4Resumida: {
    callback: () =>
      handleDav(tableSelectedItems.value?.[0], false, 'A4Resumida'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  printDavTermica80: {
    callback: () =>
      handleDav(tableSelectedItems.value?.[0], false, 'Termica80'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  printDavTermicaResumida80: {
    callback: () =>
      handleDav(tableSelectedItems.value?.[0], false, 'TermicaResumida80'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  printDavTermica58: {
    callback: () =>
      handleDav(tableSelectedItems.value?.[0], false, 'Termica58'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  },
  printDavTermicaResumida58: {
    callback: () =>
      handleDav(tableSelectedItems.value?.[0], false, 'TermicaResumida58'),
    condition: () => global.roles?.includes('VENDA.DAV:IMPRIMIR')
  }
};

// Definir botões da action bar
const actionBarButtons = useDavTableActionbar({
  selected: tableSelectedItems,
  events: actionBarEvents
});
</script>
