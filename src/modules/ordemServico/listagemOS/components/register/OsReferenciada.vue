<template>
  <div
    class="tw-col-span-12 tw-flex"
    :class="props.readonlyOrDisable ? 'tw-col-span-full' : 'tw-col-span-11'"
  >
    <SGTable
      :rows="osSelecionada"
      :valid="fields.os.codOsReferenciada.meta.valid"
      :columns="loadColumns"
      :table-bind="tableBind"
      hide-search
      :searchable="false"
      :show-pagination="false"
    />

    <div
      class="tw-col-span-1 tw-flex tw-flex-col"
      v-if="!props.readonlyOrDisable"
    >
      <q-btn
        @click="adicionarOsReferenciada"
        :disable="props.readonlyOrDisable || osSelecionada.length > 0"
        dense
        flat
        icon="add_circle_outline"
        color="primary"
        size="1rem"
      >
        <TooltipCustom
          v-if="!osSelecionada.length"
          text-tooltip="Selecione uma ordem de serviço para adicionar"
        />
      </q-btn>

      <q-btn
        @click="removerOsReferenciada"
        :disabled="props.readonlyOrDisable || !osSelecionada.length"
        dense
        flat
        icon="remove_circle_outline"
        color="primary"
        size="1rem"
      >
        <TooltipCustom
          v-if="!osSelecionada.length"
          text-tooltip="Selecione uma ordem de serviço para excluir"
        />
      </q-btn>
    </div>
  </div>
</template>

<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { currencyFormat } from 'src/components/utils/currencyFormat';
import { getDateTimeFromString } from 'src/components/utils/dates';
import { useQuasar } from 'quasar';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import { onMounted, reactive, ref } from 'vue';
import DialogModalLayout from 'src/layouts/DialogModalLayout.vue';
import ImportarVenda from 'src/modules/vendas/devolucao/components/modals/ImportarVenda.vue';
import { useVendaStore } from 'src/stores/api/venda/useVendaStore';

const props = defineProps({
  data: { type: Object, required: true },
  readonlyOrDisable: {
    type: Boolean,
    default: false
  }
});

const vendaStore = useVendaStore();

const $q = useQuasar();
// const containerClass = computed(() => {
//   if ($q.screen.lt.xl) {
//     return 'customScroll tw-flex tw-flex-col tw-gap-2 tw-overflow-auto tw-pb-4';
//   }

//   return 'tw-flex-1 tw-overflow-visible tw-flex tw-flex-col tw-gap-2';
// });

const fields = defineModel('fields', { type: Object, required: true });
const setFieldValue = defineModel('setFieldValue', {
  type: Function,
  required: false
});

const osSelecionada = ref([]);
const tableBind = ref({
  selection: 'none',
  noDataImage: false,
  class: '!tw-min-h-[100px] !tw-max-h-[100px]',
  'hide-no-data': true
});

onMounted(() => {
  if (props.data) {
    osSelecionada.value.push(props.data);
  }
});

async function adicionarOsReferenciada() {
  vendaStore.table.relationships = [
    'vendaWithItem',
    'vendaWithOs.situacao',
    'pessoa',
    'vendaWithFuncionarioResponsavel',
    'vendaWithCreatedBy',
    'vendaWithOs'
  ];
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: ImportarVenda,
      scope: 'selecao-items',
      data: {
        tipoVenda: 5,
        isDav: true
      }
    }
  })
    .onOk((importedOS) => {
      osSelecionada.value.push(importedOS?.refOS[0]);

      const codOsReferenciada = osSelecionada.value[0]?.controle
        ? Number(osSelecionada.value[0].controle)
        : 0;

      setFieldValue.value('os.codOsReferenciada', codOsReferenciada);
    })

    .onCancel(() => {});
}

function removerOsReferenciada() {
  osSelecionada.value = [];
  setFieldValue.value('os.codOsReferenciada', null);
}

const loadColumns = reactive([
  {
    name: 'controle',
    field: 'controle',
    required: true,
    label: 'Código',
    sortable: true,
    align: 'left',
    filterType: 'EQUALS',
    filtersV2: true
  },
  {
    name: 'pessoa.razaoSocial',
    field: (row) => row?.pessoa,
    tooltip: (row) => {
      return row?.pessoa?.descricaoCodigo;
    },
    format: (row) => row?.descricaoCodigo,
    label: 'Cliente',
    sortable: true,
    align: 'left',
    filterType: 'ILIKE',
    filtersV2: true,
    group: true,
    extraFilters: [
      {
        field: 'pessoa.razaoSocial',
        filterType: 'ILIKE',
        filterValue: 'pessoa.razaoSocial'
      },
      {
        operator: 'OR',
        field: 'codCliente',
        filterType: 'EQUALS',
        filterValue: 'pessoa.controle'
      }
    ]
  },
  {
    name: 'created_at',
    field: (row) =>
      row?.createdAt?.substr(0, 10).split('-').reverse().join('/'),
    tooltip: (row) => getDateTimeFromString(row?.createdAt),
    label: 'Data criação',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    filtersV2: true
  },
  {
    name: 'vendaWithOs.dataHoraEntrega',
    field: (row) => {
      if (row?.vendaWithOs) {
        const hasDataHoraEntrega = row?.vendaWithOs?.dataHoraEntrega;

        if (hasDataHoraEntrega) {
          return new Date(hasDataHoraEntrega).toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          });
        } else {
          return '-';
        }
      }
      return '-';
    },
    tooltip: (row) => {
      if (row?.vendaWithOs?.dataHoraEntrega) {
        return new Date(row.vendaWithOs.dataHoraEntrega).toLocaleDateString(
          'pt-BR',
          {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }
        );
      }
      return '-';
    },
    label: 'Data/hora entrega',
    filterType: 'DATE_RANGE',
    sortable: true,
    align: 'left',
    filtersV2: true
  },
  {
    name: 'valorLiquido',
    field: (row) => 'R$ ' + currencyFormat(row.valorLiquido, 2),
    label: 'Valor líquido',
    filterType: 'VALUE_RANGE',
    sortable: true,
    align: 'left',
    isMoney: true,
    filtersV2: true
  }
]);
defineExpose({ removerOsReferenciada });
</script>
