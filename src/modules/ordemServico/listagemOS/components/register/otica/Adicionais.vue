<template>
  <NewSGCard
    id="adicionais"
    title="Adicionais"
    :schema-errors="errors"
    :meta="meta"
    :cols="8"
    has-title-separator
    has-circle
    remove-gap
  >
    <q-input
      v-model="fields.otica.adicional.caixaGaveta.value"
      :error="!!fields.otica.adicional.caixaGaveta.errorMessage"
      :error-message="fields.otica.adicional.caixaGaveta.errorMessage"
      :valid="fields.otica.adicional.caixaGaveta.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="ModelInputText"
      label="Caixa/Gaveta"
      class="tw-col-span-3"
    />
    <InputSelect
      v-model="fields.otica.adicional.localMontagem.value"
      :error="!!fields.otica.adicional.localMontagem.errorMessage"
      :error-message="fields.otica.adicional.localMontagem.errorMessage"
      :valid="fields.otica.adicional.localMontagem.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'Loja', controle: '1' },
            { descricao: 'Laboratório', controle: '2' }
          ]
        }
      }"
      label="Local de montagem"
      class="tw-col-span-3"
      required
    />
    <InputSelect
      v-model="fields.otica.adicional.clientePossuiReceita.value"
      :error="!!fields.otica.adicional.clientePossuiReceita.errorMessage"
      :error-message="fields.otica.adicional.clientePossuiReceita.errorMessage"
      :valid="fields.otica.adicional.clientePossuiReceita.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'SIM', controle: true },
            { descricao: 'NÃO', controle: false }
          ]
        }
      }"
      label="Cliente possui receita"
      class="tw-col-span-2"
      required
    />
  </NewSGCard>

  <NewSGCard
    id="lente"
    title="Lente"
    :schema-errors="errors"
    :meta="meta"
    :cols="8"
    class="tw-col-span-full tw-mb-4"
    has-title-separator
    has-circle
    remove-gap
  >
    <InputSelect
      v-model="fields.otica.adicional.lente.tipo.value"
      :error="!!fields.otica.adicional.lente.tipo.errorMessage"
      :error-message="fields.otica.adicional.lente.tipo.errorMessage"
      :valid="fields.otica.adicional.lente.tipo.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'Pronta', controle: '1' },
            { descricao: 'Surfaçada', controle: '2' }
          ]
        }
      }"
      label="Tipo"
      class="tw-col-span-2"
      required
    />
    <InputSelect
      v-model="fields.otica.adicional.lente.material.value"
      :error="!!fields.otica.adicional.lente.material.errorMessage"
      :error-message="fields.otica.adicional.lente.material.errorMessage"
      :valid="fields.otica.adicional.lente.material.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'Policarbonato', controle: '1' },
            { descricao: 'Resina', controle: '2' },
            { descricao: 'Trivex', controle: '3' }
          ]
        }
      }"
      label="Material"
      class="tw-col-span-2"
      required
    />
    <q-input
      v-model="fields.otica.adicional.lente.descricao.value"
      :error="!!fields.otica.adicional.lente.descricao.errorMessage"
      :error-message="fields.otica.adicional.lente.descricao.errorMessage"
      :valid="fields.otica.adicional.lente.descricao.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="ModelInputText"
      label="Descrição"
      class="tw-col-span-4"
    />
    <q-input
      v-model="fields.otica.adicional.lente.coloracao.value"
      :error="!!fields.otica.adicional.lente.coloracao.errorMessage"
      :error-message="fields.otica.adicional.lente.coloracao.errorMessage"
      :valid="fields.otica.adicional.lente.coloracao.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="ModelInputText"
      label="Coloração"
      class="tw-col-span-2"
    />

    <!-- tratamentos front é somente usado no front para validacao -->
    <InputSelect
      v-model="fields.otica.adicional.lente.tratamentosFront.value"
      :model-value="tratamentosSelecionados"
      :error="!!fields.otica.adicional.lente.tratamentosFront.errorMessage"
      :error-message="
        fields.otica.adicional.lente.tratamentosFront.errorMessage
      "
      :valid="fields.otica.adicional.lente.tratamentosFront.meta.valid"
      :readonly="props.readonlyOrDisable"
      :multiple="true"
      @update:model-value="onChangeTratamentosSelecionados"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: optionsTreatments
        }
      }"
      label="Tratamentos"
      class="tw-col-span-6"
      required
      :clearable="false"
    />
  </NewSGCard>
  <NewSGCard
    id="armacao"
    title="Armação"
    :schema-errors="errors"
    :meta="meta"
    :cols="8"
    class="tw-col-span-full tw-mb-4"
    has-title-separator
    has-circle
    remove-gap
  >
    <InputSelect
      v-model="fields.otica.adicional.armacao.propria.value"
      :error="!!fields.otica.adicional.armacao.propria.errorMessage"
      :error-message="fields.otica.adicional.armacao.propria.errorMessage"
      :valid="fields.otica.adicional.armacao.propria.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'SIM', controle: true },
            { descricao: 'NÃO', controle: false }
          ]
        }
      }"
      label="Armação própria"
      class="tw-col-span-2"
      required
    />
    <InputSelect
      v-model="fields.otica.adicional.armacao.segueArmacao.value"
      :error="!!fields.otica.adicional.armacao.segueArmacao.errorMessage"
      :error-message="fields.otica.adicional.armacao.segueArmacao.errorMessage"
      :valid="fields.otica.adicional.armacao.segueArmacao.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            { descricao: 'SIM', controle: true },
            { descricao: 'NÃO', controle: false }
          ]
        }
      }"
      label="Segue armação"
      class="tw-col-span-2"
      required
    />
    <InputSelect
      v-model="fields.otica.adicional.armacao.tipo.value"
      :error="!!fields.otica.adicional.armacao.tipo.errorMessage"
      :error-message="fields.otica.adicional.armacao.tipo.errorMessage"
      :valid="fields.otica.adicional.armacao.tipo.meta.valid"
      :readonly="props.readonlyOrDisable"
      v-bind="{
        ...ModelInputSelect,
        params: {
          optionsStatic: [
            {
              descricao: 'Friso/Fio de Nylon',
              controle: 1
            },
            { descricao: 'Furo/Parafuso', controle: '2' },
            { descricao: 'Metal', controle: '3' },
            { descricao: 'Zilo/Acetato', controle: '4' }
          ]
        }
      }"
      label="Tipo"
      class="tw-col-span-2"
      required
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.aro.value"
      :error="!!fields.otica.adicional.armacao.aro.errorMessage"
      :error-message="fields.otica.adicional.armacao.aro.errorMessage"
      :valid="fields.otica.adicional.armacao.aro.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Aro"
      class="tw-col-span-2"
      required
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.ponte.value"
      :error="!!fields.otica.adicional.armacao.ponte.errorMessage"
      :error-message="fields.otica.adicional.armacao.ponte.errorMessage"
      :valid="fields.otica.adicional.armacao.ponte.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Ponte"
      class="tw-col-span-2"
      required
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.aroPonte.value"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Aro + Ponte"
      class="tw-col-span-2"
      readonly
      disabled
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.maiorDiagonal.value"
      :error="!!fields.otica.adicional.armacao.maiorDiagonal.errorMessage"
      :error-message="fields.otica.adicional.armacao.maiorDiagonal.errorMessage"
      :valid="fields.otica.adicional.armacao.maiorDiagonal.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Maior diagonal"
      class="tw-col-span-2"
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.alturaVertical.value"
      :error="!!fields.otica.adicional.armacao.alturaVertical.errorMessage"
      :error-message="
        fields.otica.adicional.armacao.alturaVertical.errorMessage
      "
      :valid="fields.otica.adicional.armacao.alturaVertical.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Altura vertical"
      class="tw-col-span-2"
    />
    <InputMoney
      v-model="fields.otica.adicional.armacao.distanciaPupilar.value"
      :error="!!fields.otica.adicional.armacao.distanciaPupilar.errorMessage"
      :error-message="
        fields.otica.adicional.armacao.distanciaPupilar.errorMessage
      "
      :valid="fields.otica.adicional.armacao.distanciaPupilar.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Distância pupilar"
      class="tw-col-span-2"
    />
    <div class="tw-col-span-full">
      <h2 class="tw-mb-1">Formato</h2>

      <div
        class="tw-grid tw-grid-cols-4 tw-gap-1 md:tw-grid-cols-8 lg:tw-grid-cols-8 xl:tw-grid-cols-8"
      >
        <label
          v-for="option in formatos"
          :key="option.id"
          class="tw-flex tw-cursor-pointer tw-flex-col tw-items-start"
        >
          <div class="tw-mb-2 tw-flex tw-items-center tw-gap-2">
            <input
              type="radio"
              :id="'formato' + option.id"
              name="formato"
              :value="option.id"
              v-model="fields.otica.adicional.armacao.formato.value"
            />

            <span class="tw-text-xs tw-text-gray-700">{{ option.id }}</span>
          </div>
          <div
            class="tw-flex tw-flex-col tw-items-center tw-rounded tw-border tw-p-2 tw-transition-all hover:tw-border-SGBRBlueLighten"
            :class="
              fields.otica.adicional.armacao.formato.value === option.id
                ? 'tw-border-SGBRBlueLighten'
                : 'tw-border-SGBRGrayLighten'
            "
          >
            <img
              :src="option.img"
              :alt="'Formato ' + option.id"
              class="tw-h-16 tw-w-16 tw-object-contain"
            />
          </div>
        </label>
      </div>
    </div>
  </NewSGCard>
  <NewSGCard
    id="centro-otico"
    title="Altura centro ótico (CO)"
    :schema-errors="errors"
    :meta="meta"
    :cols="8"
    has-circle
    class="tw-col-span-full"
    has-title-separator
    remove-gap
  >
    <InputMoney
      v-model="fields.otica.adicional.alturaCentroOtico.longeOd.value"
      :error="!!fields.otica.adicional.alturaCentroOtico.longeOd.errorMessage"
      :error-message="
        fields.otica.adicional.alturaCentroOtico.longeOd.errorMessage
      "
      :valid="fields.otica.adicional.alturaCentroOtico.longeOd.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Longe OD"
      class="tw-col-span-2"
    >
      <template #hint>mm </template>
    </InputMoney>
    <InputMoney
      v-model="fields.otica.adicional.alturaCentroOtico.longeOe.value"
      :error="!!fields.otica.adicional.alturaCentroOtico.longeOe.errorMessage"
      :error-message="
        fields.otica.adicional.alturaCentroOtico.longeOe.errorMessage
      "
      :valid="fields.otica.adicional.alturaCentroOtico.longeOe.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Longe OE"
      class="tw-col-span-2"
    >
      <template #hint>mm </template>
    </InputMoney>
    <InputMoney
      v-model="fields.otica.adicional.alturaCentroOtico.pertoOd.value"
      :error="!!fields.otica.adicional.alturaCentroOtico.pertoOd.errorMessage"
      :error-message="
        fields.otica.adicional.alturaCentroOtico.pertoOd.errorMessage
      "
      :valid="fields.otica.adicional.alturaCentroOtico.pertoOd.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Perto OD"
      class="tw-col-span-2"
    >
      <template #hint>mm </template>
    </InputMoney>
    <InputMoney
      v-model="fields.otica.adicional.alturaCentroOtico.pertoOe.value"
      :error="!!fields.otica.adicional.alturaCentroOtico.pertoOe.errorMessage"
      :error-message="
        fields.otica.adicional.alturaCentroOtico.pertoOe.errorMessage
      "
      :valid="fields.otica.adicional.alturaCentroOtico.pertoOe.meta.valid"
      :readonly="props.readonlyOrDisable"
      :max="99999999.9999"
      :decimals-quantity="4"
      v-bind="ModelInputText"
      label="Perto OE"
      class="tw-col-span-2"
    >
      <template #hint>mm </template>
    </InputMoney>
  </NewSGCard>
</template>

<script setup>
import ModelInputText from 'src/core/models/inputs/Text';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import InputMoney from 'src/core/components/Inputs/Money/InputMoney.vue';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import ModelInputSelect from 'src/core/models/inputs/Select';
import { ref, watch } from 'vue';

const props = defineProps({
  readonlyOrDisable: {
    type: Boolean,
    default: false
  }
});

const fields = defineModel('fields', {
  type: Object,
  required: true,
  default: () => {}
});

const values = defineModel('values', { type: Object, required: true });

const setFieldValue = defineModel('setFieldValue', {
  type: Function,
  required: false
});

const meta = defineModel('meta', { type: Object, required: true });
const errors = defineModel('errors', { type: Object, required: true });

const tratamentosSelecionados = ref(
  (fields.value.otica.adicional.lente.tratamentos.value || [])
    .filter((t) => !t.deletar)
    .map((t) => Number(t.tratamento))
);

const onChangeTratamentosSelecionados = (novosSelecionados) => {
  const tratamentosDoBack =
    fields.value.otica.adicional.lente.tratamentos.value;

  const tratamentosAtuais = tratamentosDoBack.map((t) => Number(t.tratamento));

  // Marcar como deletar se tiver controle ou remover se não tiver
  tratamentosAtuais.forEach((tratamento) => {
    if (!novosSelecionados.includes(tratamento)) {
      const existente = tratamentosDoBack.find(
        (t) => Number(t.tratamento) === tratamento
      );

      if (existente) {
        if (existente.controle) {
          existente.deletar = true;
        } else {
          const index = tratamentosDoBack.findIndex(
            (t) => Number(t.tratamento) === tratamento
          );
          tratamentosDoBack.splice(index, 1);
        }
      }
    }
  });

  // Adicionar novo tratamento
  novosSelecionados.forEach((tratamento) => {
    const existente = tratamentosDoBack.find(
      (t) => Number(t.tratamento) === tratamento
    );

    if (!existente) {
      tratamentosDoBack.push({
        tratamento: tratamento
      });
    } else if (existente.deletar) {
      delete existente.deletar;
    }
  });

  const treatmentFormated = tratamentosDoBack
    .filter((t) => !t.deletar)
    .map((t) => Number(t.tratamento));

  tratamentosSelecionados.value = treatmentFormated;
};

const formatos = [
  {
    id: 1,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo1.png'
  },
  {
    id: 2,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo2.png'
  },
  {
    id: 3,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo3.png'
  },
  {
    id: 4,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo4.png'
  },
  {
    id: 5,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo5.png'
  },
  {
    id: 6,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo6.png'
  },
  {
    id: 7,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo7.png'
  },
  {
    id: 8,
    img: 'https://static.sgmaster.com.br/otica/armacao-formatos/modelo8.png'
  }
];

const recalculateAroPonte = () => {
  const aro = values.value.otica.adicional.armacao.aro || 0;
  const ponte = values.value.otica.adicional.armacao.ponte || 0;

  setFieldValue.value('otica.adicional.armacao.aroPonte', aro + ponte);
};

watch(() => values.value.otica.adicional.armacao.aro, recalculateAroPonte);

watch(() => values.value.otica.adicional.armacao.ponte, recalculateAroPonte);

const optionsTreatments = [
  { descricao: 'Easy Clean', controle: 1 },
  { descricao: 'No-Risk', controle: 2 },
  { descricao: 'Optkot', controle: 3 },
  { descricao: 'Outros', controle: 4 }
];
</script>
