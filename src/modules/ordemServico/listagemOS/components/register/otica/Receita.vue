<template>
  <InputSelect
    ref="doctorInputRef"
    v-model="fields.otica.receita.codMedico.value"
    :error="!!fields.otica.receita.codMedico.errorMessage"
    :error-message="fields.otica.receita.codMedico.errorMessage"
    :valid="fields.otica.receita.codMedico.meta.valid"
    :readonly="props.readonlyOrDisable"
    v-bind="{
      ...ModelInputSelectSearch,
      params: {
        apiRoute: 'api/pessoa',
        filterOr: ['controle', 'razaosocial', 'cnpjcpf'],
        filters: {
          'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao': {
            filterType: 'ILIKE',
            filterValue: 'Médico'
          },
          deleted_at: {
            filterType: 'NULL'
          }
        },
        orderBy: { controle: 'desc' }
      }
    }"
    option-value="controle"
    option-label="descricaoCodigo"
    label="Médico"
    class="tw-col-span-5"
    add-button
    edit-button
    @on-add="() => handleRegisterDoctor()"
    @on-edit="handleRegisterDoctor"
    role-prefix="PESSOA"
    required
  />

  <DatePicker
    :value="fields.otica.receita.dataValidade.value"
    @value="(val) => fields.otica.receita.dataValidade.setValue(val)"
    :error="!!fields.otica.receita.dataValidade.errorMessage"
    :error-message="fields.otica.receita.dataValidade.errorMessage"
    :valid="fields.otica.receita.dataValidade.meta.valid"
    :readonly="props.readonlyOrDisable"
    v-bind="ModelInputText"
    maxlength="100"
    required
    label="Data de validade"
    class="tw-col-span-3"
  />
  <table class="tw-col-span-full tw-mt-3">
    <thead>
      <tr>
        <th class="tw-bg-transparent"></th>
        <th
          class="tw-font-sm p-2 tw-border-b-2 tw-border-l-2 tw-border-t-2 tw-bg-gray-100 tw-p-2 tw-text-sm tw-font-normal"
        >
          Esférico
        </th>
        <th
          class="tw-font-sm p-2 tw-border-b-2 tw-border-l-2 tw-border-t-2 tw-bg-gray-100 tw-p-2 tw-text-sm tw-font-normal"
        >
          Cilíndrico
        </th>
        <th
          class="tw-font-sm p-2 tw-border-b-2 tw-border-l-2 tw-border-t-2 tw-bg-gray-100 tw-p-2 tw-text-sm tw-font-normal"
        >
          Eixo (°)
        </th>
        <th
          class="tw-font-sm p-2 tw-border-b-2 tw-border-l-2 tw-border-t-2 tw-bg-gray-100 tw-p-2 tw-text-sm tw-font-normal"
        >
          Altura (mm)
        </th>
        <th
          class="tw-font-sm p-2 tw-border-b-2 tw-border-l-2 tw-border-r-2 tw-border-t-2 tw-bg-gray-100 tw-p-2 tw-text-sm tw-font-normal"
        >
          DNP (mm)
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>
          <div
            class="tw-flex tw-items-center tw-justify-center tw-gap-3 tw-pr-1 tw-text-blue-500"
          >
            <q-icon name="visibility" />
            <span>Longe OD</span>
          </div>
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.esfericoLongeOd.value"
            :error="!!fields.otica.receita.esfericoLongeOd.errorMessage"
            :error-message="fields.otica.receita.esfericoLongeOd.errorMessage"
            :valid="fields.otica.receita.esfericoLongeOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.cilindricoLongeOd.value"
            :error="!!fields.otica.receita.cilindricoLongeOd.errorMessage"
            :error-message="fields.otica.receita.cilindricoLongeOd.errorMessage"
            :valid="fields.otica.receita.cilindricoLongeOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.eixoLongeOd.value"
            :error="!!fields.otica.receita.eixoLongeOd.errorMessage"
            :error-message="fields.otica.receita.eixoLongeOd.errorMessage"
            :valid="fields.otica.receita.eixoLongeOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="999"
            :decimals-quantity="0"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.alturaLongeOd.value"
            :error="!!fields.otica.receita.alturaLongeOd.errorMessage"
            :error-message="fields.otica.receita.alturaLongeOd.errorMessage"
            :valid="fields.otica.receita.alturaLongeOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2 tw-border-r-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.dnpLongeOd.value"
            :error="!!fields.otica.receita.dnpLongeOd.errorMessage"
            :error-message="fields.otica.receita.dnpLongeOd.errorMessage"
            :valid="fields.otica.receita.dnpLongeOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
      </tr>
      <tr>
        <td>
          <div
            class="tw-flex tw-items-center tw-justify-center tw-gap-3 tw-pr-1 tw-text-blue-500"
          >
            <q-icon name="visibility" />
            <span>Longe OE</span>
          </div>
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.esfericoLongeOe.value"
            :error="!!fields.otica.receita.esfericoLongeOe.errorMessage"
            :error-message="fields.otica.receita.esfericoLongeOe.errorMessage"
            :valid="fields.otica.receita.esfericoLongeOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.cilindricoLongeOe.value"
            :error="!!fields.otica.receita.cilindricoLongeOe.errorMessage"
            :error-message="fields.otica.receita.cilindricoLongeOe.errorMessage"
            :valid="fields.otica.receita.cilindricoLongeOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.eixoLongeOe.value"
            :error="!!fields.otica.receita.eixoLongeOe.errorMessage"
            :error-message="fields.otica.receita.eixoLongeOe.errorMessage"
            :valid="fields.otica.receita.eixoLongeOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="999"
            :decimals-quantity="0"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.alturaLongeOe.value"
            :error="!!fields.otica.receita.alturaLongeOe.errorMessage"
            :error-message="fields.otica.receita.alturaLongeOe.errorMessage"
            :valid="fields.otica.receita.alturaLongeOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2 tw-border-r-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.dnpLongeOe.value"
            :error="!!fields.otica.receita.dnpLongeOe.errorMessage"
            :error-message="fields.otica.receita.dnpLongeOe.errorMessage"
            :valid="fields.otica.receita.dnpLongeOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
      </tr>
      <tr>
        <td>
          <div
            class="tw-flex tw-items-center tw-justify-center tw-gap-3 tw-pr-1 tw-text-red-500"
          >
            <q-icon name="visibility" />
            <span>Perto OD</span>
          </div>
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.esfericoPertoOd.value"
            :error="!!fields.otica.receita.esfericoPertoOd.errorMessage"
            :error-message="fields.otica.receita.esfericoPertoOd.errorMessage"
            :valid="fields.otica.receita.esfericoPertoOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.cilindricoPertoOd.value"
            :error="!!fields.otica.receita.cilindricoPertoOd.errorMessage"
            :error-message="fields.otica.receita.cilindricoPertoOd.errorMessage"
            :valid="fields.otica.receita.cilindricoPertoOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.eixoPertoOd.value"
            :error="!!fields.otica.receita.eixoPertoOd.errorMessage"
            :error-message="fields.otica.receita.eixoPertoOd.errorMessage"
            :valid="fields.otica.receita.eixoPertoOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="999"
            :decimals-quantity="0"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.alturaPertoOd.value"
            :error="!!fields.otica.receita.alturaPertoOd.errorMessage"
            :error-message="fields.otica.receita.alturaPertoOd.errorMessage"
            :valid="fields.otica.receita.alturaPertoOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2 tw-border-r-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.dnpPertoOd.value"
            :error="!!fields.otica.receita.dnpPertoOd.errorMessage"
            :error-message="fields.otica.receita.dnpPertoOd.errorMessage"
            :valid="fields.otica.receita.dnpPertoOd.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
      </tr>
      <tr>
        <td>
          <div
            class="tw-flex tw-items-center tw-justify-center tw-gap-3 tw-pr-1 tw-text-red-500"
          >
            <q-icon name="visibility" />
            <span>Perto OE</span>
          </div>
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.esfericoPertoOe.value"
            :error="!!fields.otica.receita.esfericoPertoOe.errorMessage"
            :error-message="fields.otica.receita.esfericoPertoOe.errorMessage"
            :valid="fields.otica.receita.esfericoPertoOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.cilindricoPertoOe.value"
            :error="!!fields.otica.receita.cilindricoPertoOe.errorMessage"
            :error-message="fields.otica.receita.cilindricoPertoOe.errorMessage"
            :valid="fields.otica.receita.cilindricoPertoOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.eixoPertoOe.value"
            :error="!!fields.otica.receita.eixoPertoOe.errorMessage"
            :error-message="fields.otica.receita.eixoPertoOe.errorMessage"
            :valid="fields.otica.receita.eixoPertoOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="999"
            :decimals-quantity="0"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2">
          <InputMoney
            allow-negative
            :borderless="true"
            v-model="fields.otica.receita.alturaPertoOe.value"
            :error="!!fields.otica.receita.alturaPertoOe.errorMessage"
            :error-message="fields.otica.receita.alturaPertoOe.errorMessage"
            :valid="fields.otica.receita.alturaPertoOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
        <td class="tw-border-b-2 tw-border-l-2 tw-border-r-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.dnpPertoOe.value"
            :error="!!fields.otica.receita.dnpPertoOe.errorMessage"
            :error-message="fields.otica.receita.dnpPertoOe.errorMessage"
            :valid="fields.otica.receita.dnpPertoOe.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
      </tr>
      <tr>
        <td></td>
        <td>
          <div class="tw-pr-1 tw-text-center">
            <span>Adição</span>
          </div>
        </td>
      </tr>
      <tr>
        <td></td>
        <td class="tw-border-2">
          <InputMoney
            :borderless="true"
            v-model="fields.otica.receita.esfericoAdicao.value"
            :error="!!fields.otica.receita.esfericoAdicao.errorMessage"
            :error-message="fields.otica.receita.esfericoAdicao.errorMessage"
            :valid="fields.otica.receita.esfericoAdicao.meta.valid"
            :readonly="props.readonlyOrDisable"
            :max="99.99"
            :decimals-quantity="2"
            input-class="tw-text-[12px] tw-text-center"
            hide-bottom-space
          />
        </td>
      </tr>
    </tbody>
  </table>
  <q-input
    v-model="fields.otica.receita.observacao.value"
    :error="!!fields.otica.receita.observacao.errorMessage"
    :error-message="fields.otica.receita.observacao.errorMessage"
    :valid="fields.otica.receita.observacao.meta.valid"
    :readonly="props.readonlyOrDisable"
    v-bind="ModelInputText"
    class="tw-col-span-full tw-mt-3"
    maxlength="2500"
    input-style="min-height: 100px"
    type="textarea"
    autogrow
    label="Observações"
  />
</template>

<script setup>
import ModelInputText from 'src/core/models/inputs/Text';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import DialogModalLayout from 'src/layouts/DialogModalLayout.vue';
import RegisterPeople from 'src/modules/pessoa/views/register.vue';
import { ref, watch } from 'vue';
import { Dialog } from 'quasar';
import DatePicker from 'src/core/components/Inputs/Date/DatePicker.vue';
import InputMoney from 'src/core/components/Inputs/Money/InputMoney.vue';

const props = defineProps({
  readonlyOrDisable: {
    type: Boolean,
    default: false
  }
});

const fields = defineModel('fields', { type: Object, required: true });
const values = defineModel('values', { type: Object, required: true });
const setFieldValue = defineModel('setFieldValue', {
  type: Function,
  required: false
});

const doctorInputRef = ref();

const recalculateMedicaoAdicao = () => {
  const esfericoLongeOd = values.value.otica.receita.esfericoLongeOd;
  const esfericoLongeOe = values.value.otica.receita.esfericoLongeOe;
  const esfericoAdicao = values.value.otica.receita.esfericoAdicao;
  const cilindricoLongeOd = values.value.otica.receita.cilindricoLongeOd;
  const cilindricoLongeOe = values.value.otica.receita.cilindricoLongeOe;
  const eixoLongeOd = values.value.otica.receita.eixoLongeOd;
  const eixoLongeOe = values.value.otica.receita.eixoLongeOe;

  if (!esfericoAdicao) {
    return;
  }

  if (esfericoLongeOd) {
    setFieldValue.value(
      'otica.receita.esfericoPertoOd',
      esfericoAdicao + esfericoLongeOd
    );
  }

  if (esfericoLongeOe) {
    setFieldValue.value(
      'otica.receita.esfericoPertoOe',
      esfericoAdicao + esfericoLongeOe
    );
  }

  setFieldValue.value('otica.receita.cilindricoPertoOd', cilindricoLongeOd);
  setFieldValue.value('otica.receita.cilindricoPertoOe', cilindricoLongeOe);
  setFieldValue.value('otica.receita.eixoPertoOd', eixoLongeOd);
  setFieldValue.value('otica.receita.eixoPertoOe', eixoLongeOe);
};

watch(
  () => values.value.otica.receita.esfericoAdicao,
  recalculateMedicaoAdicao
);

const handleRegisterDoctor = (controle = null) => {
  let options = {};
  if (controle) {
    options = { data: { controle: controle, isEditing: true } };
  }
  Dialog.create({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'cadastrar-medico',
      ...options
    }
  }).onDismiss(async () => {
    doctorInputRef.value?.reMount();
  });
};
</script>
