<script setup>
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import NewSGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import { onMounted, ref, toRaw, toRefs, watch } from 'vue';
import DialogModalLayout from 'src/layouts/DialogModalLayout.vue';
import { useQuasar } from 'quasar';

import { default as RegisterEmployee } from 'src/modules/funcionarios/views/register.vue';
import round from 'src/components/utils/round';
import { notifyLoading } from 'src/components/utils/notify';
import InputPricePercent from 'src/core/components/Inputs/Money/InputPricePercent.vue';
import DatePicker from 'src/core/components/Inputs/Date/DatePicker.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import { useRoute, useRouter } from 'vue-router';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import RegisterPeople from 'src/modules/pessoa/views/register.vue';
import ModelInputText from 'src/core/models/inputs/Text';
import { computed } from 'vue';
import ResultTable from 'components/generic/items/Total.vue';
import { useSales } from 'src/stores/api/sales';
import { useGlobal } from 'src/stores/global';
import OSItems from 'src/modules/ordemServico/listagemOS/components/OSItems.vue';
import { useSalesStore } from 'src/modules/vendas/useSalesStore';
import { useSalesConfig } from 'src/stores/api/sales/config';
import { default as RegisterSituacao } from 'src/modules/ordemServico/listagemSituacao/views/register.vue';
import { formatCPFCNPJ, returnObjOrNull } from 'src/services/utils';

import { useFormSchema } from 'src/modules/ordemServico/listagemOS/models/useFormSchema';
import Receita from 'src/modules/ordemServico/listagemOS/components/register/otica/Receita.vue';

import ModelInputSelect from 'src/core/models/inputs/Select';
import Adicionais from 'src/modules/ordemServico/listagemOS/components/register/otica/Adicionais.vue';

import OsReferenciada from '../components/register/OsReferenciada.vue';

import { useOrdemServicoStore } from '../store/useOrdemServicoStore';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { default as RegisterObjectsModal } from 'src/modules/ordemServico/listagemObjeto/views/register.vue';
// import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import hotkeys from 'hotkeys-js';
import ConstrucaoCivilModal from '../components/modals/ConstrucaoCivilModal.vue';
import { api } from 'src/boot/axios';
import useRegisterOrdemServico from '../composables/useOrdemServicoRegisterActionbar';
import PaymentMethod from 'src/components/modal/sales/PaymentMethod.vue';
import InfosAdicionalModal from 'src/modules/vendas/dav/components/modal/InfosAdicionalModal.vue';
//import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys';

const ordemServicoStore = useOrdemServicoStore();
const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  },
  data: {
    type: [Object, null],
    default: () => {},
    required: false
  }
});

const $q = useQuasar();
const global = useGlobal();
const route = useRoute();
const router = useRouter();
const registerPageRef = ref();
// const { params } = route;
const { modal } = toRefs(props);
const isWarningAnimationDisabled = ref(true);
const codSituacao = ref('');
const codInternoSituacao = ref('');
const readonlyOrDisable = ref(route.path.includes('visualizar'));
const orcamentoStore = useSales();
const osReferenciada = ref();
const isAdiantamento = ref(false);

if (!ordemServicoStore.pageState?.tipoOS && route.path.includes('cadastro')) {
  router.push('/os/listagem');
}
async function getDefaultEmployeeValue() {
  let filters = [
    {
      not_deleted: true,
      field: 'codUsuario',
      filterType: 'FIXED',
      filterValue: global?.user?.controle
    },
    {
      field: 'controle',
      filterType: 'FIXED',
      filterValue: 1
    }
  ];

  const response = await api.get('/api/funcionario/select', {
    params: { filters, paginate: 20, page: 1 }
  });

  return response?.data?.fixed?.controle || '1';
}

let editValues = {
  os: { tipo: Number(ordemServicoStore.pageState?.tipoOS) || 1 },
  venda: {
    codFuncionarioResponsavel: await getDefaultEmployeeValue()
  }
};
let controle;
let codOrcamento = route?.query?.codOrcamento;
const osReferenciadaRef = ref();
const cancel = (response = false) => {
  isWarningAnimationDisabled.value = true;

  if (modal.value) {
    if (response) {
      emit('ok', {
        data: response?.data
      });
    } else {
      emit('cancel');
    }
  } else {
    router.push('/os/listagem');
  }
};

const statusDisable = ['4', '5', '8'];
const sales = useSalesStore();
const salesConfig = useSalesConfig();

salesConfig.get(null, true, false);
salesConfig.resetData();

if (!props?.data?.isEditing && props?.modal) {
  controle = props?.data?.controle;
} else {
  controle =
    props?.data?.controle ??
    route?.params?.controle ??
    $q.sessionStorage.getItem('cloneId');
}
let isDuplicating = $q.sessionStorage.getItem('cloneId');
onMounted(() => {
  ordemServicoStore.loading.page = true;
});

const importarProdutoServicoOuApenasProduto = $q.sessionStorage.getItem(
  'importarProdutoServicoOuApenasProduto'
);

$q.sessionStorage.remove('importarProdutoServicoOuApenasProduto');

const setOticaDataOnEditValues = (vendaWithOs) => {
  editValues = {
    ...editValues,
    otica: {
      receita: vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithReceita,
      adicional: {
        ...vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional,
        lente: {
          ...vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional
            ?.vendaOsOticaAdicionalWithLente,
          tratamentos:
            vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional
              ?.vendaOsOticaAdicionalWithLente
              ?.vendaOsOticaAdicionalLenteWithTratamento,

          // somente usado no front para validacao
          tratamentosFront:
            vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional
              ?.vendaOsOticaAdicionalWithLente
              ?.vendaOsOticaAdicionalLenteWithTratamento
        },
        armacao:
          vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional
            ?.vendaOsOticaAdicionalWithArmacao,
        alturaCentroOtico:
          vendaWithOs?.vendaOsWithOtica?.vendaOsOticaWithAdicional
            ?.vendaOsOticaAdicionalWithAlturaCentroOtico ?? {}
      }
    }
  };
};
const updateEditValues = (response) => {
  const {
    cliente,
    funcionario,
    vendaWithItem: itens,
    tipoValorAcrescimo,
    tipoValorDesconto,
    vendaWithOs,

    ...venda
  } = response;
  osReferenciada.value = vendaWithOs.vendaOsWithOsReferenciada;

  codInternoSituacao.value = isDuplicating
    ? ''
    : vendaWithOs?.situacao?.codInterno;
  editValues = {
    os: {
      ...vendaWithOs,
      tipo: vendaWithOs.tipo,
      codSituacao: statusDisable.includes(vendaWithOs?.situacao?.codInterno)
        ? vendaWithOs?.situacao?.codInterno === '4' &&
          route.path.includes('editar')
          ? vendaWithOs.codSituacao
          : '1'
        : vendaWithOs.codSituacao,
      objeto: {
        codObjeto: vendaWithOs.vendaOsWithObjeto.codobjeto,
        solicitacao: vendaWithOs.vendaOsWithObjeto.solicitacao
      }
    },
    venda: {
      ...venda,
      valorBruto: venda.valorBruto,

      cliente,
      funcionario,
      tipoValorFrete: null,
      tipoValorAcrescimo: Number(tipoValorAcrescimo),
      tipoValorDesconto: Number(tipoValorDesconto),
      modulo: 5
    },
    item:
      itens?.map((item, index) => {
        let grade = item?.vendaItemWithGrade ?? [];
        return {
          ...item,
          controle: isDuplicating ? null : item.controle,
          codVenda: venda.controle,
          tipoValorAcrescimo: item?.vendaWithItem?.tipoValorAcrescimo,
          tipoValorDesconto: item?.vendaWithItem?.tipoValorDesconto,
          qtde: parseFloat(item?.qtde ?? 0),
          qtdeDevolvida: parseFloat(item?.qtdeDevolvida),
          devolvido:
            Number(item?.qtde ?? 0) <= 0
              ? Number(item?.qtdeDevolvida ?? 0) > 0
              : false,
          // Sempre garantir que grade seja um array, nunca null
          grade: Array.isArray(grade) ? grade : [],
          product: item.vendaItemWithProduto,
          fiscal: item.vendaItemWithFiscal,
          codFuncionarioResponsavel: item.codFuncionarioResponsavel,
          index,
          os:
            item?.vendaItemWithOs?.codArt && item?.vendaItemWithOs?.codObra
              ? {
                  codArt: item?.vendaItemWithOs?.codArt,
                  codObra: item?.vendaItemWithOs?.codObra
                }
              : null
        };
      }) ?? []
  };

  if (vendaWithOs?.vendaOsWithOtica) {
    setOticaDataOnEditValues(vendaWithOs);
  }
  return editValues;
};

if (controle) {
  try {
    const response = await sales.get({ controle });

    if (!response?.success) {
      cancel();
    }

    updateEditValues(response.data.rowsData);

    if (isDuplicating) {
      controle = null;
      $q.sessionStorage.remove('cloneId');
    }
  } catch (e) {
    console.error(e);
    cancel();
  }
} else if (codOrcamento) {
  try {
    const response = await orcamentoStore.get(codOrcamento);

    if (!response?.success) {
      cancel();
    }
    let itens;

    const {
      cliente,
      funcionario,
      vendaWithItem,
      tipoValorAcrescimo,
      tipoValorDesconto,
      vendaWithFuncionarioResponsavel,
      ...venda
    } = response.data.rowsData;
    itens = vendaWithItem;
    itens =
      importarProdutoServicoOuApenasProduto === 'produtos'
        ? itens.filter(
            (item) =>
              item.vendaItemWithProduto?.produtoWithCaracteristica
                ?.codTipoUso != '10'
          )
        : itens;
    editValues = {
      os: {
        tipo: ordemServicoStore.pageState?.tipoOS
      },
      venda: {
        ...venda,
        valorBruto: venda.valorBruto,
        cliente,
        funcionario,
        tipoValorFrete: null,
        tipoValorAcrescimo: Number(tipoValorAcrescimo),
        tipoValorDesconto: Number(tipoValorDesconto),
        davImportada: codOrcamento,
        modulo: 5
      },
      item:
        itens?.map((item, index) => {
          let grade = item?.vendaItemWithGrade ?? [];

          return {
            ...item,
            controle: null,
            codVenda: item.controle,
            qtde: parseFloat(item?.qtde ?? 0),
            qtdeDevolvida: parseFloat(item?.qtdeDevolvida),
            devolvido:
              Number(item?.qtde ?? 0) <= 0
                ? Number(item?.qtdeDevolvida ?? 0) > 0
                : false,
            // Sempre garantir que grade seja um array, nunca null
            grade: Array.isArray(grade) ? grade : [],
            product: item.vendaItemWithProduto,
            fiscal: item.vendaItemWithFiscal,
            codFuncionarioResponsavel: vendaWithFuncionarioResponsavel.controle,
            index: index,
            os:
              item?.vendaItemWithOs?.codArt && item?.vendaItemWithOs?.codObra
                ? {
                    codArt: item?.vendaItemWithOs?.codArt,
                    codObra: item?.vendaItemWithOs?.codObra
                  }
                : null
          };
        }) ?? []
    };

    router.replace({ query: null });
  } catch (error) {
    cancel();
  }
}

const {
  values,
  fields,
  validate,

  handleReset,
  errors,
  meta,
  isSubmitting,
  setFieldValue,
  setValues
} = useFormSchema(editValues);

codSituacao.value = fields.value.os.codSituacao.value;

const hasItems = computed(() => {
  return fields.value.item.value?.filter((item) => !item.deletar).length > 0;
});
const enableFinalizar = computed(() => {
  const value = fields.value.os.dataHoraEntrega.value;
  return !!value && value !== '';
});

const peopleInputRef = ref();

const updatePeopleOptions = async () => {
  peopleInputRef.value?.mountSelectOptions();
};

const openRegisterPeople = (controle = null) => {
  let props = {};

  if (controle) {
    props = {
      data: {
        isEditing: true,
        controle
      },
      modal: true
    };
  }

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'register-people',
      ...props
    }
  }).onOk(async () => {
    await updatePeopleOptions();
  });
};

const employeeInputRef = ref();

const objectInputRef = ref();

const situationInputRef = ref();

const updateEmployeeOptions = async () => {
  employeeInputRef.value?.mountSelectOptions();
};
const updateSituationOptions = async () => {
  situationInputRef.value?.reMount();
};

const openRegisterSituacao = () => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: RegisterSituacao,
      scope: 'register-situacao',
      hasCancel: false,
      hasSave: false,
      title: 'Cadastrar situação',
      dataModal: { inputLabel: 'Cadastrar situação' }
    }
  }).onOk(async () => {
    await updateSituationOptions();
  });
};
const openRegisterEmployee = () => {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterEmployee,
      scope: 'register-employee'
    }
  }).onOk(async () => {
    await updateEmployeeOptions();
  });
};

const updateObjectOptions = async () => {
  objectInputRef.value?.reMount();
};

const openRegisterObjects = () => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: RegisterObjectsModal,
      scope: 'register-object',
      hasCancel: false,
      hasSave: false,
      title: 'Cadastrar objetos',
      dataModal: { inputLabel: 'Cadastrar objetos' }
    }
  }).onOk(async () => {
    await updateObjectOptions();
  });
};

const reset = () => {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    handleReset(), osReferenciadaRef.value?.removerOsReferenciada();
  });
};

function calcValorTotalItem(item) {
  const bruto = Number(item.qtde) * Number(item.valorUnitario);

  let desconto = 0;
  let acrescimo = 0;

  if (item.tipoValorDesconto === 0) {
    desconto = round(item.valorDesconto ?? 0, 2);
  } else {
    desconto = round((bruto * item.valorDesconto) / 100, 2);
  }

  if (item.tipoValorAcrescimo === 0) {
    acrescimo = round(item.valorAcrescimo, 2);
  } else {
    acrescimo = round((bruto * item.valorAcrescimo) / 100, 2);
  }

  return round(bruto - desconto + acrescimo, 2);
}
const typeDescontoAcrescimo = [
  { label: 'Todos', value: 1 },
  { label: 'Produtos', value: 2 },
  { label: 'Serviços', value: 3 }
];

const orcamentoPriceTable = computed(() => {
  let productTotal = [];
  let serviceTotal = [];
  let grossTotal = [];

  // tipoValor Desconto/Acrescimo/Frete = { 0: Dinheiro, 1: Porcentagem}

  values?.item?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorTotalItem(item);
    }

    const calcGross = item.codProduto ? item.valorUnitario * item.qtde : 0;

    // Verificar se é um serviço, cujo controle é igual a 10
    if (item?.product?.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);

      grossTotal.push(calcGross);
    } else {
      productTotal.push(calc);

      grossTotal.push(calcGross);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );
  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  let grossTotalSum = grossTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const itemsTotal = round(productTotalSum + serviceTotalSum, 2);

  const acrescimo = ref(0);
  const desconto = ref(0);
  const frete = ref(0);

  // Calcula o valor do acréscimo
  if (values.venda.tipoValorAcrescimo === 1 && values.venda.acrescimo > 0) {
    const percentage = Number(round(values.venda.acrescimo / 100, 2));
    if (values.venda.aplicaAcrescimoDescontoEm == 2) {
      acrescimo.value = Number(round(percentage * productTotalSum, 2));
    } else if (values.venda.aplicaAcrescimoDescontoEm == 3) {
      acrescimo.value = Number(round(percentage * serviceTotalSum, 2));
    } else {
      acrescimo.value = Number(round(percentage * itemsTotal, 2));
    }
  } else if (
    values.venda.tipoValorAcrescimo === 0 &&
    values.venda.acrescimo > 0
  ) {
    acrescimo.value = values.venda.acrescimo;
  }

  // Calcula o valor do desconto
  if (values.venda.tipoValorDesconto === 1 && values.venda.desconto > 0) {
    const percentage = Number(round(values.venda.desconto / 100, 2));
    if (values.venda.aplicaAcrescimoDescontoEm == 2) {
      desconto.value = Number(round(percentage * productTotalSum, 2));
    } else if (values.venda.aplicaAcrescimoDescontoEm == 3) {
      desconto.value = Number(round(percentage * serviceTotalSum, 2));
    } else {
      desconto.value = Number(round(percentage * itemsTotal, 2));
    }
  } else if (
    values.venda.tipoValorDesconto === 0 &&
    values.venda.desconto > 0
  ) {
    desconto.value = values.venda.desconto;
  }

  // Calcula o valor do frete
  if (values.venda.tipoValorFrete === 1 && values.venda.frete > 0) {
    const percentage = round(values.venda.frete / 100, 2);
    frete.value = round(percentage * itemsTotal, 2);
  } else if (values.venda.tipoValorFrete === 0 && values.venda.frete > 0) {
    frete.value = values.venda.frete;
  }

  const total = round(
    itemsTotal + acrescimo.value + frete.value - desconto.value || 0,
    2
  );

  grossTotalSum = round(grossTotalSum, 2);
  setFieldValue('venda.valorLiquido', total);
  setFieldValue('venda.valorBruto', grossTotalSum);
  setFieldValue('venda.valorProdutos', productTotalSum);
  setFieldValue('venda.valorServicos', serviceTotalSum);

  return {
    itemsTotal,
    grossTotalSum,
    productTotalSum,
    serviceTotalSum,
    total,
    principalText: 'Totais da ordem de serviço',
    labelsAndPrices: [
      {
        label: 'Valor dos produtos',
        value: productTotalSum || 0,
        isMoney: true
      },
      {
        label: 'Valor dos serviços',
        value: serviceTotalSum || 0,
        isMoney: true
      },
      { label: 'Acréscimo', value: acrescimo.value, isMoney: true },
      { label: 'Desconto', value: desconto.value, isMoney: true },
      {
        label: 'Total',
        value: itemsTotal + acrescimo.value + frete.value - desconto.value || 0,
        isMoney: true
      }
    ]
  };
});

const openModalConstrucaoCivil = (row) => {
  const index = fields.value.item.value.findIndex((item) => {
    return item === row;
  });
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: ConstrucaoCivilModal,
      scope: 'construcao-civil',
      title: 'Código ART',
      hasSave: false,
      hasCancel: false,
      dataModal: {
        row
      }
    }
  }).onOk((value) => {
    setFieldValue(`item[${index}].os.codArt`, value.cardArtModel);
    setFieldValue(`item[${index}].os.codObra`, value.cardObraModel);
  });
};

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}
const disableHotkeys = () => {
  setTimeout(() => {
    hotkeys.filter = function (e) {
      var tagName = e.target.tagName;

      if (global.isPlatformMobile) return false;

      return !(tagName == 'INPUT');
    };
  }, 300);
};

function redirectToIndex() {
  isWarningAnimationDisabled.value = true;
  if (modal.value) {
    emit('ok');
    isAdiantamento.value = false;
  } else {
    router.push('/os/listagem');
    isAdiantamento.value = false;
  }
}

const finalizar = async () => {
  const responseSalvar = await save(true);
  if (responseSalvar) {
    await api.patch(
      `/api/ordem-servico/${responseSalvar.data?.controle}/finalizar`
    );
  }

  if (await isFormValid()) {
    redirectToIndex();
  }
};

const dataToUse = ref({ venda: null, os: null });
const save = async (keepDialogOpen = false) => {
  if (!(await isFormValid())) return;

  notifyLoading();
  let response;

  if (!keepDialogOpen) {
    setValues({
      ...values,
      venda: {
        ...values.venda,
        acrescimo: values.venda.acrescimo,
        desconto: values.venda.desconto,
        valorBruto: round(orcamentoPriceTable.value.grossTotalSum, 2),
        valorLiquido: round(orcamentoPriceTable.value.total, 2),
        valorProdutos: round(orcamentoPriceTable.value.productTotalSum, 2),
        valorServicos: round(orcamentoPriceTable.value.serviceTotalSum, 2),
        controle: controle ?? values.venda?.controle ?? null
      }
    });
  }

  let payload = toRaw(values);

  payload = {
    ...payload,
    venda: {
      ...values.venda,
      acrescimo: values.venda.acrescimo,
      desconto: values.venda.desconto,
      valorBruto: round(orcamentoPriceTable.value.grossTotalSum, 2),
      valorLiquido: round(orcamentoPriceTable.value.total, 2),
      valorProdutos: round(orcamentoPriceTable.value.productTotalSum, 2),
      valorServicos: round(orcamentoPriceTable.value.serviceTotalSum, 2),
      controle: controle ?? values.venda?.controle ?? null
    }
  };

  /* CASO SEJA ROTA EDITAR */
  if (controle) {
    payload.item.forEach((item) => {
      item.lote = returnObjOrNull(item.lote);
      item.codVenda = controle;
    });

    response = await ordemServicoStore.put({ payload });
  } else {
    if (payload.item && Array.isArray(payload.item)) {
      payload.item.forEach((item) => {
        if (item) {
          item.grade = Array.isArray(item.grade) ? item.grade : [];
        }
      });
    }

    response = await ordemServicoStore.post({ payload });
  }
  notifyLoading({ done: true });

  if (response?.success) {
    controle = response.data?.controle;
    if (!keepDialogOpen) redirectToIndex();
  }

  return response;
};

const adiantar = async (realizarAdiantamento) => {
  const response = await save(true);
  if (!response?.success) return;

  controle = response.data?.controle;
  const modalPromiseResult = await new Promise((resolve) => {
    if (!response.data?.vendaPaga) {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: PaymentMethod,
          scope: 'payment-method',
          hasCancel: false,
          hasSave: false,
          dataModal: {
            totalProp: orcamentoPriceTable.value.total,
            valorPago: response.data?.valorPago ?? 0,
            vendaProp: response.data,
            isAdiantamento: realizarAdiantamento,
            emitir: true
          }
        }
      })
        .onOk(() => resolve(true))
        .onCancel(() => {
          if (controle) {
            isWarningAnimationDisabled.value = true;
            router.push(`/os/editar/${controle}`);
            isDuplicating = false;
          }
          resolve(true);
        });
    }
  });

  if (modalPromiseResult) {
    const { data } = await ordemServicoStore.get({ controle: controle });

    dataToUse.value.os = updateEditValues(data.rowsData);
    dataToUse.value.venda = data.rowsData;

    if (!dataToUse.value.venda.vendaPaga) {
      setValues({
        ...dataToUse.value.os,
        os: {
          tipo: Number(dataToUse.value.os.os.tipo)
        },
        venda: {
          ...dataToUse.value.venda,
          controle: controle,
          tipoValorAcrescimo: Number(dataToUse.value.venda.tipoValorAcrescimo),
          tipoValorDesconto: Number(dataToUse.value.venda.tipoValorDesconto)
        }
      });
    }
  }

  if (dataToUse.value.venda.vendaPaga) {
    redirectToIndex();
  }
};
const applyItemInfoAdicional = (index, controle, data) => {
  // Itera sobre os itens para encontrar o item correspondente ao controle

  fields.value.item.value.forEach((item, i) => {
    if (i === index && item.controle === controle) {
      if (!item.fiscal) item.fiscal = {};
      item.fiscal.infosAdicionais = data.infosAdicionais;
    }
  });
};

const openInfoAdicionalModal = (row) => {
  const produto = fields.value.item.value[row.index];

  const item = {
    index: row.index,
    controle: produto?.controle,
    produto: produto?.product,
    fiscal: {
      infosAdicionais: produto?.fiscal?.infosAdicionais
    }
  };

  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: InfosAdicionalModal,
      scope: 'Infos-adicionais',
      title: 'Dados do produto',
      classesTopBox: '!tw-justify-start !tw-mb-2 ',
      hasSave: false,
      hasCancel: false,
      dataModal: {
        item
      }
    }
  }).onOk(({ index, controle, data }) => {
    applyItemInfoAdicional(index, controle, data);
  });
};

const faturar = async () => {
  if (codInternoSituacao.value !== '4') return;
  const { data, success } = await ordemServicoStore.get({ controle: controle });
  if (!success) return;

  const response = data?.rowsData;

  const modalPromiseResult = await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: PaymentMethod,
        scope: 'payment-method',
        hasCancel: false,
        hasSave: false,
        dataModal: {
          totalProp: orcamentoPriceTable.value.total,
          vendaProp: response,
          emitir: true
        }
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });

  if (modalPromiseResult) {
    redirectToIndex();
  }
};

const actionBarEventsReadOnly = {
  goBack: {
    callback: cancel
  }
};

const actionBarEvents = {
  save: {
    callback: save,
    condition: () => codInternoSituacao.value != '4'
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset,
    condition: () => codInternoSituacao.value != '4'
  },
  adiantar: {
    callback: () => adiantar(true),
    condition: () => codInternoSituacao.value != '4'
  },
  finalizar: {
    callback: finalizar,
    condition: () => codInternoSituacao.value != '4'
  },
  finalizarFaturar: {
    callback: () => adiantar(false),
    condition: () => codInternoSituacao.value != '4'
  },
  faturar: {
    callback: faturar,
    condition: () => codInternoSituacao.value == '4'
  }
};
const actionBarButtons = useRegisterOrdemServico({
  params: {
    modal: props.modal,
    isWarningAnimationDisabled,
    hasItems,
    enableFinalizar,
    codSituacao
  },
  readonly: readonlyOrDisable.value,
  events: readonlyOrDisable.value ? actionBarEventsReadOnly : actionBarEvents
});

const tipoOSoptions = [
  { label: 'Simples', value: 1 },
  { label: 'Ótica', value: 2 },
  { label: 'Mecânica', value: 3 },
  { label: 'Petshop', value: 4 },
  { label: 'Farmácia', value: 5 },
  { label: 'Hotel', value: 6 },
  { label: 'Produtor rural', value: 7 }
];
const tipoOs = tipoOSoptions.filter((type) => {
  const selectedTipo =
    ordemServicoStore.pageState?.tipoOS || fields.value.os.tipo.value;

  return type.value == selectedTipo;
});

watch(
  values,
  async () => {
    if (
      (Number(localStorage?.disableUndoEdit) && controle) ||
      (Number(localStorage?.disableUndoCreate) && !controle)
    ) {
      isWarningAnimationDisabled.value = true;
    } else {
      isWarningAnimationDisabled.value = true;
    }
  },
  {
    immediate: true,
    deep: true
  }
);
//useScopedHotkeys();
</script>

<template>
  <NewSGRegisterPage
    beta
    ref="registerPageRef"
    id="ordemservico-cadastro"
    :title="
      ($route.path.includes('editar') && !props.modal
        ? 'Edição'
        : readonlyOrDisable
        ? 'Visualização'
        : 'Cadastro') + ` de ordem de serviço - ${tipoOs?.[0]?.label}`
    "
    :focus-on-mounted="false"
    :buttons="actionBarButtons"
    :action-text="
      codInternoSituacao
        ? 'A ordem de serviço foi finalizada. Deseja faturá-la agora?'
        : null
    "
    :disable-warning-animation="isWarningAnimationDisabled"
    :readonly-or-disable="readonlyOrDisable"
    :modal="modal"
  >
    <NewSGCard
      id="register-os-detalhes"
      title="Dados cadastrais"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      has-title-separator
      has-circle
      remove-gap
    >
      <InputSelect
        ref="tipoOS"
        label="Tipo da OS"
        v-model="fields.os.tipo.value"
        :model-value="fields.os.tipo.value"
        :error="!!fields.os.tipo.errorMessage"
        :error-message="fields.os.tipo.errorMessage"
        :valid="fields.os.tipo.meta.valid"
        behavior="menu"
        v-bind="{
          ...ModelInputSelect,
          params: { optionsStatic: tipoOSoptions }
        }"
        option-label="label"
        option-value="value"
        :clearable="false"
        class="tw-col-span-4"
        required
        :readonly="true"
      />
      <InputSelect
        ref="peopleInputRef"
        label="Cliente"
        v-model="fields.venda.codCliente.value"
        :error="!!fields.venda.codCliente.errorMessage"
        :error-message="fields.venda.codCliente.errorMessage"
        :valid="fields.venda.codCliente.meta.valid"
        :block-editable="['1']"
        v-bind="{
          ...ModelInputSelectSearch,
          params: {
            apiRoute: '/api/pessoa',
            global: true,
            filterOr: ['controle', 'razaosocial', 'cnpjcpf'],
            filters: {
              'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao': {
                filterType: 'ILIKE',
                filterValue: 'Cliente'
              },
              deleted_at: {
                filterType: 'NULL'
              }
            },
            orderBy: {
              controle: 'desc'
            },
            optionDescription: (opt) => {
              if (!opt || !opt?.cnpjCpf) return '';
              return formatCPFCNPJ(opt?.cnpjCpf);
            },
            optionDetails: (opt) => {
              const endereco = opt?.pessoaWithEndereco?.endereco
                ? opt?.pessoaWithEndereco?.endereco + ', '
                : '';
              const cidade = opt?.pessoaWithEndereco?.cidade ?? '';
              const uf = opt?.pessoaWithEndereco?.uf
                ? ' - ' + opt?.pessoaWithEndereco?.uf
                : '';

              return endereco + cidade + uf;
            }
          }
        }"
        add-button
        edit-button
        @on-add="() => openRegisterPeople()"
        @on-edit="openRegisterPeople"
        role-prefix="PESSOA"
        option-label="descricaoCodigo"
        option-value="controle"
        class="tw-col-span-4"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        required
      />

      <InputSelect
        ref="situationInputRef"
        label="Situação"
        v-model="fields.os.codSituacao.value"
        :error="!!fields.os.codSituacao.errorMessage"
        :error-message="fields.os.codSituacao.errorMessage"
        :valid="fields.os.codSituacao.meta.valid"
        v-bind="{
          ...ModelInputSelectSearch,
          params: {
            apiRoute: '/api/ordem-servico/situacao',
            filtersV2: statusDisable.includes(codInternoSituacao)
              ? [
                  {
                    field: 'deleted_at',
                    filterType: 'NULL'
                  }
                ]
              : [
                  {
                    field: 'deleted_at',
                    filterType: 'NULL'
                  },
                  {
                    group: [
                      {
                        field: 'codinterno',
                        filterType: 'NOT_IN_ARRAY',
                        filterValue: [4, 5, 7, 8]
                      },
                      {
                        operator: 'OR',
                        field: 'codinterno',
                        filterType: 'NULL'
                      }
                    ]
                  }
                ],
            orderBy: {
              controle: 'asc'
            }
          }
        }"
        add-button
        edit-button
        @on-add="() => openRegisterSituacao()"
        @on-edit="openRegisterSituacao"
        option-label="descricao"
        option-value="controle"
        class="tw-col-span-4"
        :readonly="
          readonlyOrDisable || statusDisable.includes(codInternoSituacao)
        "
        required
      />
      <InputSelect
        ref="employeeInputRef"
        label="Funcionário responsável"
        v-model="fields.venda.codFuncionarioResponsavel.value"
        :error="!!fields.venda.codFuncionarioResponsavel.errorMessage"
        :error-message="fields.venda.codFuncionarioResponsavel.errorMessage"
        :valid="fields.venda.codFuncionarioResponsavel.meta.valid"
        v-bind="{
          ...ModelInputSelectSearch,
          params: {
            apiRoute: '/api/funcionario/select',
            filterOr: ['controle', 'nome'],
            filtersV2: [
              {
                field: 'deleted_at',
                filterType: 'NULL'
              },
              {
                not_deleted: true,
                field: 'codUsuario',
                filterType: 'FIXED',
                filterValue: fields.venda.codFuncionarioResponsavel.value
                  ? fields.venda.codFuncionarioResponsavel.value
                  : global?.user?.controle
              }
            ],
            orderBy: {
              controle: 'asc'
            }
          }
        }"
        add-button
        @on-add="() => openRegisterEmployee()"
        role-prefix="FUNCIONARIO"
        option-label="descricaoCodigo"
        option-value="controle"
        class="tw-col-span-4"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        required
      />
      <DatePicker
        :value="fields.os.dataHoraEntrega.value"
        @value="(val) => fields.os.dataHoraEntrega.setValue(val)"
        :error="!!fields.os.dataHoraEntrega.errorMessage"
        :error-message="fields.os.dataHoraEntrega.errorMessage"
        :valid="fields.os.dataHoraEntrega.meta.valid"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        v-bind="ModelInputText"
        use-current-time
        maxlength="100"
        label="Data/Hora entrega"
        class="tw-col-span-4"
        time
      />
      <DatePicker
        :value="fields.os.dataPrevisaoEntrega.value"
        @value="(val) => fields.os.dataPrevisaoEntrega.setValue(val)"
        :error="!!fields.os.dataPrevisaoEntrega.errorMessage"
        :error-message="fields.os.dataPrevisaoEntrega.errorMessage"
        :valid="fields.os.dataPrevisaoEntrega.meta.valid"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        v-bind="ModelInputText"
        maxlength="100"
        label="Data previsão"
        class="tw-col-span-4"
      />
    </NewSGCard>
    <NewSGCard
      id="register-os-referencia"
      title="Ordem de serviço referenciada"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      :has-circle="!readonlyOrDisable"
      has-title-separator
      remove-gap
      container-class="!tw-grid !tw-grid-cols-12 !tw-gap-0"
    >
      <OsReferenciada
        :data="osReferenciada"
        ref="osReferenciadaRef"
        :readonly-or-disable="readonlyOrDisable || codInternoSituacao == '4'"
        v-model:fields="fields"
        v-model:set-field-value="setFieldValue"
      />
    </NewSGCard>
    <NewSGCard
      ref="objetoEquipamentoRef"
      id="register-os-objeto-equipamento"
      title="Objeto/equipamento"
      :cols="8"
      container-class="tw-pb-4"
      :schema-errors="errors"
      :meta="meta"
      :has-circle="!readonlyOrDisable"
      has-title-separator
      remove-gap
    >
      <InputSelect
        ref="objectInputRef"
        label="Objeto/equipamento"
        v-model="fields.os.objeto.codObjeto.value"
        :error="!!fields.os.objeto.codObjeto.errorMessage"
        :error-message="fields.os.objeto.codObjeto.errorMessage"
        :valid="fields.os.objeto.codObjeto.meta.valid"
        v-bind="{
          ...ModelInputSelectSearch,
          params: {
            apiRoute: '/api/ordem-servico/objeto',
            filters: {
              deleted_at: {
                filterType: 'NULL'
              }
            },
            orderBy: {
              controle: 'asc'
            }
          }
        }"
        role-prefix="ORDEM-SERVICO.OBJETO"
        option-label="descricao"
        option-value="controle"
        class="tw-col-span-4"
        add-button
        @on-add="() => openRegisterObjects()"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        required
      />
      <q-input
        v-model="fields.os.objeto.solicitacao.value"
        :error="!!fields.os.objeto.solicitacao.errorMessage"
        :error-message="fields.os.objeto.solicitacao.errorMessage"
        :valid="fields.os.objeto.solicitacao.meta.valid"
        v-bind="ModelInputText"
        type="textarea"
        outlined
        autogrow
        class="custom-textarea-required tw-col-span-full"
        maxlength="255"
        input-class="tw-text-SGBRGray tw-p-0 !tw-mb-0"
        input-style="min-height: 7rem"
        label="Solicitação"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        @focus="disableHotkeys"
        @blur="
          () => {
            hotkeys.filter = () => (global.isPlatformMobile ? false : true);
          }
        "
        required
      />
    </NewSGCard>
    <NewSGCard
      id="register-os-items"
      title="Itens"
      :cols="12"
      container-class="tw-pb-4"
      :schema-errors="errors"
      :meta="meta"
      :has-circle="!readonlyOrDisable"
      has-title-separator
      remove-gap
      :error-keys="['item']"
    >
      <OSItems
        v-model:itens="fields.item"
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        :cod-funcionario-responsavel-default="
          fields.venda.codFuncionarioResponsavel.value
        "
        ref="productInputRefs"
        class="tw-col-span-12"
        @open-info-adicional="(row) => openInfoAdicionalModal(row)"
        @open-modal-construcao-civil="(row) => openModalConstrucaoCivil(row)"
      />
      <div
        class="tw-col-span-full tw-flex tw-grid-cols-12 tw-flex-col tw-justify-between lg:tw-grid"
        v-if="hasItems"
      >
        <div class="tw-col-span-6 tw-items-start">
          <span class="tw-text-xs tw-font-bold"
            >Aplicar desconto/acréscimo por:</span
          >

          <q-option-group
            v-model="fields.venda.aplicaAcrescimoDescontoEm.value"
            :options="typeDescontoAcrescimo"
            :disable="readonlyOrDisable || codInternoSituacao == '4'"
            size="30px"
            color="primary"
            inline
          />
        </div>
        <div
          class="tw-col-span-6 tw-flex tw-grid-cols-8 tw-flex-col tw-items-end tw-gap-4 lg:tw-grid"
        >
          <InputPricePercent
            v-model="fields.venda.acrescimo.value"
            v-model:toggle-model="fields.venda.tipoValorAcrescimo.value"
            :error="!!fields.venda.acrescimo.errorMessage"
            :error-message="fields.venda.acrescimo.errorMessage"
            :valid="fields.venda.acrescimo.meta.valid"
            ref="acrescimoInputRef"
            class="tw-col-span-4"
            label="Acréscimo"
            stack-label
            v-bind="ModelInputText"
            :readonly="readonlyOrDisable || codInternoSituacao == '4'"
          />

          <InputPricePercent
            v-model="fields.venda.desconto.value"
            v-model:toggle-model="fields.venda.tipoValorDesconto.value"
            :error="!!fields.venda.desconto.errorMessage"
            :error-message="fields.venda.desconto.errorMessage"
            :valid="fields.venda.desconto.meta.valid"
            class="tw-col-span-4"
            label="Desconto"
            stack-label
            ref="descontoInputRef"
            :readonly="readonlyOrDisable || codInternoSituacao == '4'"
          />
        </div>
      </div>

      <ResultTable
        v-if="hasItems"
        :principal-text="orcamentoPriceTable?.principalText"
        :labels-and-prices="orcamentoPriceTable?.labelsAndPrices"
      />
    </NewSGCard>
    <NewSGCard
      id="register-os-laudo-técnico"
      title="Laudo Técnico"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      :has-circle="!readonlyOrDisable || codInternoSituacao == '4'"
      has-title-separator
      remove-gap
    >
      <q-input
        v-model="fields.os.laudoTecnico.value"
        :error="!!fields.os.laudoTecnico.errorMessage"
        :error-message="fields.os.laudoTecnico.errorMessage"
        :valid="fields.os.laudoTecnico.meta.valid"
        v-bind="ModelInputText"
        type="textarea"
        outlined
        autogrow
        class="tw-col-span-full"
        maxlength="255"
        input-class="tw-text-SGBRGray tw-p-0"
        input-style="min-height: 7rem"
        stack-label
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        @focus="disableHotkeys"
        @blur="
          () => {
            hotkeys.filter = () => (global.isPlatformMobile ? false : true);
          }
        "
      />
    </NewSGCard>
    <NewSGCard
      id="register-os-obs"
      title="Observações"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      :has-circle="!readonlyOrDisable"
      has-title-separator
      remove-gap
    >
      <q-input
        v-model="fields.venda.observacoes.value"
        :error="!!fields.venda.observacoes.errorMessage"
        :error-message="fields.venda.observacoes.errorMessage"
        :valid="fields.venda.observacoes.meta.valid"
        v-bind="ModelInputText"
        type="textarea"
        outlined
        autogrow
        class="tw-col-span-full"
        maxlength="255"
        input-class="tw-text-SGBRGray tw-p-0"
        input-style="min-height: 7rem"
        stack-label
        :readonly="readonlyOrDisable || codInternoSituacao == '4'"
        @focus="disableHotkeys"
        @blur="
          () => {
            hotkeys.filter = () => (global.isPlatformMobile ? false : true);
          }
        "
      />
    </NewSGCard>
    <NewSGCard
      v-if="fields.os.tipo.value == 2"
      id="receita"
      title="Receita"
      :schema-errors="errors"
      :meta="meta"
      :cols="8"
      :has-circle="!readonlyOrDisable"
      has-title-separator
      remove-gap
    >
      <Receita
        v-if="fields.os.tipo.value == 2"
        :readonly-or-disable="readonlyOrDisable || codInternoSituacao == '4'"
        v-model:fields="fields"
        v-model:values="values"
        v-model:set-field-value="setFieldValue"
      />
    </NewSGCard>

    <Adicionais
      v-if="fields.os.tipo.value == 2"
      v-model:fields="fields"
      v-model:meta="meta"
      v-model:errors="errors"
      v-model:values="values"
      :readonly-or-disable="readonlyOrDisable || codInternoSituacao == '4'"
      v-model:set-field-value="setFieldValue"
    />
  </NewSGRegisterPage>
</template>
