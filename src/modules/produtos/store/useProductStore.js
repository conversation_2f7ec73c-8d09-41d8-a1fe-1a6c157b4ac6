import API from 'src/stores/Api';
import { createStore } from 'src/stores/storeApiFactory';

const options = {
  name: 'product',
  filtersV2: [],
  relationships: [
    'produtoWithCaracteristica',
    'produtoWithEstoque',
    'produtoWithPersonalizados',
    'produtoWithFiscal',
    'produtoWithTributacao',
    'produtoWithFiscal.produtoFiscalWithCfop',
    'produtoWithFiscal.produtoFiscalWithCestNew',
    'produtoWithFiscal.produtoFiscalWithNcmNew',
    'produtoWithFiscal.produtoFiscalWithCsosn',
    'produtoWithFiscal.produtoFiscalWithCst',
  ]
};

const api_instance = new API({
  url_default: '/api/produto',
  url_restore: '/api/produto/restore',
  url_post: '/api/produto/geral',
  url_put: '/api/produto/geral'
});

export const useProductStore = createStore(api_instance, options);
