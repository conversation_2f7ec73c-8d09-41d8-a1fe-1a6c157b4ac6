<template>
  <SGRegisterPage
    ref="registerPageRef"
    id="register-label-template"
    :title="
      readonlyOrDisable
        ? 'Visualizar modelo personalizado'
        : controle
        ? 'Editar modelo personalizado'
        : 'Cadastro de modelo personalizado'
    "
    :focus-on-mounted="false"
    action-bar
    hide-side-nav
    :buttons="actionBarButtons"
    :disable-warning-animation="isWarningAnimationDisabled"
    :readonly-or-disable="readonlyOrDisable"
    wrapper-class="!tw-pb-0"
  >
    <template
      class="tw-flex tw-grid-cols-12 tw-flex-col-reverse tw-gap-4 lg:tw-grid"
    >
      <SGCard
        id="custom-label-template"
        :schema-errors="errors"
        :meta="meta"
        :cols="12"
        remove-gap
        has-title-separator
        title="Modelos"
        class="tw-col-span-9 tw-text-left"
      >
        <template #buttons>
          <q-btn
            size="1rem"
            color="primary"
            label="Atalhos"
            @click="openShortkeysModal"
          />
        </template>

        <span class="tw-col-span-full tw-text-[10px]"
          >*É possível realizar edição e redimensionamento somente na primeira
          etiqueta.</span
        >

        <EtiquetaTemplate
          ref="templateRef"
          :scale="scaleSize"
          :paper-height="values.etiquetaConfiguracoes.alturaFolha"
          :paper-width="values.etiquetaConfiguracoes.larguraFolha"
          :column-gap="values.etiquetaConfiguracoes.entreEtiquetas"
          :label-height="values.etiquetaConfiguracoes.alturaEtiqueta"
          :label-width="values.etiquetaConfiguracoes.larguraEtiqueta"
          :label-quantity="values.etiquetaConfiguracoes.quantidade"
          :margins="margins"
          :columns="cols"
          :rows="rows"
        />

        <FieldOptions class="tw-col-span-3" />
      </SGCard>

      <div
        class="tw-top-24 tw-col-span-3 tw-flex tw-h-[77vh] tw-flex-col tw-items-center tw-justify-between tw-rounded-md tw-bg-white tw-p-4 tw-shadow-md lg:tw-sticky"
      >
        <SGCard
          id="modelos-template"
          :schema-errors="errors"
          :meta="meta"
          :cols="12"
          :has-title-separator="false"
          class="tw-col-span-12 tw-w-full !tw-p-0 tw-shadow-none"
          remove-gap
        >
          <q-input
            v-model="fields.nome.value"
            :error="!!fields.nome.errorMessage"
            :error-message="fields.nome.errorMessage"
            :valid="fields.nome.meta.valid"
            v-bind="ModelInputText"
            maxlength="60"
            label="Nome do modelo"
            class="tw-col-span-full"
            required
          />
        </SGCard>

        <div class="customScrollSmall tw-size-full tw-overflow-auto tw-pr-1">
          <q-expansion-item
            v-model="paperDetailsExpanded"
            icon="photo_size_select_small"
            label="Dimensões da folha"
            :header-class="[
              {
                'tw-bg-SGBRBlueLighten tw-text-SGBRWhite tw-rounded-md':
                  paperDetailsExpanded,
                'tw-bg-SGBRGrayBG tw-text-textsSGBR-gray': !paperDetailsExpanded
              },
              'tw-rounded-md'
            ]"
            :expand-icon-class="{
              'tw-text-SGBRWhite': paperDetailsExpanded
            }"
            class="tw-my-1 tw-transition-all tw-duration-200"
            expand-separator
          >
            <DimensoesFolha v-model:fields="fields" :values="values" />
          </q-expansion-item>

          <q-expansion-item
            v-model="labelDetailsExpanded"
            icon="fit_screen"
            label="Dimensões da etiqueta"
            :header-class="[
              {
                'tw-bg-SGBRBlueLighten tw-text-SGBRWhite tw-rounded-md':
                  labelDetailsExpanded,
                'tw-bg-SGBRGrayBG tw-text-textsSGBR-gray': !labelDetailsExpanded
              },
              'tw-rounded-md'
            ]"
            :expand-icon-class="{
              'tw-text-SGBRWhite': labelDetailsExpanded
            }"
            class="tw-my-1 tw-transition-all tw-duration-200"
            expand-separator
          >
            <DimensoesEtiqueta
              ref="RefDimensoesEtiqueta"
              v-model:fields="fields"
              :values="values"
            />
          </q-expansion-item>

          <q-expansion-item
            v-model="fieldsExpanded"
            icon="playlist_add"
            label="Dados e informações"
            :header-class="[
              {
                'tw-bg-SGBRBlueLighten tw-text-SGBRWhite tw-rounded-md':
                  fieldsExpanded,
                'tw-bg-SGBRGrayBG tw-text-textsSGBR-gray': !fieldsExpanded
              },
              'tw-rounded-md'
            ]"
            :expand-icon-class="{
              'tw-text-SGBRWhite': fieldsExpanded
            }"
            class="tw-my-1 tw-transition-all tw-duration-200"
            expand-separator
          >
            <Campos :fields-expanded="fieldsExpanded" />
          </q-expansion-item>
        </div>
        <div
          class="tw-mt-4 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-items-center tw-justify-around tw-gap-4 tw-bg-white tw-transition-all tw-duration-200 md:tw-flex-row"
        >
          <div class="tw-grid tw-max-w-[120px] tw-grid-cols-4">
            <q-btn
              @click="reduceScale"
              icon="remove"
              size="0.6rem"
              class="tw-col-span-1 tw-bg-SGBRGrayBG tw-text-xs tw-text-SGBRBlueLighten"
              unelevated
            ></q-btn>
            <div
              class="tw-col-span-2 tw-flex tw-w-full tw-flex-row tw-items-center tw-justify-center tw-bg-SGBRGrayBG tw-text-xs tw-font-medium tw-text-textsSGBR-gray"
            >
              {{ scaleSize * 100 }} %
            </div>

            <q-btn
              @click="increaseScale"
              icon="add"
              size="0.6rem"
              class="tw-col-span-1 tw-bg-SGBRGrayBG tw-text-xs tw-text-SGBRBlueLighten"
              unelevated
            ></q-btn>
          </div>

          <!-- <q-slider
            v-model="scaleSize"
            @update:model-value="
              (value) => {
                setScale(value);
                updateScale();
              }
            "
            :min="0.75"
            :max="1.25"
            :step="0.25"
            :label-value="`${scaleSize * 100}%`"
            :marker-labels="(val) => `${100 * val}%`"
            label
            color="primary"
            markers
            marker-labels-class="tw-text-[11px] tw-font-normal tw-text-textsSGBR-gray"
            class="tw-w-1/3"
          /> -->
          <q-btn
            v-authorized="'PRODUTO.ETIQUETA:PREVIEW'"
            @click="getPreview"
            label="Pré-visualizar"
            icon-right="visibility"
            color="primary"
            class="tw-h-fit"
          />
        </div>
      </div>
    </template>
  </SGRegisterPage>
</template>

<script setup>
// Core
import { computed, onUnmounted, ref, toRaw, watch } from 'vue';

// Components
import EtiquetaTemplate from 'src/modules/produtos/etiqueta/personalizada/components/EtiquetasTemplate.vue';

// Stores
import { useEtiquetaStore } from 'src/modules/produtos/etiqueta/store/useEtiquetaStore';

// Models
import _ from 'lodash';
import { Dialog, useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import { emitter as mitt } from 'src/boot/mitt';
import useRegister from 'src/components/actionBar/composables/useRegister';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { printBase64Pdf } from 'src/components/utils/pdf';
import { diffObjects } from 'src/components/utils/tests';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import ModelInputText from 'src/core/models/inputs/Text';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import Campos from 'src/modules/produtos/etiqueta/personalizada/components/Campos.vue';
import DimensoesEtiqueta from 'src/modules/produtos/etiqueta/personalizada/components/DimensoesEtiqueta.vue';
import DimensoesFolha from 'src/modules/produtos/etiqueta/personalizada/components/DimensoesFolha.vue';
import LabelShortkeys from 'src/modules/produtos/etiqueta/personalizada/components/LabelShortkeys.vue';
import { useFormSchema } from 'src/modules/produtos/etiqueta/personalizada/useFormSchema';
import handleApiErrors from 'src/services/handleApiErrors';
import { arrayBufferToBase64 } from 'src/services/utils';
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import FieldOptions from './components/FieldOptions.vue';
import { useEtiquetaCanvasStore } from './useEtiquetaCanvasStore';

const emit = defineEmits(['ok', 'cancel', 'close']);
const props = defineProps({
  data: {
    type: Object,
    required: false,
    default: null
  }
});

// Variables
const route = useRoute();
const $q = useQuasar();
const isWarningAnimationDisabled = ref(true);
const readonlyOrDisable = ref(route.path.includes('visualizar'));
const registerPageRef = ref();
const templateRef = ref();
const etiquetaStore = useEtiquetaStore();
const RefDimensoesEtiqueta = ref();

const canvasStore = useEtiquetaCanvasStore();
const { getAddedElements, setupCanvas } = canvasStore;

const paperDetailsExpanded = ref(false);
const labelDetailsExpanded = ref(false);
const fieldsExpanded = ref(false);
const scaleSize = ref(1);

const editValues = ref(false);

let controle =
  props?.data?.controle ??
  route?.params?.controle ??
  $q.sessionStorage.getItem('cloneId');

async function onCreated() {
  if (controle) {
    const isDuplicating = $q.sessionStorage.getItem('cloneId') == controle;

    const res = await etiquetaStore.get({ controle });
    const { rowsData: data } = res?.data ?? {};

    if (isDuplicating) {
      $q.sessionStorage.setItem('cloneId', false);
      data.controle = '';
      data.etiquetaConfiguracoes.codigoEtiqueta = '';
      data.etiquetaConfiguracoes.controle = '';
      data.nome = '';
      controle = null;
    }

    canvasStore.setEditElements(JSON.parse(data.objeto) ?? {});
    editValues.value = data;
  } else {
    const fields = JSON.stringify(getAddedElements());
    editValues.value = {
      objeto: fields
    };
  }
}
await onCreated();

const {
  fields,
  initialValues,
  values,

  isSubmitting,
  handleReset,
  validate,
  meta,
  errors
} = useFormSchema(toRaw(editValues.value));

// Computed Methods
const margins = computed(() => ({
  margemCima: values.etiquetaConfiguracoes.margemFolhaCima,
  margemDireita: values.etiquetaConfiguracoes.margemFolhaDireita,
  margemBaixo: values.etiquetaConfiguracoes.margemFolhaBaixo,
  margemEsquerda: values.etiquetaConfiguracoes.margemFolhaEsquerda
}));

const cols = computed(() =>
  Number(fields.value.etiquetaConfiguracoes.colunas.value)
);
const rows = computed(() => {
  if (
    values.etiquetaConfiguracoes.quantidade &&
    values.etiquetaConfiguracoes.colunas
  ) {
    return Math.ceil(
      values.etiquetaConfiguracoes.quantidade /
        values.etiquetaConfiguracoes.colunas
    );
  }
  return 1;
});

async function openShortkeysModal() {
  return new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        componentRef: LabelShortkeys,
        scope: 'label-shortkeys',
        title: 'Atalhos',
        classCardSection: 'lg:tw-w-[600px]',
        hasCancel: false,
        hasSave: false,
        dataModal: {}
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
}

function increaseScale() {
  if (scaleSize.value >= 1.25) return 1.25;
  scaleSize.value += 0.25;
}

function reduceScale() {
  if (scaleSize.value <= 0.75) return 0.75;
  scaleSize.value -= 0.25;
}

async function getPreview() {
  const elements = getAddedElements();
  if (!elements.length) {
    notify('Adicione um campo para pré-visualizar');
    return;
  }

  const objeto = JSON.stringify(toRaw(elements));
  if (!objeto) return;

  const payload = _.cloneDeep(values);
  payload.objeto = objeto;
  payload.htmlDocumento =
    document.querySelector('.label-canvas').parentElement.innerHTML;

  const isLoading = $q.notify({
    position: 'top',
    color: 'gray',
    message: 'Carregando...',
    spinner: true
  });

  try {
    const { status, data } = await api.post('/api/etiqueta/preview', payload, {
      responseType: 'arraybuffer'
    });

    if (status >= 200 && status <= 204) {
      const base64String = await arrayBufferToBase64(data);
      printBase64Pdf(base64String);
    }
  } catch (error) {
    handleApiErrors(error);
  } finally {
    isLoading();
  }
}

async function cancel() {
  isWarningAnimationDisabled.value = true;
  emit('cancel');
}

function reset() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    scaleSize.value = 1;
    const campos = JSON.parse(initialValues.value.objeto);
    canvasStore.handleUnfocusElement();
    canvasStore.setEditElements(campos);
    handleReset();
    mountCanvas();
  });
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  const elements = getAddedElements();
  const hasElements = elements.length > 0;

  if (!validation.valid) {
    registerPageRef.value?.scrollToError(validation);
  }

  if (!hasElements) {
    fieldsExpanded.value = true;
    notify('Adicione pelo menos um campo na etiqueta antes de salvar.');
  }

  isSubmitting.value = false;

  return validation.valid && hasElements;
}
async function save() {
  if (!(await isFormValid())) return;

  const elements = getAddedElements();
  const objeto = JSON.stringify(toRaw(elements));
  const htmlDocumento =
    document.querySelector('.label-canvas').parentElement.innerHTML;
  if (!objeto) return;

  const payload = {
    ...toRaw(values),
    objeto,
    htmlDocumento,
    etiquetaConfiguracoes: {
      ...toRaw(values.etiquetaConfiguracoes),
      margemFolhaCima: values.etiquetaConfiguracoes.margemFolhaCima ?? 0,
      margemFolhaDireita: values.etiquetaConfiguracoes.margemFolhaDireita ?? 0,
      margemFolhaBaixo: values.etiquetaConfiguracoes.margemFolhaBaixo ?? 0,
      margemFolhaEsquerda:
        values.etiquetaConfiguracoes.margemFolhaEsquerda ?? 0,

      colunas: values.etiquetaConfiguracoes.colunas ?? 0,
      quantidade: values.etiquetaConfiguracoes.quantidade ?? 0,
      entreEtiquetas: values.etiquetaConfiguracoes.entreEtiquetas ?? 0
    }
  };

  try {
    let response = controle
      ? await etiquetaStore.put({ controle, payload })
      : await etiquetaStore.post({ payload });

    const { success } = response;
    if (success) cancel();
  } catch (error) {
    handleApiErrors(error);
  }
}
async function updateFields() {
  fields.value.objeto.setValue(JSON.stringify(getAddedElements()));
}

const actionBarEventsReadOnly = {
  goBack: {
    callback: cancel
  }
};

const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: {
    modal: props.modal,
    isWarningAnimationDisabled
  },
  readonly: readonlyOrDisable.value,
  events: readonlyOrDisable.value ? actionBarEventsReadOnly : actionBarEvents
});

// Desfazer alterações.
watch(
  values,
  async () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);

onMounted(() => mountCanvas());

function mountCanvas() {
  const canvasEl = document.querySelector('.label-canvas');
  if (!canvasEl) return;

  mitt.off('update-label', updateFields);
  mitt.on('update-label', updateFields);

  setupCanvas(canvasEl, {
    paperWidth: values.etiquetaConfiguracoes.larguraFolha,
    paperHeight: values.etiquetaConfiguracoes.alturaFolha,
    labelWidth: values.etiquetaConfiguracoes.larguraEtiqueta,
    labelHeight: values.etiquetaConfiguracoes.alturaEtiqueta,
    labelQuantity: values.etiquetaConfiguracoes.quantidade,
    spacing: values.etiquetaConfiguracoes.entreEtiquetas,
    margins: margins.value,
    scale: scaleSize,
    rows: rows.value,
    cols: cols.value
  });
}

onUnmounted(() => {
  etiquetaStore.get();
  mitt.off('update-label', updateFields);
  canvasStore.cleanup();
});
</script>
