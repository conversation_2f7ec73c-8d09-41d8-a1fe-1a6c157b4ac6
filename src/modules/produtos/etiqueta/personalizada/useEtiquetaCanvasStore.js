import codBarras from 'assets/images/cod-barras.png';
import logotipoSGBR from 'assets/images/logotipo-sgbr.png';
import { defineStore } from 'pinia';
import { throttle } from 'quasar';
import { emitter as mitt } from 'src/boot/mitt';
import notify from 'src/components/utils/notify';
import round from 'src/components/utils/round';
import { useGlobal } from 'src/stores/global';
import { computed, ref } from 'vue';
import { AddCommand } from 'src/modules/produtos/etiqueta/personalizada/commands/elements/AddElement';
import { DragCommand } from 'src/modules/produtos/etiqueta/personalizada/commands/elements/DragElement';
import { ResizeCommand } from 'src/modules/produtos/etiqueta/personalizada/commands/elements/ResizeElement';
import { RotateCommand } from 'src/modules/produtos/etiqueta/personalizada/commands/elements/RotateElement';

/**
 * Retorna o texto formatado de acordo com o front-end. Porque em certas etiquetas antigas que foram salvas, o valor de label é diferente.
 * @returns {text} Texto a ser formatado(se possuir no newLabels).
 */
export function formatLabelsToNewTexts(text) {
  const lowerCaseText = text.toLowerCase();

  const newLabels = ['Cód barras(grade)', 'Cor(grade)', 'Tamanho(grade)'];

  const newLabel = newLabels.find((label) => {
    const labelLowerCase = label.toLowerCase();

    const includesThatText = labelLowerCase.includes(lowerCaseText);

    // LOGICAS CUSTOMIZADAS

    const hasCodBarrasOldText = lowerCaseText == 'cód.barras';

    // LOGICAS CUSTOMIZADAS

    const statusValue = includesThatText || hasCodBarrasOldText;

    return statusValue;
  });

  return newLabel ?? text;
}

function getDefaultElements() {
  const global = useGlobal();
  const logoSrc = global.company?.caminhoLogo || logotipoSGBR;

  return [
    {
      label: 'Código',
      content: 'Código',
      id: 'codigo',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 8,
      width: 16,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Cód barras GTIN',
      content: 'Cód barras GTIN',
      id: 'cod_barras_gtin',
      type: 'bar_code',
      imageUrl: codBarras,
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 14,
      width: 32,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Cód barras interno',
      content: 'Cód barras interno',
      id: 'cod_barras',
      type: 'bar_code',
      imageUrl: codBarras,
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 14,
      width: 32,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Descrição',
      content: 'Descrição',
      id: 'descricao',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Referência',
      content: 'Referência',
      id: 'referencia',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Logo',
      content: 'Logo',
      id: 'logo',
      type: 'image',
      imageUrl: logoSrc,
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 14,
      width: 32,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Preço de venda',
      content: 'Preço de venda',
      id: 'preco_de_venda',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Preço de custo',
      content: 'Preço de custo',
      id: 'preco_de_custo',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Emitente',
      content: 'Emitente',
      id: 'emitente',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Unidade',
      content: 'Unidade',
      id: 'unidade',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Aplicação do produto',
      content: 'Aplicação do produto',
      id: 'aplicacao_de_produto',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Quantidade',
      content: 'Quantidade',
      id: 'quantidade',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Cód barras(grade)',
      content: 'Cód barras',
      id: 'cod_barras_grade',
      type: 'bar_code',
      imageUrl: codBarras,
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 14,
      width: 32,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Cor(grade)',
      content: 'Cor',
      id: 'grade_cor',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    },
    {
      label: 'Tamanho(grade)',
      content: 'Tamanho',
      id: 'grade_tamanho',
      type: 'text',
      class:
        'tw-px-2 tw-text-SGBRBlueUltraLighten tw-border-solid tw-border tw-border-tw-bg-SGBRBlueLighten',
      color: 'primary',
      height: 9,
      width: 20,
      fontSize: 12,
      x: 0,
      y: 0,
      rotate: 0,
      added: false,
      isEditing: false
    }
  ];
}

export const useEtiquetaCanvasStore = defineStore('label-canvas', () => {
  // Min max of each field
  const dpi = window.devicePixelRatio * 96;
  const MIN_WIDTH = ref(mmToPx(15));
  const MIN_HEIGHT = ref(mmToPx(6));

  // History
  const imageCache = ref(new Map());
  const loadingImages = ref(new Set());
  const elements = ref(getDefaultElements());

  const history = ref([]);
  const redoStack = ref([]);

  // Drawing
  const selectionStart = ref(null);
  const selectionEnd = ref(null);
  const isSelecting = ref(false);

  // Paper and margins
  const scale = ref(1);
  const paper = ref({
    width: 210,
    height: 297
  });
  const margins = ref({
    top: 5,
    left: 5,
    right: 5,
    bottom: 5
  });

  // Label:
  const labelWidth = ref(mmToPx(50));
  const labelHeight = ref(mmToPx(50));
  const spacing = ref(mmToPx(5));
  const rows = ref(1);
  const cols = ref(1);

  // Element
  const handleSize = ref(6);
  const rotationHandle = ref({});
  const selectedElement = ref();
  const isEditingElement = ref();
  const isRotating = ref(false);
  const interactionState = ref({
    isDragging: false,
    isRotating: false,
    isResizing: false,
    startPos: { x: 0, y: 0 },
    currentPos: { x: 0, y: 0 },
    startAngle: 0,
    currentAngle: 0,
    startDimensions: { width: 0, height: 0, x: 0, y: 0 },
    currentDimensions: { width: 0, height: 0 },
    resizeHandle: null
  });

  // Computed
  const labelBounds = computed(() => {
    return {
      x: 0,
      y: 0,
      width: labelWidth.value,
      height: labelHeight.value
    };
  });

  // Canvas
  const canvas = ref();
  const ctx = ref();

  function getElement(id) {
    const newElement = elements.value.find((el) => el.id == id);

    return newElement;
  }

  function getElements() {
    const rawElements = elements.value;

    return rawElements;
  }

  function getAddedElements() {
    return elements.value.filter((el) => el.added);
  }

  function isElementAdded(id) {
    return Boolean(elements.value.find((el) => el.id == id && el.added));
  }

  function hasElement(id) {
    return elements.value.findIndex((el) => el.id === id) !== -1;
  }

  function elementIndex(id) {
    return elements.value.findIndex((el) => el.id === id);
  }

  function resetElements() {
    elements.value = getDefaultElements();
  }

  function setElement(index, el) {
    elements.value[index] = el;
  }

  function executeCommand(command) {
    command.execute();
    history.value.push(command);
    redoStack.value = [];

    if (history.value.length === 20) history.value.shift();
    mitt.emit('update-label');
  }

  function undo() {
    const command = history.value.pop();
    if (command) {
      command.undo();
      redoStack.value.push(command);
      if (redoStack.value.length === 20) redoStack.value.shift();
    }
  }

  function addElementCommand(id, newAdded, oldAdded) {
    const command = new AddCommand(id, newAdded, oldAdded);
    executeCommand(command);
  }

  function dragElement(id, newPos, oldPos) {
    const dragCommand = new DragCommand(id, newPos, oldPos);
    executeCommand(dragCommand);
  }

  function rotateElement(id, newAngle, prevAngle) {
    const rotateCommand = new RotateCommand(id, newAngle, prevAngle);
    executeCommand(rotateCommand);
  }

  function resizeElement(id, newSize, oldSize) {
    const resizeCommand = new ResizeCommand(id, newSize, oldSize);
    executeCommand(resizeCommand);
  }

  function redo() {
    const command = redoStack.value.pop();
    if (command) {
      command.execute();
      history.value.push(command);
    }
  }

  function addElement({ id }) {
    if (isElementAdded(id)) {
      notify('Campo já adicionado');
    } else {
      const index = elementIndex(id);

      const updates = {
        x: 0,
        y: 0,
        xPx: 0,
        yPx: 0,
        widthPx: mmToPx(elements.value[index].width),
        heightPx: mmToPx(elements.value[index].height)
      };
      Object.assign(elements.value[index], updates);

      interactionState.value.startPos = {
        x: elements.value[index]?.xPx || 0,
        y: elements.value[index]?.yPx || 0
      };

      interactionState.value.startDimensions = {
        width: elements.value[index].widthPx,
        height: elements.value[index].heightPx,

        x: elements.value[index].xPx,
        y: elements.value[index].yPx
      };

      // Foca no elemento
      selectedElement.value = elements.value[index];

      addElementCommand(elements.value[index].id, true, false);
      drawElement(elements.value[index]);
    }
  }

  function removeElement(element) {
    if (!element) return;

    // Find and remove element from array
    const index = elementIndex(element.id);
    if (index !== -1) {
      const defaultElements = getDefaultElements();
      const el = defaultElements.find((el) => el.id == element.id);
      elements.value[index] = el;

      // Reset position
      elements.value[index].x = 0;
      elements.value[index].y = 0;
      elements.value[index].xPx = 0;
      elements.value[index].yPx = 0;

      addElementCommand(elements.value[index].id, false, true);
    }

    handleUnfocusElement();
    render();
  }

  function setEditElements(editElements) {
    resetElements();

    editElements.forEach((element) => {
      const index = elementIndex(element.id);
      if (index === -1) return;

      const updates = {
        xPx: mmToPx(element.x),
        yPx: mmToPx(element.y),
        widthPx: mmToPx(element.width),
        heightPx: mmToPx(element.height)
      };

      Object.assign(elements.value[index], element, updates);
    });
  }

  function setPaper(width, height) {
    paper.value.width = width;
    paper.value.height = height;

    const availableWidth = width - margins.value.left - margins.value.right;
    const availableHeight = height - margins.value.top - margins.value.bottom;

    const canvasWidthPx = round(mmToPx(availableWidth), 0);
    const canvasHeightPx = round(mmToPx(availableHeight), 0);

    canvas.value.width = canvasWidthPx;
    canvas.value.height = canvasHeightPx;

    canvas.value.style.width = `${canvasWidthPx}px`;
    canvas.value.style.height = `${canvasHeightPx}px`;
    render();
  }

  function isInTemplateLabel(element) {
    const bounds = labelBounds.value;
    const corners = getRotatedCorners(element);
    return corners.every(
      (corner) =>
        corner.x >= bounds.x &&
        corner.x <= bounds.x + bounds.width &&
        corner.y >= bounds.y &&
        corner.y <= bounds.y + bounds.height
    );
  }

  function setMargins({ top, left, right, bottom }) {
    margins.value = {
      top,
      left,
      right,
      bottom
    };
    render();
  }

  function setSpacing(space) {
    spacing.value = round(mmToPx(space), 0) || 0;
  }

  function setScale(newScale) {
    scale.value = newScale;
  }

  function setCols(columns) {
    cols.value = columns;
  }

  function setRows(rowsNumber) {
    rows.value = rowsNumber;
  }

  function setLabelWidth(width) {
    labelWidth.value = round(mmToPx(width), 0);
  }

  function setLabelHeight(height) {
    labelHeight.value = round(mmToPx(height), 0);
  }

  function setRotation(id, rotate) {
    const i = elementIndex(id);
    elements.value[i].rotate = rotate;
    render();
  }

  function mmToPx(mm) {
    return (Number(mm) * dpi) / 25.4;
  }

  function pxToMm(pixels) {
    return (Number(pixels) * 25.4) / dpi;
  }

  function setupCanvas(canvasEl, options) {
    canvas.value = canvasEl;
    ctx.value = canvas.value.getContext('2d');
    ctx.value.imageSmoothingEnabled = false;

    paper.value.width = options.paperWidth;
    paper.value.height = options.paperHeight;

    margins.value.top = options.margins.margemCima;
    margins.value.left = options.margins.margemEsquerda;
    margins.value.right = options.margins.margemDireita;
    margins.value.bottom = options.margins.margemBaixo;
    scale.value = options.scale;

    spacing.value = mmToPx(options.spacing) || 5;
    rows.value = options.rows || 1;
    cols.value = options.cols || 1;

    labelWidth.value = mmToPx(options.labelWidth);
    labelHeight.value = mmToPx(options.labelHeight);

    interactionState.value = {
      isDragging: false,
      isRotating: false,
      isResizing: false,
      startPos: { x: 0, y: 0 },
      startAngle: 0,
      startDimensions: { width: 0, height: 0, x: 0, y: 0 },
      resizeHandle: null
    };

    const widthPx = round(
      mmToPx(
        paper.value.width - margins.value.left - margins.value.right - 0.1
      ),
      0
    );
    const heightPx = round(
      mmToPx(
        paper.value.height - margins.value.top - margins.value.bottom - 0.1
      ),
      0
    );

    canvas.value.width = widthPx;
    canvas.value.height = heightPx;
    canvas.value.scale = scale.value;

    // Set display size
    canvas.value.style.width = `${widthPx}px`;
    canvas.value.style.height = `${heightPx}px`;

    bindEvents();
    render();
  }

  function drawCanvasContainer() {
    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
    ctx.value.strokeStyle = '#718096';
    ctx.value.lineWidth = 1;
  }

  function render() {
    drawCanvasContainer();
    drawLabels();
    drawAddedElements();
  }

  async function drawAddedElements() {
    const addedElementsArray = getAddedElements();

    for (let row = 0; row < rows.value; row++) {
      for (let col = 0; col < cols.value; col++) {
        // Calculate offset for current label
        const offsetX = col * (labelWidth.value + spacing.value);
        const offsetY = row * (labelHeight.value + spacing.value);

        addedElementsArray?.forEach((element) => {
          if (row === 0 && col === 0) {
            // Draw original element in first label
            drawElement(element);
          } else {
            // Draw duplicated element in other labels
            ctx.value.save();

            // Create temporary element with adjusted position
            const duplicatedElement = {
              ...element,
              xPx: element.xPx + offsetX,
              yPx: element.yPx + offsetY
            };

            // Make duplicates slightly transparent
            ctx.value.globalAlpha = 0.7;
            drawElement(duplicatedElement);

            ctx.value.restore();
          }
        });
      }
    }
  }

  function drawLabels() {
    // Draw labels grid
    for (let row = 0; row < rows.value; row++) {
      for (let col = 0; col < cols.value; col++) {
        const x = col * (labelWidth.value + spacing.value);
        const y = row * (labelHeight.value + spacing.value);

        // Draw single label
        ctx.value.fillStyle = '#FFFFFF';
        ctx.value.fillRect(x, y, labelWidth.value, labelHeight.value);

        ctx.value.strokeStyle = '#718096';
        ctx.value.setLineDash([5, 5]);
        ctx.value.strokeRect(x, y, labelWidth.value, labelHeight.value);
        ctx.value.setLineDash([]);
      }
    }
  }

  function drawElement(element) {
    if (element.type == 'image' || element.type == 'bar_code') {
      drawImage(element);
      return;
    }
    ctx.value.save();

    // Center of rotation
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;

    // Apply rotation transformation only to the element content
    ctx.value.translate(centerX, centerY);
    ctx.value.rotate((element.rotate * Math.PI) / 180);
    ctx.value.translate(-centerX, -centerY);

    // Draw element border - this will also be rotated
    ctx.value.strokeStyle =
      selectedElement.value === element ? '#0066ff' : '#718096';
    ctx.value.strokeRect(
      element.xPx,
      element.yPx,
      element.widthPx,
      element.heightPx
    );

    // Draw element content
    if (element.content) {
      ctx.value.save();
      // Save the current context state
      // Create a clipping region
      ctx.value.beginPath();
      ctx.value.rect(
        element.xPx,
        element.yPx,
        element.widthPx,
        element.heightPx
      );
      ctx.value.clip();

      ctx.value.fillStyle = '#000000';
      ctx.value.font = `${element.fontWeight || 'normal'} ${
        element.fontStyle || 'normal'
      } ${element.fontSize}px Arial`;
      ctx.value.textAlign = 'left';
      ctx.value.textBaseline = 'top';

      const textX = element.xPx + 5;
      const textY = element.yPx + 5;
      ctx.value.fillText(element.content, textX, textY);

      if (element.textDecoration === 'underline') {
        const textWidth = ctx.value.measureText(element.content).width;
        const underlineY = textY + element.fontSize;
        ctx.value.beginPath();
        ctx.value.moveTo(textX, underlineY);
        ctx.value.lineTo(textX + textWidth, underlineY);
        ctx.value.strokeStyle = '#000000';
        ctx.value.lineWidth = 1;
        ctx.value.stroke();
      }

      ctx.value.restore();
    }

    // Restore the context to remove rotation before drawing handles
    ctx.value.restore();

    // Draw handles in their original orientation
    if (element === selectedElement.value) {
      drawSelectionHandles(element);
    }
  }

  // Modify drawImage function as well
  function drawImage(element) {
    const imageUrl = element.imageUrl;
    const cachedImage = imageCache.value.get(imageUrl);
    if (cachedImage) {
      ctx.value.save();
      const centerX = element.xPx + element.widthPx / 2;
      const centerY = element.yPx + element.heightPx / 2;
      ctx.value.translate(centerX, centerY);
      ctx.value.rotate((element.rotate * Math.PI) / 180);
      ctx.value.translate(-centerX, -centerY);
      ctx.value.drawImage(
        cachedImage,
        element.xPx,
        element.yPx,
        element.widthPx,
        element.heightPx
      );
      if (element === selectedElement.value) {
        ctx.value.strokeStyle = '#0066ff';
        ctx.value.strokeRect(
          element.xPx,
          element.yPx,
          element.widthPx,
          element.heightPx
        );
      }
      ctx.value.restore();
      if (element === selectedElement.value) {
        drawSelectionHandles(element);
      }
      return;
    }

    if (loadingImages.value.has(imageUrl)) {
      return;
    }

    loadingImages.value.add(imageUrl);

    cacheImage(element)
      .then(() => {
        render();
      })
      .catch((error) => {
        console.error(
          `Falha ao renderizar imagem após cache: ${imageUrl}`,
          error
        );
      })
      .finally(() => {
        loadingImages.value.delete(imageUrl);
      });
  }
  function cacheImage(element) {
    if (!element.imageUrl || imageCache.value.has(element.imageUrl)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        imageCache.value.set(element.imageUrl, img);
        resolve();
      };
      img.onerror = (error) => {
        console.error(`Failed to load image: ${element.imageUrl}`, error);
        reject(error);
      };
      img.src = element.imageUrl;
    });
  }

  function drawSelectionHandles(element) {
    const handles = getHandlePositions(element);

    // Draw resize handles
    ctx.value.fillStyle = '#0066ff';
    handles.forEach((handle) => {
      // Save current transform state
      ctx.value.save();

      // Draw the handle square
      ctx.value.translate(handle.x, handle.y);
      if (handle.type === 'resize') {
        // For resize handles, keep them as squares regardless of rotation
        ctx.value.fillRect(
          -handleSize.value / 2,
          -handleSize.value / 2,
          handleSize.value,
          handleSize.value
        );
      } else if (handle.type === 'rotate') {
        // For rotation handle, draw a circle instead of a square
        ctx.value.beginPath();
        ctx.value.arc(0, 0, handleSize.value / 2, 0, Math.PI * 2);
        ctx.value.fill();
      }

      ctx.value.restore();
    });
  }

  function updateScale() {
    ctx.value.setTransform(scale.value, 0, 0, scale.value, 0, 0);
    render();
  }

  function bindEvents() {
    canvas.value.addEventListener('mousedown', handleMouseDown);
    canvas.value.addEventListener('mousemove', handleMouseMove);
    canvas.value.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('keydown', handleKeyUp);
  }

  function unbindEvents() {
    canvas.value.removeEventListener('mousedown', handleMouseDown);
    canvas.value.removeEventListener('mousemove', handleMouseMove);
    canvas.value.removeEventListener('mouseup', handleMouseUp);
    window.removeEventListener('keydown', handleKeyUp);
  }

  function handleKeyUp(ev) {
    const activeEl = document.activeElement;
    if (activeEl.tagName === 'INPUT' || activeEl.tagName === 'TEXTAREA') return;

    if (ev.key === 'Delete' && selectedElement.value) {
      removeElement(selectedElement.value);
    }

    if (ev.key.includes('Arrow')) {
      ev.preventDefault();
      handleKeyboardMove(ev);
    }

    if (ev.key === 'Escape') {
      ev.preventDefault();
      handleUnfocusElement(ev);
    }

    if (ev.ctrlKey && ev.key === 'z') {
      selectedElement.value = null;
      undo();
      render();
    }
    if (ev.ctrlKey && ev.key === 'y') {
      selectedElement.value = null;
      redo();
      render();
    }
  }

  function handleUnfocusElement() {
    handleMouseUp();
    isEditingElement.value = null;
    selectedElement.value = null;
  }

  function handleMouseDown(e) {
    const pos = getCanvasPosition(e);

    if (selectedElement.value) {
      const handle = getHandleAtPosition(pos);
      if (handle) {
        if (handle.type === 'rotate') {
          startRotation(pos);
        } else if (handle.type === 'resize') {
          startResize(pos, handle);
        }
        return;
      }
    }

    const element = findElementAt(pos);
    if (element) {
      selectedElement.value = element;
      startDragging(pos);
    } else {
      selectedElement.value = null;
      isSelecting.value = true;
      selectionStart.value = pos;
      selectionEnd.value = null;
    }

    render();
  }

  function handleMouseUp() {
    stopSelection();
    stopDragging();
    stopRotation();
    stopResize();
    updateHandlers();
    render();
  }

  function handleKeyboardMove(event) {
    const element = selectedElement.value;
    if (!element) return;

    const step = event.shiftKey ? 20 : 2;

    // Calculate boundaries
    const minX = 0;
    const minY = 0;
    const maxX = canvas.value.width - element.widthPx;
    const maxY = canvas.value.height - element.heightPx;

    const originalState = {
      x: element.x,
      y: element.y,
      xPx: element.xPx,
      yPx: element.yPx
    };

    switch (event.key) {
      case 'ArrowUp':
        element.yPx = Math.max(minY, element.yPx - step);
        break;
      case 'ArrowDown':
        element.yPx = Math.min(maxY, element.yPx + step);
        break;
      case 'ArrowLeft':
        element.xPx = Math.max(minX, element.xPx - step);
        break;
      case 'ArrowRight':
        element.xPx = Math.min(maxX, element.xPx + step);
        break;
    }

    element.x = pxToMm(element.xPx);
    element.y = pxToMm(element.yPx);

    if (!isInTemplateLabel(element)) {
      Object.assign(element, {
        xPx: originalState.xPx,
        yPx: originalState.yPx,
        x: originalState.x,
        y: originalState.y
      });
    }

    render();
  }

  const throttledUpdateResize = throttle((pos) => updateResize(pos), 10);

  function updateResizeThrottle(pos) {
    throttledUpdateResize(pos);
  }

  function handleMouseMove(e) {
    const pos = getCanvasPosition(e);

    if (interactionState.value.isRotating) {
      updateRotation(pos);
      render();
      return;
    }

    if (interactionState.value.isResizing) {
      updateResizeThrottle(pos);
      render();
      return;
    }

    if (interactionState.value.isDragging) {
      updateDrag(pos);
      render();
      return;
    }

    const handle = getHandleAtPosition(pos);
    if (handle) {
      canvas.value.style.cursor = handle.cursor;
    } else {
      canvas.value.style.cursor =
        findElementAt(pos) && selectedElement.value ? 'move' : 'default';
    }
  }

  // function drawSelectionArea() {
  //   if (!selectionStart.value || !selectionEnd.value) return;

  //   // Log calculated dimensions
  //   const x = Math.round(
  //     Math.min(selectionStart.value.x, selectionEnd.value.x)
  //   );
  //   const y = Math.round(
  //     Math.min(selectionStart.value.y, selectionEnd.value.y)
  //   );
  //   const width = Math.round(
  //     Math.abs(selectionEnd.value.x - selectionStart.value.x)
  //   );
  //   const height = Math.round(
  //     Math.abs(selectionEnd.value.y - selectionStart.value.y)
  //   );

  //   ctx.value.save();
  //   ctx.value.fillStyle = 'rgba(65, 105, 225, 0.1)';
  //   ctx.value.fillRect(x, y, width, height);

  //   ctx.value.strokeStyle = 'rgb(65, 105, 225)';
  //   ctx.value.lineWidth = 1;
  //   ctx.value.strokeRect(x, y, width, height);

  //   ctx.value.restore();
  // }

  function stopSelection() {
    if (isSelecting.value) {
      isSelecting.value = false;
      selectionStart.value = null;
      selectionEnd.value = null;
    }
  }

  function stopDragging() {
    interactionState.value.isDragging = false;
    if (!selectedElement.value) return;

    const { x: oldx, y: oldy } = interactionState.value.startPos;
    let { xPx: newx, yPx: newy } = selectedElement.value;

    const oldPos = {
      x: pxToMm(oldx),
      y: pxToMm(oldy),
      xPx: oldx,
      yPx: oldy
    };

    const newPos = {
      x: pxToMm(newx),
      y: pxToMm(newy),
      xPx: newx,
      yPx: newy
    };

    if (newPos.x === oldPos.x && newPos.y === oldPos.y) return;
    dragElement(selectedElement.value.id, newPos, oldPos);
  }

  function stopRotation() {
    isRotating.value = false;
    interactionState.value.isRotating = false;
    if (!selectedElement.value) return;

    const oldRotate = interactionState.value.startAngle;
    if (selectedElement.value.rotate == oldRotate) return;

    rotateElement(
      selectedElement.value.id,
      selectedElement.value.rotate,
      oldRotate
    );
  }

  function stopResize() {
    interactionState.value.isResizing = false;
    if (!selectedElement.value) return;

    const oldSize = {
      width: pxToMm(interactionState.value.startDimensions.width),
      widthPx: interactionState.value.startDimensions.width,
      height: pxToMm(interactionState.value.startDimensions.height),
      heightPx: interactionState.value.startDimensions.height,

      xPx: interactionState.value.startDimensions.x,
      yPx: interactionState.value.startDimensions.y,
      x: pxToMm(interactionState.value.startDimensions.x),
      y: pxToMm(interactionState.value.startDimensions.y)
    };

    const newSize = {
      width: selectedElement.value.width,
      widthPx: selectedElement.value.widthPx,
      height: selectedElement.value.height,
      heightPx: selectedElement.value.heightPx,

      xPx: selectedElement.value.xPx,
      yPx: selectedElement.value.yPx,
      x: selectedElement.value.x,
      y: selectedElement.value.y
    };

    if (
      newSize.widthPx == oldSize.widthPx &&
      newSize.heightPx == oldSize.heightPx
    )
      return;
    resizeElement(selectedElement.value.id, newSize, oldSize);
  }

  function updateHandlers() {
    const element = selectedElement.value;
    if (!element) return;

    const centerX = element.x + element.width / 2;
    const centerY = element.y + element.height / 2;

    // Update rotation handler position
    const angle = (element.rotate * Math.PI) / 180;
    const rotationDistance = 30; // Distance from center of element
    rotationHandle.value = {
      x: centerX + Math.sin(angle) * rotationDistance,
      y: centerY - Math.cos(angle) * rotationDistance
    };
  }

  function startDragging(pos) {
    interactionState.value.isDragging = true;
    interactionState.value.startPos = {
      x: selectedElement.value.xPx,
      y: selectedElement.value.yPx
    };
    interactionState.value.currentPos = pos;
  }

  function startResize(pos, handle) {
    interactionState.value.isResizing = true;
    interactionState.value.resizeHandle = handle;
    interactionState.value.currentPos = pos;
    interactionState.value.startDimensions = {
      width: selectedElement.value.widthPx,
      height: selectedElement.value.heightPx,
      x: selectedElement.value.xPx,
      y: selectedElement.value.yPx
    };
    interactionState.value.currentDimensions = {
      width: selectedElement.value.widthPx,
      height: selectedElement.value.heightPx,
      x: selectedElement.value.xPx,
      y: selectedElement.value.yPx
    };
  }

  function updateResize(pos) {
    if (!interactionState.value.isResizing) return;

    const element = selectedElement.value;
    const start = interactionState.value.currentDimensions;

    // Calculate center of rotation
    const centerX = start.x + start.width / 2;
    const centerY = start.y + start.height / 2;

    let cachedAngle = interactionState.value.cachedAngle;
    if (!cachedAngle || cachedAngle._lastAngle !== element.rotate) {
      const angle = (-element.rotate * Math.PI) / 180;
      cachedAngle = {
        cos: Math.cos(angle),
        sin: Math.sin(angle),
        angle,
        _lastAngle: element.rotate
      };
      interactionState.value.cachedAngle = cachedAngle;
    }

    // Transform mouse position to element's coordinate system
    const getTransformedPoint = (point) => {
      const dx = point.x - centerX;
      const dy = point.y - centerY;
      return {
        x: centerX + dx * cachedAngle.cos - dy * cachedAngle.sin,
        y: centerY + dx * cachedAngle.sin + dy * cachedAngle.cos
      };
    };

    const transformedPos = getTransformedPoint(pos);
    const transformedStart = getTransformedPoint(
      interactionState.value.currentPos
    );

    const dx = transformedPos.x - transformedStart.x;
    const dy = transformedPos.y - transformedStart.y;

    // Store original state
    const originalState = {
      x: element.xPx,
      y: element.yPx,
      width: element.widthPx,
      height: element.heightPx
    };

    // Calculate new dimensions and position based on resize handle
    // Cache common calculations and values
    const cursor = interactionState.value.resizeHandle.cursor;
    const MIN_W = MIN_WIDTH.value;
    const MIN_H = MIN_HEIGHT.value;

    // Pre-calculate trig values once
    const cos = cachedAngle.cos;
    const sin = cachedAngle.sin;

    // Store initial values
    const startX = start.x;
    const startY = start.y;

    if (cursor === 'se-resize') {
      // Simplest case - just resize width/height
      const newWidth = Math.max(start.width + dx, MIN_W);
      const newHeight = Math.max(start.height + dy, MIN_H);

      // Batch property updates
      const updates = {
        widthPx: newWidth,
        heightPx: newHeight,
        width: pxToMm(newWidth),
        height: pxToMm(newHeight)
      };
      Object.assign(element, updates);
    } else if (cursor === 'sw-resize') {
      const newWidth = Math.max(start.width - dx, MIN_W);
      const newHeight = Math.max(start.height + dy, MIN_H);
      const deltaWidth = start.width - newWidth;

      // Pre-calculate rotated deltas once
      const rotX = deltaWidth * cos;
      const rotY = deltaWidth * sin;

      const updates = {
        xPx: startX + rotX,
        yPx: startY + rotY,
        widthPx: newWidth,
        heightPx: newHeight,
        x: pxToMm(startX + rotX),
        y: pxToMm(startY + rotY),
        width: pxToMm(newWidth),
        height: pxToMm(newHeight)
      };
      Object.assign(element, updates);
    } else if (cursor === 'ne-resize') {
      const newWidth = Math.max(start.width + dx, MIN_W);
      const newHeight = Math.max(start.height - dy, MIN_H);
      const deltaHeight = start.height - newHeight;

      const rotX = -deltaHeight * sin;
      const rotY = deltaHeight * cos;

      const updates = {
        xPx: startX + rotX,
        yPx: startY + rotY,
        widthPx: newWidth,
        heightPx: newHeight,
        x: pxToMm(startX + rotX),
        y: pxToMm(startY + rotY),
        width: pxToMm(newWidth),
        height: pxToMm(newHeight)
      };
      Object.assign(element, updates);
    } else if (cursor === 'nw-resize') {
      const newWidth = Math.max(start.width - dx, MIN_W);
      const newHeight = Math.max(start.height - dy, MIN_H);
      const deltaWidth = start.width - newWidth;
      const deltaHeight = start.height - newHeight;

      const rotX = deltaWidth * cos + -deltaHeight * sin;
      const rotY = deltaWidth * sin + deltaHeight * cos;

      const updates = {
        xPx: startX + rotX,
        yPx: startY + rotY,
        widthPx: newWidth,
        heightPx: newHeight,
        x: pxToMm(startX + rotX),
        y: pxToMm(startY + rotY),
        width: pxToMm(newWidth),
        height: pxToMm(newHeight)
      };
      Object.assign(element, updates);
    }

    // If out of bounds, revert all changes
    if (!isInTemplateLabel(element)) {
      Object.assign(element, {
        xPx: originalState.x,
        yPx: originalState.y,
        widthPx: originalState.width,
        heightPx: originalState.height,
        x: pxToMm(originalState.x),
        y: pxToMm(originalState.y),
        width: pxToMm(originalState.width),
        height: pxToMm(originalState.height)
      });
    }
  }

  function startRotation(pos) {
    const element = selectedElement.value;
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;

    interactionState.value.isRotating = true;
    interactionState.value.currentAngle =
      Math.atan2(pos.y - centerY, pos.x - centerX) -
      (element.rotate * Math.PI) / 180;
  }

  function updateRotation(pos) {
    if (!interactionState.value.isRotating) return;

    const element = selectedElement.value;
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;

    const angle = Math.atan2(pos.y - centerY, pos.x - centerX);
    let newRotation =
      ((angle - interactionState.value.currentAngle) * 180) / Math.PI;

    // Snap to common angles (0, 90, 180, 270)
    const snapThreshold = 5;
    [0, 90, 180, 270].forEach((snapAngle) => {
      if (Math.abs(newRotation - snapAngle) < snapThreshold) {
        newRotation = snapAngle;
      }
    });

    // Store original rotation for boundary checking
    const originalRotation = element.rotate;
    element.rotate = newRotation;

    if (!isInTemplateLabel(element)) {
      element.rotate = originalRotation;
    }

    drawSelectionHandles(element);
  }

  function updateDrag(pos) {
    if (!interactionState.value.isDragging) return;

    const dx = pos.x - interactionState.value.currentPos.x;
    const dy = pos.y - interactionState.value.currentPos.y;

    const element = selectedElement.value;

    // Store original position
    const originalX = element.xPx;
    const originalY = element.yPx;

    element.xPx += dx;
    element.yPx += dy;
    element.x += pxToMm(dx);
    element.y += pxToMm(dy);

    if (!isInTemplateLabel(element)) {
      element.xPx = originalX;
      element.yPx = originalY;
      element.x = pxToMm(originalX);
      element.y = pxToMm(originalY);
    } else {
      interactionState.value.currentPos = pos;
    }
  }

  function getCanvasPosition(e) {
    const rect = canvas.value.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left) * (canvas.value.width / rect.width),
      y: (e.clientY - rect.top) * (canvas.value.height / rect.height)
    };
  }

  function findElementAt(pos) {
    // Iterate in reverse to check top-most elements first
    for (let i = elements.value.length - 1; i >= 0; i--) {
      const element = elements.value[i];
      if (isPointInElement(pos, element)) {
        return element;
      }
    }
    return null;
  }

  function isPointInElement(pos, element) {
    // Transform point based on element rotation
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;
    const angle = (-element.rotate * Math.PI) / 180;

    const rotatedPoint = {
      x:
        centerX +
        (pos.x - centerX) * Math.cos(angle) -
        (pos.y - centerY) * Math.sin(angle),
      y:
        centerY +
        (pos.x - centerX) * Math.sin(angle) +
        (pos.y - centerY) * Math.cos(angle)
    };

    return (
      rotatedPoint.x >= element.xPx &&
      rotatedPoint.x <= element.xPx + element.widthPx &&
      rotatedPoint.y >= element.yPx &&
      rotatedPoint.y <= element.yPx + element.heightPx
    );
  }

  function getHandleAtPosition(pos) {
    if (!selectedElement.value) return null;

    const handles = getHandlePositions(selectedElement.value);
    const hitArea = handleSize.value / 2;

    return handles.find((handle) => {
      return (
        pos.x >= handle.x - hitArea &&
        pos.x <= handle.x + hitArea &&
        pos.y >= handle.y - hitArea &&
        pos.y <= handle.y + hitArea
      );
    });
  }

  function getRotatedCorners(element) {
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;
    const angle = (element.rotate * Math.PI) / 180;

    const corners = [
      { x: -element.widthPx / 2, y: -element.heightPx / 2 }, // Top-left
      { x: element.widthPx / 2, y: -element.heightPx / 2 }, // Top-right
      { x: element.widthPx / 2, y: element.heightPx / 2 }, // Bottom-right
      { x: -element.widthPx / 2, y: element.heightPx / 2 } // Bottom-left
    ];

    return corners.map((corner) => {
      const rotatedX = corner.x * Math.cos(angle) - corner.y * Math.sin(angle);
      const rotatedY = corner.x * Math.sin(angle) + corner.y * Math.cos(angle);
      return {
        x: centerX + rotatedX,
        y: centerY + rotatedY
      };
    });
  }

  function getHandlePositions(element) {
    const centerX = element.xPx + element.widthPx / 2;
    const centerY = element.yPx + element.heightPx / 2;
    const angle = (element.rotate * Math.PI) / 180;

    // Calculate rotated positions for each handle
    const rotatePoint = (x, y) => {
      const dx = x - centerX;
      const dy = y - centerY;
      return {
        x: centerX + dx * Math.cos(angle) - dy * Math.sin(angle),
        y: centerY + dx * Math.sin(angle) + dy * Math.cos(angle)
      };
    };

    // Base positions for resize handles at corners
    const corners = [
      { x: element.xPx, y: element.yPx, cursor: 'nw-resize' },
      { x: element.xPx + element.widthPx, y: element.yPx, cursor: 'ne-resize' },
      {
        x: element.xPx + element.widthPx,
        y: element.yPx + element.heightPx,
        cursor: 'se-resize'
      },
      { x: element.xPx, y: element.yPx + element.heightPx, cursor: 'sw-resize' }
    ];

    // Generate resize handles
    const resizeHandles = corners.map((corner) => {
      const rotated = rotatePoint(corner.x, corner.y);
      return {
        x: rotated.x,
        y: rotated.y,
        cursor: corner.cursor,
        type: 'resize'
      };
    });

    // Calculate rotation handle position (20 pixels above the top edge center)
    const rotationHandleBase = {
      x: element.xPx + element.widthPx / 2,
      y: element.yPx - 20
    };
    const rotatedRotationHandle = rotatePoint(
      rotationHandleBase.x,
      rotationHandleBase.y
    );

    // Combine all handles
    return [
      ...resizeHandles,
      {
        x: rotatedRotationHandle.x,
        y: rotatedRotationHandle.y,
        cursor: 'grab',
        type: 'rotate'
      }
    ];
  }

  function cleanup() {
    ctx.value.clearRect(0, 0, canvas.value.width, canvas.value.height);
    selectedElement.value = null;
    isEditingElement.value = null;
    resetElements();
    imageCache.value.clear();
    unbindEvents();
  }

  return {
    getElement,
    getElements,
    getAddedElements,
    isElementAdded,
    hasElement,
    elementIndex,
    resetElements,
    setElement,
    setEditElements,
    getDefaultElements,
    paper,
    margins,
    elements,
    selectedElement,
    isEditingElement,
    canvas,
    ctx,
    history,
    addElement,
    removeElement,
    setPaper,
    setSpacing,
    setScale,
    setCols,
    setRows,
    setLabelWidth,
    setLabelHeight,
    setRotation,
    setMargins,
    setupCanvas,
    render,
    drawElement,
    drawLabels,
    undo,
    redo,
    rotateElement,
    cleanup,
    mmToPx,
    handleUnfocusElement,
    updateScale
  };
});
