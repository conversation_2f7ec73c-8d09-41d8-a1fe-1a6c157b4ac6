<template>
  <!-- SKELETON PÁGINA -->
  <q-skeleton
    v-if="loading"
    animated
    height="380px"
    class="tw-col-span-6 tw-h-fit"
  />
  <!-- FIM SKELETON PÁGINA -->

  <!-- CONTEÚDO PRINCIPAL -->
  <NewSGCard
    title="Desempenho de vendas"
    :has-title-separator="false"
    id="total-vendas-component-dashboard"
    class="tw-size-full tw-overflow-hidden tw-py-2"
    :has-circle="false"
    v-else
  >
    <!-- Gráfico -->
    <AreaChartComponent
      v-if="canShowAreaChart"
      :loading="loading"
      :chart-series="chartSeries"
    />
    <div
      v-else
      class="tw-flex tw-h-[300px] tw-flex-col tw-items-center tw-justify-center"
    >
      <q-img src="/no-table-data.svg" style="max-width: auto; height: auto" />
      <p class="tw-text-xs tw-font-bold">Nenhum registro encontrado</p>
    </div>
  </NewSGCard>
  <!-- FIM CONTEÚDO PRINCIPAL -->
</template>

<script setup>
import { api } from 'src/boot/axios';
import AreaChartComponent from 'src/components/graphic/AreaChartComponent.vue';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import { ref, onMounted, computed } from 'vue';
import handleApiErrors from 'src/services/handleApiErrors';
import { storeToRefs } from 'pinia';
import { useGlobal } from 'stores/global';

// Variáveis de estado
let loading = ref(false);
const salesData = ref({});

const global = useGlobal();
const { company } = storeToRefs(global);

// Cálculo das datas de início e fim do ano atual
const currentYear = new Date().getFullYear();
const startDate = `${currentYear}-01-01`;
const endDate = `${currentYear}-12-31`;

const months = [
  'Jan',
  'Fev',
  'Mar',
  'Abr',
  'Mai',
  'Jun',
  'Jul',
  'Ago',
  'Set',
  'Out',
  'Nov',
  'Dez'
];
const hasPermission = (type) => {
  const codPlano = Number(company.value?.codPlano) || 0;
  const documentoFiscal = company.value?.documentoFiscal;
  const empresaWithAdicional = company.value?.empresaWithAdicional || [];

  // PLANO BÁSICO
  if (codPlano === 2) {
    switch (type) {
      case 'NF-e':
        return documentoFiscal === 'NF-e' || documentoFiscal === 'NFC-e';
      case 'PDV-NM':
        return documentoFiscal === 'NFC-e';
      case 'PDV-NFCE':
      case 'OS':
        return false;
      default:
        return false;
    }
  }

  // PLANO MASTER E PLANO TESTE
  if (codPlano === 3 || codPlano === 1) {
    switch (type) {
      case 'NF-e':
      case 'PDV-NFCE':
      case 'PDV-NM':
        return true;

      case 'OS': {
        return empresaWithAdicional.length > 0;
      }

      default:
        return false;
    }
  }

  return false;
};

const parseSalesData = (data) => {
  return months.map((month) => {
    const lowerCaseMonth = month.toLowerCase();
    const value = data?.[lowerCaseMonth] || '0.00'; // Valor padrão como "0"
    const formattedValue = value.replace(/\./g, '').replace(',', '.');
    return parseFloat(formattedValue);
  });
};

const chartSeries = computed(() => {
  const seriesTypes = ['NF-e', 'PDV-NFCE', 'PDV-NM', 'OS'];

  return seriesTypes.reduce((acc, type) => {
    if (!hasPermission(type)) return acc;

    let data;
    switch (type) {
      case 'NF-e':
        data = parseSalesData(salesData.value?.nfe?.totalVendasNFE || {});
        break;
      case 'PDV-NFCE':
        data = parseSalesData(salesData.value?.pdv?.totalVendasNFCE || {});
        break;
      case 'PDV-NM':
        data = parseSalesData(salesData.value?.pdv?.totalVendasNM || {});
        break;
      case 'OS':
        data = parseSalesData(salesData.value?.os?.totalOS || {});
    }

    acc.push({ name: type, data });
    return acc;
  }, []);
});

const fetchData = async () => {
  loading.value = true;
  await api
    .get(`/api/dashboard/total-vendas`, {
      params: {
        filters: {
          created_at: {
            filterType: 'DATE_TIME_RANGE',
            filterValue: {
              startDate: `${startDate} 00:00:00`,
              endDate: `${endDate} 23:59:59`
            }
          }
        }
      }
    })
    .then(({ data }) => {
      salesData.value = data;
    })
    .catch((error) => {
      handleApiErrors(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

onMounted(fetchData);

const canShowAreaChart = computed(
  () =>
    !loading.value &&
    chartSeries.value.some((series) => series.data.some((val) => val !== 0))
);
</script>
