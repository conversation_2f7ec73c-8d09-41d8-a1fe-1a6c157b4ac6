<template>
  <NewSGCard
    id="suporte-dashboard"
    class="tw-size-full tw-overflow-hidden"
    status="warning"
    title-class="tw-text-SGBRGray"
    :has-circle="false"
    :has-title-separator="false"
  >
    <h2 class="flex items-center justify-center gap-2 tw-text-xl tw-font-bold">
      💬 Precisa de ajuda?
    </h2>

    <p class="tw-mt-6 tw-text-center tw-text-base tw-text-gray-700">
      Nosso time está disponível 24h para te atender!
    </p>

    <div class="tw-mt-6 tw-flex tw-flex-col tw-items-center tw-gap-2">
      <q-btn
        no-caps
        unelevated
        @click="openSupport(user, route.meta.title)"
        class="tw-w-full tw-max-w-[240px] tw-rounded-md tw-bg-blue-600 tw-py-1.5 tw-font-bold tw-text-white tw-shadow-sm hover:tw-bg-blue-700"
      >
        <template #default>
          <span class="tw-flex tw-items-center tw-gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="tw-h-5 tw-w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M2 5a2 2 0 012-2h12a2 2 0 012 2v10a2 2 0 01-2 2H6l-4 4V5z"
              />
            </svg>
            Solicitar suporte no chat
          </span>
        </template>
      </q-btn>
    </div>

    <p
      class="tw-mt-10 tw-border-t tw-pt-4 tw-text-center tw-text-xs tw-text-gray-400"
    >
      Se preferir,
      <a href="#" class="tw-text-blue-500 hover:tw-underline">
        solicite suporte remoto através do XRemote
      </a>
    </p>

    <div class="tw-mt-2 tw-flex tw-justify-center">
      <q-btn
        flat
        no-caps
        unelevated
        href="https://static.sgbr.com.br/xremote-releases/client/latest/XRemoteClient-Setup.exe"
        class="tw-inline-flex tw-max-w-[240px] tw-items-center tw-gap-2 tw-rounded-md tw-px-2 tw-py-1.5 tw-text-xs tw-text-blue-500 hover:tw-bg-blue-50"
        style="border: 1px solid rgb(147, 197, 253)"
      >
        <template #default>
          <div class="tw-flex tw-items-center tw-gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="tw-h-4 tw-w-4"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                d="M3 14a1 1 0 100 2h14a1 1 0 100-2H3zM7 10h2V3h2v7h2l-3 3-3-3z"
              />
            </svg>
            <span class="tw-text-xs">Baixar XRemote</span>
          </div>
        </template>
      </q-btn>
    </div>
  </NewSGCard>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { openSupport } from 'components/utils/support';
import { useGlobal } from 'stores/global';
import { storeToRefs } from 'pinia';
import NewSGCard from 'src/core/components/SG/Card/NewSGCard.vue';

const route = useRoute();

const store = useGlobal();

const { user } = storeToRefs(store);
</script>
