<template>
  <SGPage title="Dashboard" id="dashboard-index">
    <!-- <PERSON><PERSON><PERSON><PERSON> de filtro e mostrar atalhos -->
    <template #contentHeader>
      <q-icon
        v-if="!isEditMode"
        name="img:/icons/edit.svg"
        size="25px"
        class="tw-ml-2 tw-h-[1.1rem] tw-w-4 tw-cursor-pointer tw-items-center tw-justify-center tw-rounded-[4px] tw-align-middle"
        @click="habilitarGrid()"
      />
      <q-btn
        v-if="isEditMode"
        :ripple="false"
        class="q-px-sm tw-ml-2 tw-min-w-fit tw-rounded-lg tw-text-SGBRGray"
        style="border: 1px solid #cecece"
        flat
        label="Selecionar cards"
        @click="dashboardConfig"
      >
      </q-btn>
      <q-btn
        v-if="isEditMode"
        @click="restoreLayout()"
        class="tw-h-2 tw-w-2"
        icon="settings_backup_restore"
        size="0.7rem"
        flat
        color="primary"
      >
        <TooltipCustom text-tooltip="Restaurar padrão" />
      </q-btn>
    </template>
    <template #buttons>
      <DateFilter
        @on-save="() => updateFilters(true)"
        v-model:fields="fields"
        v-model:set-field-value="setFieldValue"
        :disable="isEditMode"
      />

      <q-btn
        v-if="hasAcess.value"
        :flat="showCardAtalhos"
        :outline="!showCardAtalhos"
        dense
        color="primary"
        :class="
          showCardAtalhos
            ? 'tw-rounded-md tw-bg-SGBRBlueLighten '
            : 'tw-rounded-md'
        "
        @click="showCardAtalhos = !showCardAtalhos"
        :disable="isEditMode"
      >
        <q-icon
          :class="showCardAtalhos ? 'tw-text-SGBRWhite' : ''"
          name="menu_open"
        />
      </q-btn>
      <TooltipCustom
        v-if="hasAcess.value && isEditMode"
        text-tooltip="O botão ficará desabilitado durante a edição de layout."
      />
    </template>

    <div
      class="tw-grid-rows-auto tw-grid tw-grid-cols-12 tw-gap-3"
      :class="isEditMode ? 'tw-mb-8' : ''"
    >
      <!-- Loading  -->

      <div
        v-if="loading"
        class="tw-col-span-full tw-flex tw-h-96 tw-flex-col tw-items-center tw-justify-center md:tw-col-span-full"
        :class="
          showCardAtalhos
            ? 'lg:tw-col-span-9 xl:tw-col-span-9'
            : 'lg:tw-col-span-full xl:tw-col-span-full'
        "
      >
        <q-circular-progress
          indeterminate
          size="60px"
          :thickness="0.22"
          color="primary"
          track-color="white"
          class="tw-mt-10"
        />

        <h3 class="tw-mt-2 tw-text-SGBRBlueDarken">Carregando...</h3>
      </div>

      <!-- Gridstack Container -->
      <div
        v-else
        class="grid-stack tw-col-span-full md:tw-col-span-full lg:tw-row-span-full xl:tw-row-span-full"
        :class="
          showCardAtalhos
            ? 'lg:tw-col-span-9 xl:tw-col-span-9'
            : 'lg:tw-col-span-full xl:tw-col-span-full'
        "
      >
        <div
          v-for="card in cardsFiltered"
          :key="card"
          class="grid-stack-item"
          :id="card.id"
          :gs-x="card.x"
          :gs-y="card.y"
          :gs-w="card.w"
          :gs-h="card.h"
          :gs-min-w="card.minW"
          :gs-min-h="card.minH"
          :gs-max-h="card.maxH"
        >
          <div class="grid-stack-item-content">
            <component
              v-if="card.condition ?? true"
              :is="card.component"
              v-model="card.model"
              :loading="card.loading"
            />
          </div>
        </div>
      </div>
      <!-- Atalhos -->
      <Transition name="slide-fade">
        <div
          v-if="showCardAtalhos"
          class="tw-col-span-full tw-py-1 lg:tw-col-start-10 lg:tw-row-span-full lg:tw-row-start-1 md:xl:tw-row-start-1 xl:tw-col-start-10 xl:tw-row-span-full"
        >
          <Atalhos :show-novidades="true" />
        </div>
      </Transition>
    </div>

    <ActionBar
      v-if="isEditMode"
      :buttons="buttons"
      action-text="Após a edição, lembre-se de salvar suas alterações!"
      scope="dashboard"
      :animate="animateActionBar"
    />
  </SGPage>
</template>

<script setup>
import SGPage from 'components/generic/SGPage.vue';
import { GridStack } from 'gridstack';
import 'gridstack/dist/gridstack.min.css';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import ActionBar from 'src/components/actionBar/index.vue';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import Atalhos from 'src/modules/dashboard/components/Atalhos.vue';
import DateFilter from 'src/modules/dashboard/components/DateFilter.vue';
import EntradasEspecies from 'src/modules/dashboard/components/graficos/EntradasEspecies.vue';
import SaidasEspecies from 'src/modules/dashboard/components/graficos/SaidasEspecies.vue';
import Suporte from 'src/modules/dashboard/components/Suporte.vue';
import ProdutosMaisVendidos from 'src/modules/dashboard/components/tabelas/ProdutosMaisVendidos.vue';
import FluxoCaixa from 'src/modules/dashboard/components/totalizadores/FluxoCaixa.vue';
import Gastos from 'src/modules/dashboard/components/totalizadores/Gastos.vue';
import Recebimentos from 'src/modules/dashboard/components/totalizadores/Recebimentos.vue';
import TotalVendas from 'src/modules/dashboard/components/graficos/TotalVendas.vue';
import { linksAcessoRapido } from 'src/modules/dashboard/models/linksAcessoRapido';
import { useFormSchema } from 'src/modules/dashboard/models/useFormSchema';
import { useSteps } from 'stores/api/firstStep';
import { useGlobal } from 'stores/global';
import { computed, nextTick, onMounted, ref, shallowRef, watch } from 'vue';
import { onBeforeRouteLeave, useRouter } from 'vue-router';
import DashboardConfig from '../components/DashboardConfig.vue';
import Movimentacoes from '../components/graficos/Movimentacoes.vue';
import GruposMaisVendidos from '../components/graficos/GruposMaisVendidos.vue';
import ProdutosVencer from '../components/tabelas/ProdutosVencer.vue';
import RankingFuncionario from '../components/totalizadores/RankingFuncionario.vue';
import {
  fetchAnivesarioCliente,
  fetchFinanceiroSaldo,
  fetchGruposMaisVendidos,
  fetchMovimentacoesEspecies,
  fetchMovimentoSaldo,
  fetchPrdutosMaisVendidos,
  fetchProdutosVencendo,
  fetchRankingFuncionarios
} from '../composables/useDashboardRequests';

import _ from 'lodash';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import Aniversariantes from '../components/tabelas/Aniversariantes.vue';
import useDashboardAction from '../composables/useDashboardAction';

const $q = useQuasar();
const router = useRouter();
const global = useGlobal();

const { company, perfil } = storeToRefs(global);
const { setFieldValue, values, fields } = useFormSchema();
const fluxoCaixaModel = ref({});
const recebimentosModel = ref({});
const gastosModel = ref({});
const entradasEspeciesModel = ref([]);
const saidasEspeciesModel = ref([]);
const produtosMaisVendidosModel = ref([]);
const RankingFuncionarioModel = ref([]);
const AniversarioClientesModel = ref([]);
const ProdutosVencerModel = ref([]);
const GruposMaisVendidosRef = ref([]);
const loading = ref(false);
const initiGrid = ref(false);
const showCardAtalhos = ref(
  JSON.parse(localStorage.getItem('showCardAtalhos') || 'false')
);
const hasAcess = computed(() => {
  const acess = ref(false);
  linksAcessoRapido.forEach((link) => {
    if (global.roles.includes(link.role)) {
      acess.value = true;
    }
  });
  return acess;
});

const fetchAllRequest = async (ids = []) => {
  if (!initiGrid.value) return;
  loading.value = true;

  const fetchMap = {
    1: {
      fetch: () => fetchMovimentoSaldo(values.startDate, values.endDate),
      model: fluxoCaixaModel
    },
    2: {
      fetch: () =>
        fetchFinanceiroSaldo(values.startDate, values.endDate, 'REC'),
      model: recebimentosModel
    },
    3: {
      fetch: () =>
        fetchFinanceiroSaldo(values.startDate, values.endDate, 'PAY'),
      model: gastosModel
    },
    5: {
      fetch: () =>
        fetchMovimentacoesEspecies(values.startDate, values.endDate, 'REC'),
      model: entradasEspeciesModel
    },
    6: {
      fetch: () =>
        fetchMovimentacoesEspecies(values.startDate, values.endDate, 'PAY'),
      model: saidasEspeciesModel
    },
    8: {
      fetch: () => fetchPrdutosMaisVendidos(values.startDate, values.endDate),
      model: produtosMaisVendidosModel
    },
    11: {
      fetch: () => fetchGruposMaisVendidos(values.startDate, values.endDate),
      model: GruposMaisVendidosRef
    },
    12: {
      fetch: () => fetchProdutosVencendo(values.startDate, values.endDate),
      model: ProdutosVencerModel
    },
    13: {
      fetch: () => fetchRankingFuncionarios(values.startDate, values.endDate),
      model: RankingFuncionarioModel
    },
    14: {
      fetch: () => fetchAnivesarioCliente(values.startDate, values.endDate),
      model: AniversarioClientesModel
    }
  };

  const requests = Object.keys(fetchMap)
    .filter((id) => ids.includes(Number(id)))
    .map((id) => {
      const { fetch, model } = fetchMap[id];
      return fetch().then((response) => {
        model.value = response;
      });
    });

  await Promise.all(requests);
  loading.value = false;
};

const cards = ref([
  {
    id: 1,
    component: shallowRef(FluxoCaixa),
    title: 'Saldo de caixa',
    model: fluxoCaixaModel,
    x: 0,
    y: 0,
    w: 4,
    h: 2,
    minW: 2,
    minH: 2,
    loading: loading.value
  },
  {
    id: 2,
    component: shallowRef(Recebimentos),
    title: 'Recebimentos',
    model: recebimentosModel,
    x: 4,
    y: 0,
    w: 4,
    h: 2,
    minW: 2,
    minH: 2,
    loading: loading.value
  },
  {
    id: 3,
    component: shallowRef(Gastos),
    title: 'Pagamentos',
    model: gastosModel,
    x: 8,
    y: 0,
    w: 4,
    h: 2,
    minW: 2,
    minH: 2,
    loading: loading.value
  },
  {
    id: 4,
    component: shallowRef(TotalVendas),
    title: 'Movimentações',
    x: 0,
    y: 2,
    w: 6,
    h: 7,
    minW: 5,
    minH: 4,
    loading: loading.value
  },
  {
    id: 5,
    component: shallowRef(EntradasEspecies),
    title:
      entradasEspeciesModel.value.length != 0
        ? ''
        : 'Recebimentos por espécies',

    model: entradasEspeciesModel,
    x: 6,
    y: 2,
    w: 3,
    h: 7,
    minW: 3,
    minH: 7,
    loading: loading.value
  },
  {
    id: 6,
    component: shallowRef(SaidasEspecies),
    title:
      saidasEspeciesModel.value.length != 0 ? '' : 'Pagamentos por espécies',

    model: saidasEspeciesModel,
    x: 9,
    y: 2,
    w: 3,
    h: 7,
    minW: 3,
    minH: 7,
    loading: loading.value
  },
  {
    id: 7,
    component: shallowRef(Suporte),
    x: 0,
    y: 9,
    w: 3,
    h: 7,
    minW: 3,
    minH: 8,
    loading: loading.value
  },
  {
    id: 8,
    component: shallowRef(ProdutosMaisVendidos),
    model: produtosMaisVendidosModel,
    x: 4,
    y: 9,
    w: 9,
    h: 8,
    minW: 3,
    minH: 8,
    maxH: 8,
    loading: loading.value
  },
  {
    id: 10,
    component: shallowRef(Movimentacoes),

    x: null,
    y: null,
    w: 8,
    h: 8,
    minW: 8,
    minH: 8,
    loading: loading.value
  },
  {
    id: 11,
    component: shallowRef(GruposMaisVendidos),
    model: GruposMaisVendidosRef,
    x: null,
    y: null,
    w: null,
    h: null,
    minW: 3,
    minH: 7,
    loading: loading.value
  },
  {
    id: 12,
    component: shallowRef(ProdutosVencer),
    model: ProdutosVencerModel,
    x: null,
    y: null,
    w: null,
    h: null,
    minW: 6,
    minH: 8,
    maxH: 8,
    loading: loading.value
  },
  {
    id: 13,
    component: shallowRef(RankingFuncionario),
    model: RankingFuncionarioModel,
    x: null,
    y: null,
    w: 5,
    h: 5,
    minW: 5,
    minH: 5,
    loading: loading.value
  },
  {
    id: 14,
    component: shallowRef(Aniversariantes),
    model: AniversarioClientesModel,
    x: null,
    y: null,
    w: null,
    h: null,
    minW: 6,
    minH: 7,
    maxH: 8,
    loading: loading.value
  }
]);

let grid = null;
const isEditMode = ref(false);
const cardsFiltered = ref([]);
const savedCardIds = ref(
  JSON.parse(localStorage.getItem('dashboard-layout')) ?? [
    1, 2, 3, 4, 5, 6, 7, 8
  ]
);

const currentSavedCardIds = ref([]);
function moveItems(value) {
  grid.getGridItems().forEach((el) => {
    el.gridstackNode.noResize = value;
    el.gridstackNode.noMove = value;
  });
}

function habilitarGrid() {
  if (!grid) return;

  currentSavedCardIds.value = JSON.parse(
    localStorage.getItem('dashboard-layout')
  ) || [1, 2, 3, 4, 5, 6, 7, 8];

  moveItems(false);

  isEditMode.value = true;
  grid.setStatic(false);
  grid.enableMove(true);
  grid.enableResize(true);
}

function getFilteredCards(ids = []) {
  if (!initiGrid.value) return;
  const newPosition = JSON.parse(localStorage.getItem('cardStates')) || null;

  const defaultCard = _.cloneDeep(cards.value);
  cardsFiltered.value = defaultCard.filter((card) => {
    return ids.includes(card.id);
  });

  if (newPosition) {
    cardsFiltered.value.forEach((card) => {
      const position = newPosition.find((item) => item.id == card.id);
      if (position) {
        card.h = position.h;
        card.w = position.w;
        card.x = position.x;
        card.y = position.y;
      }
    });
  }
}

onMounted(async () => {
  await updateFilters();
});

const updateFilters = async () => {
  await fetchAllRequest(savedCardIds.value);
  await getFilteredCards(savedCardIds.value);
  await initGridStack();
};

async function mountGridStack() {
  grid.destroy(false);
  initGridStack();
}

async function initGridStack() {
  grid = GridStack.init({
    float: true,
    maxRow: 100,

    column: 12,
    margin: 6,
    cellHeight: '50px',
    oneColumnSize: 640
  });
  if (initiGrid.value) {
    grid.setStatic(true);
    grid.enableMove(false);
    grid.enableResize(false);
    await nextTick();

    grid.on('change', onChange);
  }
}

const dashboardConfig = async () => {
  // Retorna um array com os ids dos cards selecionados
  const SelectedCards = await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: DashboardConfig,
        scope: 'dashboard-config',
        title: 'Selecionar cards',
        hasCancel: false,
        hasSave: false,
        dataModal: {
          selectedCards: savedCardIds.value
        }
      }
    })
      .onOk((cod) => resolve(cod))
      .onCancel(() => resolve(false));
  });

  if (SelectedCards) {
    savedCardIds.value = SelectedCards;
    await fetchAllRequest(SelectedCards);
    await getFilteredCards(SelectedCards);
    mountGridStack();
    habilitarGrid();
  }
};

const onChange = (event, elements) => {
  elements.forEach((element) => {
    const card = cardsFiltered.value.find((card) => card.id == element.el.id);
    if (card) {
      card.x = element.x;
      card.y = element.y;
      card.w = element.w;
      card.h = element.h;
    }
  });
};

const saveGrid = async () => {
  if (!grid) return;

  const gridChanged = Array.from(
    cardsFiltered.value.map((card) => {
      return {
        id: card.id,
        x: card.x,
        y: card.y,
        w: card.w,
        h: card.h
      };
    })
  );

  localStorage.setItem('cardStates', JSON.stringify(gridChanged));
  localStorage.setItem('dashboard-layout', JSON.stringify(savedCardIds.value));

  isEditMode.value = false;
  await getFilteredCards(savedCardIds.value);
  mountGridStack();
};

async function restoreLayout() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(setLayoutDefault);
}

async function setLayoutDefault() {
  currentSavedCardIds.value = [];
  isEditMode.value = false;
  localStorage.removeItem('dashboard-layout');
  if (JSON.parse(localStorage.getItem('cardStates'))) {
    await localStorage.removeItem('cardStates');
  }

  savedCardIds.value = [1, 2, 3, 4, 5, 6, 7, 8];
  cardsFiltered.value = [];
  await fetchAllRequest(savedCardIds.value);
  await getFilteredCards(savedCardIds.value);
  mountGridStack();
}

const cancelGridChanges = async () => {
  if (!grid) return;
  savedCardIds.value = currentSavedCardIds.value;
  isEditMode.value = false;
  localStorage.setItem(
    'dashboard-layout',
    JSON.stringify(currentSavedCardIds.value)
  );

  await getFilteredCards(currentSavedCardIds.value);
  mountGridStack();
};

const actionBarEvents = {
  save: {
    callback: () => {
      saveGrid();
    }
  },
  cancel: {
    callback: () => {
      cancelGridChanges();
    }
  }
};
const buttons = useDashboardAction({
  events: actionBarEvents
});

watch(showCardAtalhos, async (newValue) => {
  await localStorage.setItem('showCardAtalhos', JSON.stringify(newValue));
  await mountGridStack();
});
const animateActionBar = ref(false);
function runAnimation() {
  animateActionBar.value = true;
  setTimeout(() => {
    animateActionBar.value = false;
  }, 1000);
}

onBeforeRouteLeave(() => {
  if (isEditMode.value) {
    runAnimation();
    return false;
  }

  return true;
});

/* Redireciona para bem vindo quando usuário não tem permisão para ver a dashboard */

if (!global.roles.includes('DASHBOARD:MENU')) {
  router.push('/bem-vindo');
}

/* CASO O PLANO NÃO CONTENHA VENDEDOR */
if (!perfil?.value?.includes('VENDEDOR')) {
  const steps = useSteps();
  steps.get(company.value.controle);
  const { data: firstSteps } = storeToRefs(steps);

  watch(
    firstSteps,
    (newFirstSteps) => {
      /* CASO O PLANO CONTENHA BASICO */
      if (company.value?.empresaWithPlano?.descricao?.includes('BÁSICO')) {
        newFirstSteps.contaBancaria = true;

        if (company.value?.documentoFiscal == 'NFC-e') {
          newFirstSteps.configNfe = true;
        } else if (company.value?.documentoFiscal == 'NF-e') {
          newFirstSteps.configNfce = true;
        }
      }

      const keysSteps = Object.keys(newFirstSteps);

      // CONFERE QUAIS PASSOS ESTAO FALTANDO (TIRANDO MDFE)
      const falseValues = keysSteps.filter(
        (step) => newFirstSteps[step] == false && step != 'configMdfe'
      );

      /* CASO O PRIMEIROS PASSOS NÃO TENHA NADA VERDADEIRO E NÃO SENDO IGNORADO */
      if (
        falseValues.length >= 1 &&
        !localStorage.getItem(global.localStepsName)
      ) {
        initiGrid.value = false;
        if (!global.isCompanyDataFilled) {
          /* SE OS DADOS DA EMPRESA NÃO ESTIVEREM PREENCHIDOS */
          router.push('/dados-empresa');
        } else {
          router.push('/primeiros-passos');
        }
      } else {
        initiGrid.value = true;
      }
    },
    {
      immediate: true
    }
  );
}
</script>
<style scoped>
.slide-fade-enter-active {
  transition: all 0.2s;
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}
.grid-stack {
  height: 100vh;
  width: 100%;
}

.grid-stack-item-content {
  border-radius: 5px;
  text-align: center;
}
</style>
