import { useGlobal } from 'src/stores/global';
import API from 'stores/Api';
import { createStore } from 'stores/storeApiFactory';

const global = useGlobal();

const options = {
  name: 'report_template_central',
  extraParameters: { codEmpresa: global?.company?.controle || '' }
};

const api_instance = new API({
  global: true,
  url_default: '/api/relatorio-geral/modelo-relatorio',
  url_post: '/api/relatorio-geral/modelo-relatorio',
  url_put: '/api/relatorio-geral/modelo-relatorio',
  url_restore: '/api/relatorio-geral/modelo-relatorio/restore',
  url_duplicate: 'api/relatorio-geral/duplicar-modelo-relatorio'
});

export const useReportTemplateCentralStore = createStore(api_instance, options);
