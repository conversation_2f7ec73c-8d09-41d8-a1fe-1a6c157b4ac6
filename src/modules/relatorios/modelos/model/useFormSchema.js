import useFormFields from 'src/core/composables/useFormFields';
import { useFilterValidation } from 'src/modules/relatorios/modelos/composables/useFilterValidation';
import { array, boolean, mixed, number, object, string } from 'yup';

/**
 * Retorna os objetos de formulários vinculados com suas configurações de validação.
 * @returns {Object} Um objeto contendo os formulários vinculados e funções auxiliares.
 */
export function useFormSchema(initialValues = false) {
  const schema = object({
    controle: string().default('').nullable(),
    nome: string().default('').required('O campo é obrigatório.'),
    modulo: number().default(null).required('O campo é obrigatório.'),
    model: string().default('').nullable(),
    orientacao: string().default('R').required('O campo é obrigatório.'), // R - retrato / L - paisagem
    cabecalho: boolean().default(true).nullable(),
    descricao: string().default('').nullable(),

    // TODO: Criar algum tipo de documentação informando que esta parte dos dados serão persistidas em JSON, qualquer alteração pode interferir em relatórios já criados.
    filtersV2: array()
      .of(
        object({
          group: array().of(mixed()).nullable(),
          operator: string().default('').nullable(),
          field: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          filterType: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          cast: string().default('').nullable(),
          filterValue: mixed()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          fieldType: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          camposConfiguracao: array().nullable()
        })
      )
      .default([])
      .nullable()
      .test({
        name: 'valid-filters',
        message: 'Preencha os filtros fixos corretamente',
        test: function (value) {
          const { validateFilters } = useFilterValidation();
          return validateFilters(value);
        }
      }),
    dynamicFiltersV2: array()
      .of(
        object({
          field: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          filterType: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          cast: string().default('').nullable(),
          operator: string().default('').nullable(),
          filterValue: mixed().default('').nullable(),
          fieldType: string()
            .default('')
            .when('group', {
              is: (group) => Boolean(group),
              then: (schema) => schema.nullable(),
              otherwise: (schema) => schema.required('O campo é obrigatório.')
            }),
          camposConfiguracao: array().nullable()
        })
      )
      .default([])
      .nullable(),
    select: array()
      .of(
        object({
          controle: string().default('').nullable(), // Controle do módulo
          tipoCampo: string().default('').nullable(), // Tipo do campo (ex.: string, integer, decimal)
          cast: string().default('').nullable(), // Para campos personalizados, sempre o cast do primeiro selecionado.
          nomeCampo: string().default('').nullable(), // Nome do campo (ex.: valorFinal)
          descricao: string().default('').nullable(), // Descrição do campo
          relacionamento: string().default('').nullable(), // Nome e hierarquia de relacionamentos.
          relacionamentoDescricao: string().default('').nullable(), // Descrição visível do nome do relacionamento
          show: boolean().default(false).nullable(), // Mostrar ou ocultar o campo no relatório.
          isCustom: boolean().default(false).nullable(), // Caso o campo seja customizado.
          operation: string().default('').nullable(), // Operação matemática de campos personalizados exposta ao usuário.
          camposConfiguracao: array().of(mixed()).nullable(), // Para campos que possuem ENUM (status, tipo valor desconto, etc)

          // Campo que serve como ID no front.
          campoRelacionamento: string().default('').nullable(), // Hierarquia + campo
          relationshipHierarchy: string().default('').nullable(), // Hierarquia de relacionamentos sem o nome do campo
          operationWithCast: string().default('').nullable() // Para campos personalizados que representam uma operação matemática, cada campo precisa ter seu cast definido.
        })
      )
      .default([])
      .test(
        'Deve conter pelomenos 1 campo marcado',
        'Adicione ao menos um campo para geração do relatório.',
        (value) => {
          const hasAtLeastOneField = value.some((field) => field && field.show);

          return hasAtLeastOneField;
        }
      ),
    operation: array()
      .of(
        object({
          cast: null,
          alias: string().default(''),
          field: string().default('').required('O campo é obrigatório.'), // Nome do campo em snake case
          description: string().default('').required('O campo é obrigatório.'), // Descrição do campo
          type: string().default('').required('O campo é obrigatório.'), // sum, avg, count, min, max
          fieldType: string().default('').required('O campo é obrigatório.'), // string, integer, decimal
          calc: string().default('').nullable(), // cálculo do campo, caso seja personalizado.
          groupBy: boolean().default(false).nullable()
        })
      )
      .default([])
      .nullable(),
    group: array()
      .of(
        object({
          cast: string().default(null).nullable(),
          alias: string().default(''),
          field: string().default('').required('O campo nome é obrigatório.'), // Nome do campo em snake case
          fieldType: string().default('').required('Campo é obrigatório.'), // string, integer, decimal
          description: string()
            .default('')
            .required('O campo nome é obrigatório.'), // Descrição do campo
          calc: string().default('').nullable(), // cálculo do campo, caso seja personalizado.
          type: string().default('').required('O campo tipo é obrigatório.'), // sum, avg, count, min, max
          groupBy: boolean().default(false).nullable()
        })
      )
      .default([])
      .nullable()
      .test(
        'at-least-one-group-by',
        'Pelo menos um campo deve ter agrupar selecionado',
        (value) => {
          if (!value || value.length === 0) return true;
          return value.some((item) => item && item.groupBy === true);
        }
      ),
    orderBy: array()
      .of(
        object({
          field: string().default('').required('O campo é obrigatório.'),
          order: string().default('').required('O campo é obrigatório.')
        })
      )
      .default([])
      .nullable()
  });

  return useFormFields(schema, initialValues);
}
