<template>
  <section
    class="ignore-fields tw-col-span-full tw-flex tw-flex-col tw-gap-2 tw-gap-x-4 lg:tw-grid lg:tw-grid-cols-9"
  >
    <!-- Campos -->
    <div class="tw-relative tw-col-span-2 tw-h-full">
      <q-input
        ref="searchInputRef"
        :valid="true"
        v-model="moduleFilterString"
        v-bind="ModelInputText"
        class="small-input small-text no-label"
        placeholder="Digite para buscar..."
        bottom-slots
      >
        <template #hint>
          <Transition name="fade">
            <span
              v-if="moduleFilterString"
              class="tw-text-[11px] tw-font-bold tw-text-textsSGBR-gray"
              >{{ filteredModuleFields.length }} resultados encontrados.</span
            >
          </Transition>
        </template>
        <template #append>
          <Shortkey v-if="showShortKey" text="F2" />
          <q-icon size="20px" name="search" color="primary"></q-icon>
        </template>
        <template #after>
          <Shortkey v-if="showShortKey" :text="`${altOrOption} + O`" />
          <q-btn
            v-if="Object.values(props.moduleData || {}).length"
            @click="onToggleExtraFieldsModal"
            :valid="true"
            color="primary"
            icon="add"
            padding="0"
            flat
          />
          <TooltipCustom text-tooltip="Adicionar campos do módulo" />
        </template>
      </q-input>

      <!-- :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed':
            schema.select.errorMessage
        }" -->
      <div
        class="customScroll tw-h-[420px] tw-min-h-[360px] tw-overflow-auto tw-rounded-md tw-bg-SGBRGrayBG tw-px-2 md:tw-min-h-[420px]"
      >
        <q-spinner
          v-if="isLoading"
          color="primary"
          size="3rem"
          thickness="4"
          class="tw-mx-auto tw-h-full"
        />

        <Sortable
          v-if="!isLoading"
          ref="sortableRef"
          :list="filteredModuleFields"
          :options="options"
          tag="div"
          :item-key="(item) => item?.controle || item?.nomeCampo"
          handle=".handle"
          @end="onEnd"
        >
          <template #item="{ element }">
            <li
              :key="element.nomeCampo"
              class="tw-my-2 tw-flex tw-min-h-[24px] tw-w-full tw-flex-row tw-items-center tw-gap-1 tw-rounded-md tw-border tw-bg-white tw-px-2 tw-py-1"
              :class="{
                draggable: !moduleFilterString.length,
                'tw-h-10 tw-border tw-border-SGBRBlueUltraLighten':
                  element.isCustom
              }"
            >
              <div class="tw-flex tw-h-full tw-items-center">
                <q-icon
                  v-if="!moduleFilterString?.length"
                  name="drag_indicator"
                  class="handle tw-cursor-grab tw-text-black"
                  size="18px"
                />
                <q-checkbox
                  v-model="element.show"
                  @update:model-value="removeFieldFromOrderBy($event, element)"
                  size="26px"
                >
                  <TooltipCustom text-tooltip="Exibir campo no relatório" />
                </q-checkbox>
                <span
                  v-if="element.isCustom"
                  class="tw-mx-1 tw-flex tw-h-4 tw-w-2 tw-items-center tw-justify-center tw-rounded-sm tw-bg-tablesSGBR-lightGreen"
                >
                  <TooltipCustom text-tooltip="Campo personalizado" />
                </span>
              </div>
              <div
                class="tw-flex tw-w-full tw-items-center tw-justify-between tw-gap-1"
              >
                <span class="tw-text-[11px] tw-font-medium tw-text-SGBRGray">
                  {{ element.descricao }}
                  <TooltipCustom :text-tooltip="elementTooltip(element)" />
                </span>

                <q-icon
                  v-if="!moduleFilterString?.length"
                  @click="removeExtraField(element)"
                  name="img:/icons/trash-closed-red.svg"
                  color="negative"
                  class="tw-cursor-pointer tw-rounded-[4px] tw-bg-[#fd9999]"
                  size="16px"
                >
                  <TooltipCustom text-tooltip="Remover campo" />
                </q-icon>
              </div>
            </li>
          </template>
        </Sortable>
      </div>

      <SGButton
        v-if="select.value.length > 0"
        @click="onToggleCustomFieldsModal()"
        title="Personalizados"
        title-class="tw-text-SGBRBlueLighten"
        :shortkey="`${altOrOption} + p`"
        tooltip="Clique para adicionar campos personalizados"
        class="tw-mt-2 tw-w-full tw-flex-1 tw-rounded-md"
        color="primary"
        outline
        dense
        no-caps
      />

      <div
        v-if="select.value.length > 0"
        class="tw-mt-1 tw-flex tw-flex-wrap tw-items-center tw-justify-center tw-gap-2"
      >
        <SGButton
          @click="clearSelectFields"
          title="Remover todos"
          title-class="tw-text-[10px] tw-text-SGBRRedBrown"
          shortkey="ctrl+shift+del"
          tooltip="Remover todos os campos"
          padding="0px 4px"
          class="!tw-h-6 tw-rounded-md"
          color="negative"
          flat
          dense
          no-caps
        />
        <SGButton
          @click="clearCustomFields"
          title="Remover personalizados"
          title-class="tw-text-[10px] tw-text-SGBRRedBrown"
          :shortkey="`${altOrOption} + del`"
          tooltip="Remover campos personalizados"
          padding="0px 4px"
          class="!tw-h-6 tw-rounded-md"
          size="12px"
          color="negative"
          flat
          dense
          no-caps
        />
      </div>
    </div>

    <q-spinner
      v-if="isLoading"
      color="primary"
      size="3rem"
      thickness="4"
      class="tw-col-span-7 tw-mx-auto tw-min-h-[450px]"
    />

    <!-- Agrupamentos e preview -->
    <section v-else class="tw-col-span-7 tw-transition tw-duration-200">
      <div
        v-if="columns.length"
        :key="columns"
        class="customScroll tw-mb-2 tw-flex tw-flex-1 tw-flex-row tw-items-center tw-justify-around tw-gap-4 tw-overflow-auto tw-rounded-md tw-border-b tw-bg-SGBRBlueLighten tw-p-1 tw-py-3"
      >
        <span
          v-for="col in columns"
          :key="col"
          class="tw-w-fit tw-max-w-[220px] tw-text-xs tw-font-medium tw-text-SGBRWhite"
        >
          {{ col.label }}
        </span>
      </div>

      <q-expansion-item
        v-if="!!select.value.length"
        v-model="filterExpanded"
        @update:model-value="onExpandFilters"
        expand-separator
        label="Filtros fixos"
        caption="Filtros fixos, não alteráveis no momento da geração e sempre vigentes para este modelo."
        class="tw-mb-2 tw-rounded-md tw-border"
        :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed':
            schema.filtersV2.errorMessage
        }"
        :header-class="[
          'tw-p-2 tw-h-fit tw-min-h-[32px] tw-rounded-md tw-text-sm tw-text-SGBRBlueLighten',
          {
            'tw-bg-SGBRButtonBlueLighten tw-rounded-md': filterExpanded
          }
        ]"
      >
        <FilterLine
          ref="filterLineRef"
          @clean-up-groups="onCleanUpGroups(schema.filtersV2.value)"
          v-model:filters="schema.filtersV2.value"
          v-model:fields="select.value"
        />
      </q-expansion-item>

      <q-expansion-item
        v-if="!!select.value.length"
        v-model="dynamicFilterExpanded"
        @update:model-value="onExpandDynamicFilters"
        expand-separator
        label="Filtros dinâmicos"
        caption="Selecione os filtros que serão definidos no momento da geração do relatório."
        class="tw-mb-2 tw-rounded-md tw-border"
        :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed':
            schema.dynamicFiltersV2.errorMessage
        }"
        :header-class="[
          'tw-p-2 tw-h-fit tw-min-h-[32px] tw-rounded-md tw-text-sm tw-text-SGBRBlueLighten',
          {
            'tw-bg-SGBRButtonBlueLighten tw-rounded-md': dynamicFilterExpanded
          }
        ]"
      >
        <FilterLine
          ref="dynamicFilterLineRef"
          @clean-up-groups="onCleanUpGroups(schema.dynamicFiltersV2.value)"
          v-model:filters="schema.dynamicFiltersV2.value"
          v-model:fields="select.value"
          :is-dynamic="true"
        />
      </q-expansion-item>

      <q-expansion-item
        v-if="!!select.value.length"
        v-model="totalizerExpanded"
        @update:model-value="onExpandTotalizers"
        expand-separator
        label="Totalizadores"
        caption="Defina totalizadores. (Ex: total de vendas por espécie.)"
        class="tw-mb-2 tw-rounded-md tw-border"
        :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed':
            schema.operation.errorMessage
        }"
        :header-class="[
          'tw-p-2 tw-h-fit tw-min-h-[32px] tw-rounded-md tw-text-sm tw-text-SGBRBlueLighten',
          {
            'tw-bg-SGBRButtonBlueLighten tw-rounded-md': totalizerExpanded
          }
        ]"
      >
        <Operations
          ref="operationsRef"
          v-model:operations="schema.operation"
          :select="select.value"
        />
      </q-expansion-item>

      <q-expansion-item
        v-if="!!select.value.length"
        v-model="groupExpanded"
        @update:model-value="onExpandGroup"
        expand-separator
        label="Agrupamentos"
        caption="Defina agrupamentos. (Ex: agrupado por espécie.)"
        class="tw-mb-2 tw-rounded-md tw-border"
        :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed': schema.group.errorMessage
        }"
        :header-class="[
          'tw-p-2 tw-h-fit tw-min-h-[32px] tw-rounded-md tw-text-sm tw-text-SGBRBlueLighten',
          {
            'tw-bg-SGBRButtonBlueLighten tw-rounded-md': groupExpanded
          }
        ]"
      >
        <Group
          ref="groupRef"
          v-model:group="schema.group"
          :select="select.value"
        />
      </q-expansion-item>

      <q-expansion-item
        v-if="select.value.filter((field) => field.show)?.length"
        v-model="orderByExpanded"
        @update:model-value="onExpandOrderBy"
        expand-separator
        label="Ordenações"
        caption="Defina a ordem de exibição dos registros baseado nos campos visíveis do modelo."
        class="tw-mb-2 tw-rounded-md tw-border"
        :class="{
          'tw-border-2 tw-border-tablesSGBR-lightRed':
            schema.orderBy.errorMessage
        }"
        :header-class="[
          'tw-p-2 tw-h-fit tw-min-h-[32px] tw-rounded-md tw-text-sm tw-text-SGBRBlueLighten',
          {
            'tw-bg-SGBRButtonBlueLighten tw-rounded-md': orderByExpanded
          }
        ]"
      >
        <OrderBy
          ref="orderByRef"
          v-model:order-by="schema.orderBy"
          :select="select.value"
        />
      </q-expansion-item>
    </section>

    <MiniModal
      v-if="showExtraFieldsModal"
      v-model="showExtraFieldsModal"
      title="Adicionar campos"
      scope="extra-fields-modal"
      :has-cancel="false"
      :has-save="false"
      card-section-class="lg:!tw-w-[400px]"
    >
      <ExtraFieldModal
        @on-save="onFieldsSelection"
        @on-cancel="onToggleExtraFieldsModal"
        v-model:all-fields="allFields"
        :module-data="moduleData"
        :select="select"
      />
    </MiniModal>

    <MiniModal
      v-if="showCustomFieldsModal"
      v-model="showCustomFieldsModal"
      title="Campos personalizados"
      scope="custom-field-modal"
      :has-cancel="false"
      :has-save="false"
    >
      <CustomFields
        @on-save="onCustomFieldConfirmation"
        :select="select.value"
        scope="custom-field-modal"
      />
    </MiniModal>
  </section>
</template>

<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import Sortable from 'src/core/components/Sortable.vue';
import { moveColumns, normalizeText } from 'src/services/utils';
import { computed, ref, toRefs } from 'vue';
import FilterLine from 'src/modules/relatorios/modelos/components/FilterLine.vue';
import ModelInputText from 'src/core/models/inputs/Text';
import ExtraFieldModal from 'src/modules/relatorios/modelos/components/modal/ExtraFieldModal.vue';
import MiniModal from 'src/components/modal/MiniModal.vue';
import Shortkey from 'src/components/generic/shortkey/Shortkey.vue';
import { useGlobal } from 'src/stores/global';
import { storeToRefs } from 'pinia';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys';
import CustomFields from 'src/modules/relatorios/modelos/components/modal/CustomFields.vue';
import SGButton from 'src/core/components/SG/Buttons/SGButton.vue';
import Operations from 'src/modules/relatorios/modelos/components/Operations.vue';
import Group from 'src/modules/relatorios/modelos/components/Group.vue';
import OrderBy from 'src/modules/relatorios/modelos/components/OrderBy.vue';

const allFields = defineModel('all-fields', {
  type: Map,
  default: () => new Map()
});

const schema = defineModel('schema', {
  type: Object,
  default: () => {}
});

const props = defineProps({
  schemaValues: {
    type: Object,
    required: true
  },
  moduleData: {
    type: [Object, null],
    required: true
  },
  isLoading: {
    type: [Boolean, null],
    required: false,
    default: false
  },
  errors: {
    type: Object,
    required: false,
    default: () => {}
  }
});

// Campos selecionados
const { select } = schema.value;

const { isLoading } = toRefs(props);
const global = useGlobal();
const { showShortKey, altOrOption } = storeToRefs(global);

// Template refs
const searchInputRef = ref('');
const sortableRef = ref(null);
const filterLineRef = ref();
const dynamicFilterLineRef = ref();
const operationsRef = ref();
const groupRef = ref();
const orderByRef = ref();

// Refs
const moduleFilterString = ref('');
const filterExpanded = ref(false);
const dynamicFilterExpanded = ref(false);
const totalizerExpanded = ref(false);
const groupExpanded = ref(false);
const orderByExpanded = ref(false);

// Modals
const showExtraFieldsModal = ref(false);
const showCustomFieldsModal = ref(false);

// Dragging
const options = ref({
  animation: 200,
  easing: 'cubic-bezier(1, 0, 0, 1)',
  sort: true,
  group: 'fields',
  disabled: false,
  handle: '.handle',
  ghostClass: 'ghost',
  draggable: '.draggable',
  scroll: true
});

// Filtering
const columns = computed(() => {
  return select.value
    ?.filter((campo) => campo.show)
    ?.map((campo) => ({
      name: campo.nomeCampo,
      field: campo?.relacionamento
        ? campo.relacionamento + '.' + campo.nomeCampo
        : campo.nomeCampo,
      label: campo.descricao,
      align: 'left',
      sort: 'desc'
    }));
});

const filteredModuleFields = computed(() => {
  const normalizedSearch = normalizeText(moduleFilterString.value);

  return select.value
    ?.map((field) => {
      if (!('show' in field)) field.show = false;
      return field;
    })
    .filter((field) => {
      const normalizedField = normalizeText(field.descricao);
      return normalizedField.includes(normalizedSearch);
    });
});

const elementTooltip = (field) => {
  return field?.descricaoRelacionamento
    ? `${field.descricaoRelacionamento} - ${field.descricao}`
    : field.descricao;
};

const onToggleCustomFieldsModal = () => {
  showCustomFieldsModal.value = !showCustomFieldsModal.value;
};

const onExpandFilters = (isExpanded) => {
  if (isExpanded && schema.value.filtersV2.value.length === 0) {
    filterLineRef.value?.addFilter();
  }
};

const onExpandDynamicFilters = (isExpanded) => {
  if (isExpanded && schema.value.dynamicFiltersV2.value.length === 0) {
    dynamicFilterLineRef.value?.addFilter();
  }
};
const onExpandTotalizers = (isExpanded) => {
  if (isExpanded && schema.value.operation.value.length === 0) {
    operationsRef.value?.onAddOperation();
  }
};

const onExpandGroup = (isExpanded) => {
  if (isExpanded && schema.value.group.value.length === 0) {
    groupRef.value?.onAddGroup();
  }
};

const onExpandOrderBy = (isExpanded) => {
  if (isExpanded && schema.value.orderBy.value.length === 0) {
    orderByRef.value?.onAddOrderBy();
  }
};

const onCustomFieldConfirmation = ({ selected, unselected }) => {
  unselected?.forEach((field) => removeExtraField(field, 'nomeCampo'));
  selected?.forEach((field) => addExtraField(field, 'nomeCampo'));
  onToggleCustomFieldsModal();
};

const onFieldsSelection = ({ selected, unselected }) => {
  selected?.forEach((field) => addExtraField(field));
  unselected?.forEach((field) => removeExtraField(field));
  onToggleExtraFieldsModal();
};

const onToggleExtraFieldsModal = async () => {
  showExtraFieldsModal.value = !showExtraFieldsModal.value;
};

const addExtraField = (field, key = 'campoRelacionamento') => {
  const index = select.value.findIndex((campo) => {
    return campo[key] === field[key] && campo?.isCustom === field?.isCustom;
  });

  const exists = index !== -1;

  if (!exists) {
    select.value.push(field);
  } else {
    select.value[index] = field;
  }
};

const removeExtraField = (field, key = 'campoRelacionamento') => {
  const index = select.value.findIndex((campo) => {
    return campo[key] === field[key] && campo?.isCustom === field?.isCustom;
  });
  const exists = index !== -1;

  if (exists) {
    let selects = select.value;
    selects.splice(index, 1);
    select.setValue(selects);
  }

  removeFieldFromFilters(field);
};

const removeFieldFromFilters = (field) => {
  const filterByField = (filters) => {
    return filters.reduce((acc, filter) => {
      if (filter.field === field.nomeCampo) {
        return acc;
      }

      // Nested groups
      if (filter.group && Array.isArray(filter.group)) {
        const filteredGroup = filterByField(filter.group);
        if (filteredGroup.length > 0) {
          acc.push({ ...filter, group: filteredGroup });
        }
        return acc;
      }

      // Keep non-matching fields
      acc.push(filter);
      return acc;
    }, []);
  };

  const filtersV2 = filterByField(schema.value.filtersV2.value);
  const dynamicFiltersV2 = filterByField(schema.value.dynamicFiltersV2.value);

  schema.value.filtersV2.setValue(filtersV2);
  schema.value.dynamicFiltersV2.setValue(dynamicFiltersV2);
};

function removeFieldFromOrderBy(val, field) {
  if (!val) {
    const orderBy = schema.value.orderBy.value.filter(
      (order) => order.field !== field.campoRelacionamento
    );
    schema.value.orderBy.setValue(orderBy);
  }
}

// Remove subgrupos
function cleanUpGroupsRecursively(filtersArray) {
  for (let i = filtersArray.length - 1; i >= 0; i--) {
    const filter = filtersArray[i];
    if (filter && filter.group && Array.isArray(filter.group)) {
      if (filter.group.length === 0) {
        filtersArray.splice(i, 1);
      } else {
        cleanUpGroupsRecursively(filter.group);
      }
    }
  }
}

function onCleanUpGroups(filters) {
  cleanUpGroupsRecursively(filters);
}

function onEnd(event) {
  const { oldIndex, newIndex } = event;
  const selectedFields = [...select.value];
  moveColumns(selectedFields, oldIndex, newIndex);
  select.setValue(selectedFields);
}

function clearSelectFields() {
  select.setValue([]);
}

function clearCustomFields() {
  const cleared = select.value.filter((field) => !field.isCustom);
  select.setValue(cleared);
}

// Definir atalhos
const hotkeyScope = 'register-register-report-template';
const atalhos = [
  {
    key: 'f2',
    event: () => searchInputRef?.value?.focus?.()
  },
  {
    key: 'alt+p',
    event: onToggleCustomFieldsModal
  },
  {
    key: 'alt+o',
    event: onToggleExtraFieldsModal
  },
  {
    key: 'alt+del',
    event: clearCustomFields
  },
  {
    key: 'ctrl+shift+del',
    event: clearSelectFields
  }
];

useScopedHotkeys(atalhos, hotkeyScope);
defineExpose({ searchInputRef });
</script>

<style scoped>
.ghost {
  opacity: 0.3;
}
</style>
