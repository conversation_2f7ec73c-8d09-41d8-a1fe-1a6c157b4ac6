<template>
  <section class="tw-mt-2 tw-h-fit tw-pb-2">
    <div class="tw-my-2 tw-flex tw-flex-col tw-gap-4">
      <div
        v-for="(operation, idx) in group.value"
        :key="operation.field"
        class="tw-mt-2 tw-flex tw-w-full tw-flex-col tw-gap-2 tw-px-4 md:tw-flex-row"
      >
        <q-checkbox
          v-model="operation.groupBy"
          size="28px"
          dense
          label="Agrupar"
        ></q-checkbox>

        <q-input
          @update:model-value="
            (val) => onOperationDescriptionChange(val, operation)
          "
          v-model="operation.description"
          v-bind="ModelInputText"
          label="Nome"
          class="small-input small-text tw-flex-1"
        />

        <InputSelect
          :key="FIELD_OPTIONS"
          @selected="
            (val) => {
              onOperationFieldChange(val, operation);
            }
          "
          :model-value="operation.field"
          v-bind="{
            ...ModelInputSelectSearch,
            params: {
              optionsStatic: FIELD_OPTIONS
            }
          }"
          :clearable="false"
          label="Campo"
          :option-value="getItemCampoRelacionamentoOuOperation"
          option-label="descricao"
          class="small-input small-text tw-flex-1"
          no-label
        />

        <InputSelect
          v-model="operation.type"
          v-bind="{
            ...ModelInputSelectSearch,
            params: {
              optionsStatic: getFilterType(operation.fieldType)
            }
          }"
          :clearable="false"
          label="Tipo"
          class="small-input small-text tw-flex-1"
          no-label
        />

        <q-icon
          @click="removeGroup(idx)"
          name="img:/icons/trash-closed-red.svg"
          color="negative"
          class="-tw-mt-2 tw-mb-2 tw-cursor-pointer tw-rounded-[4px] tw-bg-[#fd9999] md:tw-mb-2 md:tw-mt-1"
          size="1.1rem"
        />
      </div>
    </div>

    <div
      class="tw-mx-2 tw-flex tw-items-center tw-justify-start tw-gap-2 tw-pl-2"
    >
      <q-btn
        @click="onAddGroup"
        :disable="shouldBlockNewGroup"
        name="filter"
        class="tw-mb-2 !tw-h-6 !tw-min-h-6 tw-bg-SGBRGrayBG tw-px-1 tw-py-0"
        no-caps
      >
        <TooltipCustom
          :text-tooltip="
            shouldBlockNewGroup
              ? 'Complete as informações do agrupamento atual'
              : 'Adicionar agrupamento'
          "
        />
        <q-icon name="add" class="tw-text-xs" size="0.8rem" />
        <span class="tw-text-[11px] tw-text-textsSGBR-gray">Agrupamento</span>
      </q-btn>
    </div>
  </section>
</template>

<script setup>
import _ from 'lodash';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputText from 'src/core/models/inputs/Text';
import { computed, nextTick } from 'vue';

/*
  Alterações em operations sendo feitas diretamente no objeto (operations.value.value.push(...))
  para evitar validação não intencional pelo schema.
*/

const group = defineModel('group', {
  type: Array,
  required: true,
  default: () => []
});

const props = defineProps({
  select: {
    type: Object,
    required: true,
    default: () => {}
  }
});

const getFilterType = (fieldType) => {
  let filterTypes = OPERATIONS;
  switch (fieldType) {
    case 'decimal':
    case 'integer': {
      filterTypes = INTEGER_OPERATIONS;
      break;
    }
  }

  return filterTypes;
};

const shouldBlockNewGroup = computed(() => {
  return group.value.value?.some(
    (group) => !group.field || !group.type || !group.description
  );
});

const getItemCampoRelacionamentoOuOperation = (item) => {
  return item?.campoRelacionamento || item?.operation;
};

async function onAddGroup() {
  const groupIndex = group.value.value.length;

  const newGroupOperation = {
    alias: ``,
    field: '',
    fieldType: '',
    type: '',
    calc: '',
    cast: null,
    description: '',
    groupBy: false
  };

  if (groupIndex < 0) {
    group.value.value.push({
      group: [newGroupOperation]
    });
    return;
  }

  group.value.value[groupIndex] = newGroupOperation;
}

async function onOperationFieldChange(field, operation) {
  await nextTick();

  operation.field = getItemCampoRelacionamentoOuOperation(field);
  operation.fieldType =
    field?.tipoCampo == 'operation' ? 'decimal' : field?.tipoCampo;
  operation.type = '';
  operation.cast = field?.cast || null;
  operation.calc = setTotalizerCast(field);
}

function setTotalizerCast(field) {
  const campo = getItemCampoRelacionamentoOuOperation(field);

  let calc = field?.cast ? `${campo}/${parseInt(field.cast)}` : campo;

  return field?.operationWithCast ? field?.operationWithCast : calc;
}

function onOperationDescriptionChange(val, operation) {
  operation.alias = _.snakeCase(val);
}

function removeGroup(index) {
  const groups = group.value.value;
  groups.splice(index, 1);
  group.value.setValue(groups);
}

const INTEGER_OPERATIONS = [
  {
    controle: 'sum',
    descricao: 'Somar'
  },
  {
    controle: 'avg',
    descricao: 'Média'
  },
  {
    controle: 'max',
    descricao: 'Máximo'
  },
  {
    controle: 'min',
    descricao: 'Mínimo'
  },
  {
    controle: 'count',
    descricao: 'Contar'
  },
  {
    controle: 'none',
    descricao: 'Nenhum'
  }
];

const OPERATIONS = [
  {
    controle: 'count',
    descricao: 'Contar'
  },
  {
    controle: 'none',
    descricao: 'Nenhum'
  }
];

const FIELD_OPTIONS = computed(() => props.select.map((f) => f));

defineExpose({
  onAddGroup,
  removeGroup
});
</script>
