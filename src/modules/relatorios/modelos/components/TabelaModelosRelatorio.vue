<template>
  <SGCard id="report-template-table">
    <q-tabs
      v-model="tab"
      @update:model-value="
        () => {
          tableSelectedItems = [];
        }
      "
      dense
      narrow-indicator
      class="tw-col-span-full tw-w-full tw-text-SGBRGray"
      align="left"
      active-color="primary"
    >
      <q-tab
        name="central"
        label="RELATÓRIOS SISTEMA"
        class="tw-p-0 tw-text-[12px]"
        :content-class="`tw-py-0 tw-px-2`"
      />
      <q-tab
        name="tenant"
        label="RELATÓRIOS PERSONALIZADOS"
        class="tw-p-0 tw-text-[12px]"
        :content-class="`tw-py-0 tw-px-2 `"
      />
    </q-tabs>

    <q-tab-panels
      class="tw-col-span-full tw-overflow-hidden"
      id="modelos-relatorio-tab"
      v-model="tab"
      animated
      swipeable
      keep-alive
    >
      <!-- class="tw-mb-10" -->
      <q-tab-panel class="tw-overflow-hidden" name="central">
        <SGTable
          v-show="tab === 'central'"
          v-model:selected="tableSelectedItems"
          :store="reportCentralStore"
          :columns="columns"
          :actions="tableActionsCentral"
          :table-bind="tableBind"
          search-input-column-name="nome"
        >
          <template #extra-filter>
            <InputSelect
              v-if="!props?.dataModal?.modulo"
              @update:model-value="
                (value) => handleModuloFilter({ value, isCentral: true })
              "
              v-model="moduloFiltroCentral"
              v-bind="{
                ...ModelInputSelectSearch,
                params: {
                  apiRoute: '/api/relatorio-geral',
                  central: true,
                  filterOr: ['controle', 'descricao'],
                  filtersV2: [
                    {
                      field: 'principal',
                      filterType: 'EQUALS',
                      filterValue: true
                    }
                  ],
                  relationships: [
                    'campos',
                    'relacionamentos',
                    'relacionamentos.campos'
                  ],
                  extraParameters: {
                    codEmpresa: global?.company?.controle || ''
                  }
                }
              }"
              clearable
              class="tw-w-56"
              label="Módulo"
              required
            />
          </template>
        </SGTable>
      </q-tab-panel>

      <q-tab-panel class="tw-overflow-hidden" name="tenant">
        <SGTable
          v-show="tab === 'tenant'"
          v-model:selected="tableSelectedItems"
          :store="reportStore"
          :columns="columns"
          :actions="tableActions"
          :table-bind="tableBind"
          search-input-column-name="nome"
        >
          <template #extra-filter>
            <InputSelect
              v-if="!props?.dataModal?.modulo"
              @update:model-value="
                (value) => handleModuloFilter({ value, isCentral: false })
              "
              v-model="moduloFiltro"
              v-bind="{
                ...ModelInputSelectSearch,
                params: {
                  apiRoute: '/api/relatorio-geral',
                  central: true,
                  filterOr: ['controle', 'descricao'],
                  filtersV2: [
                    {
                      field: 'principal',
                      filterType: 'EQUALS',
                      filterValue: true
                    }
                  ],
                  relationships: [
                    'campos',
                    'relacionamentos',
                    'relacionamentos.campos'
                  ],

                  extraParameters: {
                    codEmpresa: global?.company?.controle || ''
                  }
                }
              }"
              clearable
              class="tw-w-56"
              label="Módulo"
              required
            />
            <SGActiveInactiveFilter :store="reportStore" />
          </template>
        </SGTable>
      </q-tab-panel>
    </q-tab-panels>
  </SGCard>

  <ActionBar
    v-if="tableSelectedItems.length !== 0 && tab === 'tenant'"
    :table-selected="tableSelectedItems"
    :buttons="actionBarButtons"
  />

  <ActionBar
    v-if="tableSelectedItems.length !== 0 && tab === 'central'"
    :table-selected="tableSelectedItems"
    :buttons="actionBarButtonsCentral"
  />
</template>

<script setup>
import SGActiveInactiveFilter from 'src/core/components/SG/Table/Filter/SGActiveInactiveFilter.vue';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import SGCard from 'components/generic/SGCard.vue';
import ActionBar from 'src/components/actionBar/index.vue';
import { onMounted, ref } from 'vue';
import RegisterDelete from 'components/modal/global/RegisterDelete.vue';
import { useQuasar } from 'quasar';
import RestoreUser from 'src/components/modal/global/RestoreUser.vue';
import { useGenerateReport } from 'src/modules/relatorios/modelos/composables/useGenerateReport';
import useReportTableActionbar from 'src/modules/relatorios/modelos/composables/useReportTableActionbar';
import { useGlobal } from 'stores/global';
import { useRouter } from 'vue-router';
import { useReportTemplateStore } from 'src/modules/relatorios/modelos/store/useReportTemplateStore';
import { useReportTemplateCentralStore } from 'src/modules/relatorios/modelos/store/useReportTemplateCentralStore';
import { api_global } from 'src/boot/axios';
import handleApiErrors from 'src/services/handleApiErrors';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import AwaitGenerationModal from 'src/modules/relatorios/modelos/components/modal/AwaitGenerationModal.vue';

// Props do componente
const props = defineProps({
  dataModal: {
    type: Object,
    required: false,
    default: () => {}
  }
});

const global = useGlobal();
const router = useRouter();
const $q = useQuasar();

// Report Store
const reportStore = useReportTemplateStore();

// Report Central Store
const reportCentralStore = useReportTemplateCentralStore();

const tab = ref('central');
const moduloFiltro = ref();
const moduloFiltroCentral = ref();

const tableSelectedItems = ref([]);
const tableBind = ref({
  selection: props?.dataModal?.modulo ? 'none' : 'single'
});

const getModuloID = async () => {
  const params = {
    filtersV2: [
      {
        field: 'model',
        filterType: 'EQUALS',
        filterValue: props?.dataModal?.modulo || ''
      }
    ],
    page: 1,
    paginate: 20,
    codEmpresa: global?.company?.controle || ''
  };

  try {
    const { status, data } = await api_global.get('/api/relatorio-geral', {
      params
    });

    if (status !== 200 || !data?.data.length) return;

    return data?.data?.[0]?.controle;
  } catch (error) {
    handleApiErrors(error);
  }
};

onMounted(async () => {
  if (props?.dataModal?.modulo) {
    const moduloID = await getModuloID();

    moduloFiltro.value = moduloID;
    moduloFiltroCentral.value = moduloID;

    handleModuloFilter({ value: moduloID, isCentral: true });
    handleModuloFilter({ value: moduloID, isCentral: false });
  }
});

const restoreReportTemplate = async (register = false) => {
  const reportTemplate = register || tableSelectedItems.value?.[0];
  if (!reportTemplate) return;

  $q.dialog({
    component: RestoreUser,
    componentProps: { controle: reportTemplate.nome }
  }).onOk(async () => {
    const response = await reportStore.restore({
      controle: reportTemplate.controle
    });

    if (response) {
      await reportStore.updateRowsPerPage();
      tableSelectedItems.value = [];
    }
  });
};

function deleteReportTemplate(register = false) {
  const reportTemplate = register || tableSelectedItems.value?.[0];
  if (!reportTemplate) return;

  $q.dialog({
    component: RegisterDelete,
    componentProps: { itemName: reportTemplate.nome }
  }).onOk(async () => {
    const { success } = await reportStore.delete({
      controle: reportTemplate.controle
    });

    if (success) {
      await reportStore.updateRowsPerPage();
      tableSelectedItems.value = [];
    }
  });
}

const handleModuloFilter = async ({ value, isCentral }) => {
  const store = isCentral ? reportCentralStore : reportStore;

  delete store.table.filters['modulo'];

  if (value) {
    store.table.filters = {
      ...store.table.filters,
      ['modulo']: {
        field: 'modulo',
        filterType: 'EQUALS',
        filterValue: value
      }
    };
  }

  await store.updateRowsPerPage();
};

function duplicateReportTemplate(register = false) {
  const reportTemplate = register || tableSelectedItems.value?.[0];
  if (!reportTemplate) return;

  $q.sessionStorage.set('cloneId', {
    id: reportTemplate.controle,
    central: false
  });
  router.push('/relatorios/modelos/cadastro');
}

function duplicateReportCentralTemplate(register = false) {
  const reportTemplate = register || tableSelectedItems.value?.[0];
  if (!reportTemplate) return;

  $q.sessionStorage.set('cloneId', {
    id: reportTemplate.controle,
    central: true
  });
  router.push('/relatorios/modelos/cadastro');
}

async function handleReportGeneration({ report, format, isCentral }) {
  report.format = format || 'PDF';

  const { generateReport, getReportParams } = useGenerateReport();

  let params = await getReportParams({ report, isCentral });

  if (isCentral && params) params.isCentral = true;

  const response = await generateReport({ params, router });
  tableSelectedItems.value = [];

  if (response) {
    openAwaitGenerationModal(response);
  }
}
const isAwaitGenerationModalOpen = ref(false);
const openAwaitGenerationModal = (data) => {
  if (isAwaitGenerationModalOpen.value) return;

  isAwaitGenerationModalOpen.value = true;
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: AwaitGenerationModal,
      hasCancel: false,
      confirmLabel: 'Continuar usando o sistema',
      title: 'Gerando relatório',
      scope: 'aguardandoGeracao',
      dataModal: {
        controle: data?.controle
      }
    }
  })
    .onOk(() => {
      isAwaitGenerationModalOpen.value = false;
    })
    .onCancel(() => {
      isAwaitGenerationModalOpen.value = false;
    });
};

const tableActions = ref([
  {
    name: 'edit',
    icon: {
      name: 'img:/icons/edit.svg'
    },
    tooltip: 'Editar',
    disableOnCondition: true,
    condition: (row) =>
      !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:EDITAR'),
    action: (row) => router.push(`/relatorios/modelos/editar/${row.controle}`)
  },
  {
    name: 'more',
    icon: 'more_vert',
    tooltip: '',
    isMenu: true,
    menuItems: [
      {
        name: 'pdf',
        icon: {
          name: 'picture_as_pdf'
        },
        label: 'Gerar PDF',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'PDF',
            isCentral: false
          })
      },
      {
        name: 'xls',
        icon: {
          name: 'img:/icons/xls.svg'
        },
        label: 'Gerar XLS',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'XLS',
            isCentral: false
          })
      },
      {
        name: 'csv',
        icon: {
          name: 'img:/icons/csv.svg'
        },
        label: 'Gerar CSV',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'CSV',
            isCentral: false
          })
      },
      {
        name: 'duplicate',
        icon: {
          name: 'img:/icons/difference.svg'
        },
        label: 'Duplicar',
        condition: (row) =>
          !row?.deletedAt &&
          global.roles.includes('RELATORIO.MODELO:DUPLICAR') &&
          global.roles.includes('RELATORIO.MODELO:CADASTRAR'),
        action: (row) => duplicateReportTemplate(row)
      },
      {
        name: 'delete',
        icon: {
          name: 'img:/icons/trash-closed-red.svg',
          color: 'negative',
          class: 'tw-bg-[#fd9999] tw-rounded-[4px]',
          size: '0.8rem'
        },
        label: 'Excluir',
        disableOnCondition: true,
        condition: (row) =>
          !row.deletedAt && global.roles.includes('RELATORIO.MODELO:EXCLUIR'),
        action: (row) => deleteReportTemplate(row)
      },
      {
        name: 'restore',
        icon: {
          name: 'img:/icons/sync.svg',
          class: 'tw-bg-tablesSGBR-lightGreen tw-rounded-[4px]'
        },
        label: 'Reativar',

        condition: (row) =>
          global.roles?.includes('RELATORIO.MODELO:RESTAURAR') &&
          row.deletedAt !== null,
        disableOnCondition: false,
        action: (row) => restoreReportTemplate(row)
      }
    ]
  }
]);

const tableActionsCentral = ref([
  {
    name: 'edit',
    icon: {
      name: 'img:/icons/edit.svg'
    },
    tooltip: 'Editar',
    disableOnCondition: true,
    condition: () => false
  },
  {
    name: 'more',
    icon: 'more_vert',
    tooltip: '',
    isMenu: true,
    menuItems: [
      {
        name: 'pdf',
        icon: {
          name: 'picture_as_pdf'
        },
        label: 'Gerar PDF',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'PDF',
            isCentral: true
          })
      },
      {
        name: 'xls',
        icon: {
          name: 'img:/icons/xls.svg'
        },
        label: 'Gerar XLS',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'XLS',
            isCentral: true
          })
      },
      {
        name: 'csv',
        icon: {
          name: 'img:/icons/csv.svg'
        },
        label: 'Gerar CSV',
        condition: (row) =>
          !row?.deletedAt && global.roles.includes('RELATORIO.MODELO:GERAR'),
        action: (row) =>
          handleReportGeneration({
            report: row,
            format: 'CSV',
            isCentral: true
          })
      },
      {
        name: 'duplicate',
        icon: {
          name: 'img:/icons/difference.svg'
        },
        label: 'Duplicar',
        condition: (row) =>
          !row?.deletedAt &&
          global.roles.includes('RELATORIO.MODELO:DUPLICAR') &&
          global.roles.includes('RELATORIO.MODELO:CADASTRAR'),
        action: (row) => duplicateReportCentralTemplate(row)
      }
    ]
  }
]);

const columns = ref([
  {
    name: 'controle',
    field: 'controle',
    label: 'Código',
    align: 'left',
    sort: 'desc',
    filterType: 'EQUALS',
    sortable: true
  },
  {
    name: 'nome',
    field: 'nome',
    label: 'Nome do modelo',
    align: 'left',
    sort: 'desc',
    filterType: 'ILIKE',

    format: (modelo) => modelo?.toUpperCase(),
    sortable: true
  },
  {
    name: 'relatorioWithModulo.descricao',
    field: (row) => row?.relatorioWithModulo?.descricao?.toUpperCase() || '-',
    label: 'Módulo',
    align: 'left'
    // sort: 'desc',
    // filterType: 'ILIKE'
  },
  {
    name: 'descricao',
    field: (row) => row?.filtros?.descricao?.toUpperCase() || '-',
    label: 'Descrição',
    align: 'left',
    copyToClipboard: true

    // sort: 'desc',
    // filterType: 'ILIKE'
  },
  {
    name: 'createdAt',
    field: 'createdAt',
    label: 'Data criação',
    align: 'left',
    // sort: 'desc',
    // filterType: 'DATE_RANGE',
    format: (val) => {
      if (val) {
        const date = new Date(val);
        return date.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
        });
      }
      return '-';
    },
    tooltip: (row) => {
      if (row) {
        const date = new Date(row?.createdAt);
        return date.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      }
      return '-';
    }
  },

  {
    name: 'actions',
    field: 'actions',
    required: true,
    label: 'Ações',
    align: 'center'
  }
]);

const actionBarEvents = {
  edit: {
    callback: () =>
      router.push(
        `/relatorios/modelos/editar/${tableSelectedItems.value?.[0].controle}`
      ),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:EDITAR')
  },
  delete: {
    callback: deleteReportTemplate,
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:EXCLUIR')
  },
  duplicate: {
    callback: duplicateReportTemplate,
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:DUPLICAR') &&
      global.roles.includes('RELATORIO.MODELO:CADASTRAR')
  },
  generateReportPdf: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'PDF',
        isCentral: false
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  },
  generateReportXls: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'XLS',
        isCentral: false
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  },
  generateReportCsv: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'CSV',
        isCentral: false
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  },
  restore: {
    callback: () => restoreReportTemplate(tableSelectedItems.value?.[0]),
    condition: () => tableSelectedItems.value?.[0]?.deletedAt
  }
};

const actionBarEventsCentral = {
  duplicate: {
    callback: duplicateReportCentralTemplate,
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:DUPLICAR') &&
      global.roles.includes('RELATORIO.MODELO:CADASTRAR')
  },
  generateReportPdf: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'PDF',
        isCentral: true
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  },
  generateReportXls: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'XLS',
        isCentral: true
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  },
  generateReportCsv: {
    callback: () =>
      handleReportGeneration({
        report: tableSelectedItems.value?.[0],
        format: 'CSV',
        isCentral: true
      }),
    condition: () =>
      !tableSelectedItems.value?.[0]?.deletedAt &&
      global.roles.includes('RELATORIO.MODELO:GERAR')
  }
};

const actionBarButtons = useReportTableActionbar({
  selected: tableSelectedItems,
  events: actionBarEvents
});

const actionBarButtonsCentral = useReportTableActionbar({
  selected: tableSelectedItems,
  events: actionBarEventsCentral,
  central: true
});
</script>
