<template>
  <section
    class="tw-mb-1 tw-flex tw-items-center tw-justify-center md:tw-min-w-[900px]"
  >
    <q-spinner
      v-if="isLoading"
      size="3rem"
      color="primary"
      class="tw-min-h-[150px]"
    ></q-spinner>
    <FilterLine
      v-else
      v-model:filters="filters"
      v-model:fields="select"
      is-generating
    />
  </section>

  <section class="tw-flex tw-justify-end tw-gap-2">
    <q-btn
      v-close-popup
      unelevated
      flat
      dense
      class="tw-rounded-md tw-bg-SGBRGrayBG tw-px-2 tw-font-medium tw-text-SGBRGray"
      padding="6px 12px"
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Esc" />
        <p class="!tw-text-inherit">Cancelar</p>
      </div>
    </q-btn>

    <q-btn
      @click="onConfirm"
      class="tw-rounded-md tw-bg-SGBRBlueLighten tw-px-2 tw-font-medium tw-text-white"
      padding="6px 12px"
      :disable="isLoading"
      flat
      unelevated
      dense
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Enter" />
        <p class="tw-inline-block !tw-text-inherit">Gerar</p>
      </div>
    </q-btn>
  </section>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { api, api_global } from 'src/boot/axios';
import Shortkey from 'src/components/generic/shortkey/Shortkey.vue';
import FilterLine from 'src/modules/relatorios/modelos/components/FilterLine.vue';
import handleApiErrors from 'src/services/handleApiErrors';
import { useGlobal } from 'src/stores/global';
import { computed, ref } from 'vue';
import { useCastFilterValues } from 'src/modules/relatorios/modelos/composables/useCastFilterValues';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys';

const emits = defineEmits(['cancel', 'close', 'ok']);
const props = defineProps({
  dataModal: {
    type: Object,
    required: true,
    default: () => ({})
  },
  scope: {
    type: String,
    required: false,
    default: 'generate-report-model'
  },
  format: {
    type: String,
    required: false,
    default: 'PDF'
  },
  reportId: {
    type: [String, Number, null],
    required: true,
    default: null
  },
  isCentral: {
    type: Boolean,
    required: true,
    default: false
  }
});

const global = useGlobal();
const { showShortKey } = storeToRefs(global);

const { applyCastToFilterValues } = useCastFilterValues();

const filters = ref([]);
const select = ref([]);
let filtersV2 = [];
let route = null;
let codModeloRelatorio = null;
let operation = null;
let group = null;
let orderBy = null;
let model = null;
const isLoading = ref(false);

async function onCreated() {
  const API = props.isCentral ? api_global : api;

  try {
    isLoading.value = true;
    const { status, data: report } = await API.get(
      `/api/relatorio-geral/modelo-relatorio/${props.reportId}`,
      { params: { codEmpresa: global?.company?.controle || '' } }
    );

    isLoading.value = false;

    if (status === 200 && report) {
      filters.value = report.filtros?.dynamicFiltersV2 || [];
      select.value = report.filtros?.select;
      filtersV2 = report.filtros?.filtersV2 || [];
      route = report?.relatorioWithModulo?.rota?.toLowerCase();
      codModeloRelatorio = report?.controle || null;
      operation = report?.filtros?.operation || [];
      group = report?.filtros?.group || [];
      orderBy = report?.filtros?.orderBy || [];
      model = report?.filtros?.model || null;
    }
  } catch (error) {
    handleApiErrors(error);
  } finally {
    isLoading.value = false;
  }
}

function hasNullValues(value) {
  if (value === null || value === undefined) return true;

  if (Array.isArray(value)) {
    return value.some((item) => hasNullValues(item));
  }

  if (typeof value === 'object') {
    return Object.values(value).some((item) => hasNullValues(item));
  }

  return false;
}

function removeEmptyFilters(filters) {
  return filters.filter((filter) => {
    if (!filter || !filter.filterValue) return false;

    // Check if filterValue has null values based on its type
    if (hasNullValues(filter.filterValue)) {
      return false;
    }

    return true;
  });
}

function onConfirm() {
  const processedFilters = applyCastToFilterValues([
    ...filtersV2,
    ...filters.value
  ]);

  const cleanedFilters = removeEmptyFilters(processedFilters);

  emits('ok', {
    model,
    filtersV2: cleanedFilters,
    aggregation: {
      select: select.value,
      route,
      codModeloRelatorio,
      operation,
      group,
      format: props.format
    },
    orderBy: orderBy
  });
}

onCreated();

const hotkeyScope = 'generate-report-modal';
const atalhos = [
  {
    key: 'enter',
    event: () => onConfirm(),
    condition: computed(() => !isLoading.value)
  }
];

// Definir atalhos
useScopedHotkeys(atalhos, hotkeyScope);
</script>
