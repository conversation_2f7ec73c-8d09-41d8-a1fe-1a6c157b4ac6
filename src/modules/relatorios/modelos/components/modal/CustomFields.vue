<template>
  <section
    class="tw-flex tw-flex-col tw-flex-wrap tw-gap-4 lg:tw-grid lg:tw-min-w-[900px] lg:tw-grid-cols-12"
  >
    <div
      class="tw-col-span-6 tw-flex tw-w-full tw-flex-col tw-flex-wrap tw-gap-2"
    >
      <h4 class="-tw-mb-1 tw-text-textsSGBR-gray">Campos disponíveis</h4>

      <q-input
        ref="searchInputRef"
        v-model="searchString"
        v-bind="ModelInputText"
        class="small-input small-text no-label"
        placeholder="Digite para buscar..."
      >
        <template #append>
          <Shortkey v-if="showShortKey" text="F2" />
          <q-icon size="20px" name="search" color="primary"></q-icon>
        </template>
      </q-input>

      <div
        v-if="filteredFields.length > 0"
        class="customScroll tw-flex tw-h-[404px] tw-max-h-[404px] tw-flex-col tw-gap-2 tw-overflow-auto tw-rounded-md tw-border tw-border-quasar-inputBorder tw-p-2"
      >
        <div
          v-for="field in filteredFields"
          :key="field.nomeCampo"
          class="tw-text-xs tw-font-medium tw-text-textsSGBR-gray"
        >
          <q-btn
            @click="onSeletField(field)"
            icon="add"
            class="tw-h-6 tw-w-6"
            padding="0"
            size="10px"
            color="primary"
            dense
            flat
          >
            <TooltipCustom text-tooltip="Adicionar campo ao cálculo" />
          </q-btn>
          {{ field.descricao }}:
          <span class="tw-font-normal"> {{ field.nomeCampo }}</span>
        </div>
      </div>

      <div
        v-else
        class="customScroll tw-flex tw-h-[404px] tw-max-h-[404px] tw-flex-col tw-gap-2 tw-overflow-auto tw-rounded-md tw-border tw-border-quasar-inputBorder tw-p-2"
      >
        <div class="tw-flex tw-justify-center tw-rounded-md tw-p-4">
          <span class="tw-text-xs">Nenhum resultado encontrado.</span>
        </div>
      </div>
    </div>

    <div class="tw-col-span-6 tw-flex tw-w-full tw-flex-col tw-gap-2">
      <h4 class="-tw-mb-1 tw-text-textsSGBR-gray">Campos personalizados</h4>

      <div class="custom-required tw-flex tw-items-center tw-gap-2">
        <InputSelect
          @update:model-value="() => (operation = '')"
          v-model="tipoCampo"
          v-bind="{
            ...ModelInputSelect,
            params: {
              optionsStatic: CUSTOM_FIELDS_TYPES
            }
          }"
          option-value="tipoCampo"
          option-label="descricao"
          :clearable="false"
          label="Tipo"
          no-label
          class="small-input small-text tw-flex-1"
        />

        <q-input
          ref="nameInputRef"
          v-model="nomeCampo"
          v-bind="ModelInputText"
          label="Nome do campo"
          class="small-input small-text tw-flex-1"
          rows="2"
          required
          hide-bottom-space
        />
      </div>

      <q-input
        ref="operationInputRef"
        @keydown="preventSpaceOnKeydown"
        @update:model-value="handleOperationValueChange"
        :model-value="operation"
        v-bind="ModelInputText"
        type="textarea"
        label="Valor"
        class="tw-w-full tw-p-0"
        rows="2"
        required
        :hint="
          tipoCampo == 'operation'
            ? 'Certifique-se de que foi inserida uma operação matemática válida.'
            : undefined
        "
      >
        <TooltipCustom :text-tooltip="operation" />
      </q-input>

      <div class="tw-mt-2 tw-flex tw-justify-end">
        <SGButton
          @click="onAddCustomField"
          :title="
            !editingField && editingField !== 0 ? 'Adicionar' : 'Confirmar'
          "
          :shortkey="`${altOrOption} + enter`"
          tooltip="Adicionar cálculo à lista"
          padding="4px 12px"
          size="12px"
          color="primary"
          dense
          no-caps
        />
      </div>

      <SGTable
        :rows="[...customFields.values()]"
        :columns="columns"
        :actions="tableActions"
        :table-bind="tableBind"
        :searchable="false"
      />
    </div>
  </section>

  <section class="tw-mb-1 tw-flex tw-justify-end tw-gap-4">
    <q-btn
      v-close-popup
      unelevated
      flat
      dense
      class="tw-rounded-md tw-bg-SGBRGrayBG tw-px-2 tw-font-medium tw-text-SGBRGray"
      padding="6px 12px"
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Esc" />
        <p class="!tw-text-inherit">Cancelar</p>
      </div>
    </q-btn>

    <q-btn
      @click="onConfirm"
      class="tw-rounded-md tw-bg-SGBRBlueLighten tw-px-2 tw-font-medium tw-text-white"
      padding="6px 12px"
      flat
      unelevated
      dense
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Enter" />
        <p class="tw-inline-block !tw-text-inherit">Confirmar</p>
      </div>
    </q-btn>
  </section>
</template>

<script setup>
import _ from 'lodash';
import { storeToRefs } from 'pinia';
import Shortkey from 'src/components/generic/shortkey/Shortkey.vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import notify from 'src/components/utils/notify';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGButton from 'src/core/components/SG/Buttons/SGButton.vue';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys';
import ModelInputSelect from 'src/core/models/inputs/Select';
import ModelInputText from 'src/core/models/inputs/Text';
import { useGlobal } from 'src/stores/global';
import { computed, ref } from 'vue';

const emits = defineEmits(['on-save']);
const props = defineProps({
  select: {
    type: Object,
    required: true
  },
  scope: {
    type: String,
    required: false,
    default: 'custom-fields-modal'
  }
});

const global = useGlobal();
const { showShortKey, altOrOption } = storeToRefs(global);

// Refs
const operationInputRef = ref();

const tipoCampo = ref('concat');
const nomeCampo = ref('');
const operation = ref('');
const editingField = ref(null);
const nameInputRef = ref(null);

const searchString = ref('');
const searchInputRef = ref(null);

const customFields = ref(new Map());
const removedMap = ref(new Map());

// Computed to show only string matched results.
const filteredFields = computed(() => {
  return props.select.filter((field) => {
    return !field.isCustom && tipoCampo.value == 'operation'
      ? field.tipoCampo == 'decimal'
      : field.tipoCampo != 'decimal' &&
          (field.nomeCampo
            .toLowerCase()
            .includes(searchString.value.toLowerCase()) ||
            field.descricao
              .toLowerCase()
              .includes(searchString.value.toLowerCase()));
  });
});

// Populates the customFields map with the existing custom fields.
const fieldCastMap = ref({});
props.select.forEach((field) => {
  if (field.isCustom) customFields.value.set(field.nomeCampo, field);
  if (field.campoRelacionamento && field.cast && parseInt(field.cast) > 1) {
    fieldCastMap.value[field.campoRelacionamento] = parseInt(field.cast);
  }
});

const controlKeys = [
  'Backspace',
  'Delete',
  'ArrowLeft',
  'ArrowRight',
  'Tab',
  'Enter'
];

const mathSymbolsRegex = /^[0-9+\-*/().\s]$/;

const isMathKey = (key) => {
  return mathSymbolsRegex.test(key);
};

function preventSpaceOnKeydown(event) {
  const { key } = event;

  if (tipoCampo.value === 'operation') {
    if (!controlKeys.includes(key) && !isMathKey(key)) {
      event.preventDefault();
      event.stopPropagation();
    }
  }

  if (key === 'Enter') {
    onAddCustomField({
      tipo: tipoCampo.value,
      nome: nomeCampo.value,
      operacao: operation.value
    });
  }
}

function handleOperationValueChange(val) {
  operation.value = val;
}

function onSeletField(field) {
  if (!nomeCampo.value) nomeCampo.value = field.descricao;

  if (field.isCustom) {
    operation.value = `${operation.value} ${field.operation}`;
  } else {
    operation.value = `${operation.value} ${field.campoRelacionamento}`;
  }

  searchString.value = '';
  operationInputRef.value?.focus();
}

function onEditCustomField(field) {
  editingField.value = field.nomeCampo;
  tipoCampo.value = field.tipoCampo;
  nomeCampo.value = field.descricao;
  operation.value = field.operation;
}

function onAddCustomField() {
  if (!tipoCampo.value || !nomeCampo.value || !operation.value) return;

  const isRegisteringEqual =
    customFields.value.has(_.snakeCase(nomeCampo.value)) && !editingField.value;

  if (isRegisteringEqual) {
    notify('Campo personalizado já adicionado');
    return;
  }

  const { firstFieldCast, operationValue } = treatCast(operation.value);
  const newCustom = {
    field: nomeCampo.value,
    tipoCampo: tipoCampo.value,
    nomeCampo: _.snakeCase(nomeCampo.value),
    descricao: nomeCampo.value,
    operation: operation.value,
    operationWithCast: operationValue,
    cast: firstFieldCast,
    show: true,
    isCustom: true,
    isFiltered: false
  };

  // Se está editando e o nome mudou, deleta o campo antigo.
  if (editingField.value && editingField.value !== nomeCampo.value) {
    removedMap.value.set(
      editingField.value,
      customFields.value.get(editingField.value)
    );
    customFields.value.delete(editingField.value);
  }

  customFields.value.set(_.snakeCase(nomeCampo.value), newCustom);

  tipoCampo.value = 'concat';
  nomeCampo.value = '';
  operation.value = '';
  nameInputRef.value?.focus();
  editingField.value = null;
}

function onRemoveCustomField(field) {
  customFields.value.delete(field.nomeCampo);
  removedMap.value.set(field.nomeCampo, field);
}

function onConfirm() {
  const fields = {
    selected: [...customFields.value.values()],
    unselected: [...removedMap.value.values()]
  };

  emits('on-save', fields);
}

function treatCast(stringValue) {
  if (!stringValue || tipoCampo.value !== 'operation') {
    return { firstFieldCast: null, operationValue: stringValue };
  }

  let processedValue = stringValue;
  let firstFieldCast = null;
  let firstFieldFound = false;

  // Expressão regular para encontrar tokens (campos ou números) separados por operadores matemáticos
  // Isso captura:
  // 1. Identificadores (nomes de campos)
  // 2. Números (podem incluir decimais)
  // 3. Operadores matemáticos (+, -, *, /, parênteses)
  const tokenRegex = /([A-Za-z][A-Za-z0-9._]*|\d+(?:\.\d+)?|[+\-*/()])/g;

  // Dividimos a string em tokens (campos, números e operadores)
  const tokens = processedValue.match(tokenRegex) || [];
  const processedTokens = [];

  // Processamos cada token
  for (const token of tokens) {
    // Se o token é um identificador (possivelmente um nome de campo)
    if (/^[A-Za-z][A-Za-z0-9._]*$/.test(token) && fieldCastMap.value[token]) {
      const castFactor = fieldCastMap.value[token];

      // Registramos o primeiro campo encontrado ou o campo com cast igual a 100 (prioridade)
      if (!firstFieldFound || castFactor == '100') {
        firstFieldCast = castFactor;
        firstFieldFound = true;
      }

      // Envolvemos o campo com parênteses e aplicamos o fator de conversão
      processedTokens.push(`(${token}/${castFactor})`);
    } else {
      // Caso contrário, mantemos o token original
      processedTokens.push(token);
    }
  }

  // Reconstruímos a string de operação
  processedValue = processedTokens.join('');
  return { firstFieldCast, operationValue: processedValue };
}

const CUSTOM_FIELDS_TYPES = [
  { tipoCampo: 'concat', descricao: 'Texto' },
  { tipoCampo: 'operation', descricao: 'Operação' }
];

const columns = [
  {
    name: 'type',
    field: (row) => (row?.type === 'concat' ? 'Texto' : 'Operação'),
    label: 'Tipo',
    align: 'left',
    sortable: true
  },
  {
    name: 'nomeCampo',
    field: 'nomeCampo',
    label: 'Nome',
    align: 'left',
    sortable: true
  },
  {
    name: 'operation',
    field: 'operation',
    label: 'Operação',
    align: 'left',
    sortable: true
  },
  {
    name: 'actions',
    field: 'actions',
    label: 'Ações',
    headerClasses: '!tw-min-w-[80px] !tw-w-[80px] !tw-max-w-[80px] ',
    align: 'center',
    required: true,
    fixed: true
  }
];

const tableActions = ref([
  {
    name: 'edit',
    icon: {
      name: 'img:/icons/edit.svg'
    },
    tooltip: 'Editar campo',
    disableOnCondition: true,
    action: (row, scope) => onEditCustomField(row, scope.rowIndex)
  },
  {
    name: 'delete',
    icon: {
      name: 'img:/icons/trash-closed-red.svg',
      color: 'negative',
      class: 'tw-bg-[#fd9999] tw-rounded-[4px]',
      size: '0.8rem'
    },
    label: 'Excluir',
    action: (row) => onRemoveCustomField(row)
  }
]);

const tableBind = {
  selection: 'none',
  noDataImage: false,
  rowsPerPage: 5,
  class: '!tw-min-h-[287px] !tw-h-[287px]',
  headerClass: '!tw-h-8'
};

// Definir atalhos
const atalhos = [
  {
    key: 'f2',
    event: () => searchInputRef.value?.focus()
  },
  {
    key: 'alt+enter',
    event: () =>
      onAddCustomField({
        tipo: tipoCampo.value,
        nome: nomeCampo.value,
        operacao: operation.value
      })
  },
  {
    key: 'enter',
    event: onConfirm
  }
];
useScopedHotkeys(atalhos, props.scope);
</script>

<style scoped></style>
