''
<template>
  <span class="tw-max-w-[350px] tw-text-justify"
    >Selecione os campos que serão utilizados em filtros, agrupamentos ou em
    cálculos na geração do relatório.</span
  >

  <q-input
    ref="searchInputRef"
    :debounce="200"
    v-model="searchString"
    v-bind="ModelInputText"
    class="small-input small-text no-label tw-mt-2"
    placeholder="Digite para buscar..."
  >
    <template #append>
      <q-icon name="search" color="primary"></q-icon>
    </template>
  </q-input>

  <div class="tw-flex tw-items-center tw-justify-between">
    <q-checkbox
      :model-value="isAllSelected"
      @update:model-value="onSelectAll"
      label="Todos"
      class="tw-h-fit"
      size="28px"
      dense
    />

    <Transition name="fade">
      <span
        v-if="searchString"
        class="tw-text-[11px] tw-font-bold tw-text-textsSGBR-gray"
        >{{ Object.keys(extraFields).length }} resultados encontrados.</span
      >
    </Transition>
  </div>

  <section
    v-if="Object.keys(extraFields).length > 0"
    class="tw-relative tw-mb-2 tw-flex tw-flex-col tw-gap-2"
  >
    <div
      v-if="isLoadingRelationship || isSelectionLoading"
      class="tw-absolute tw-left-0 tw-top-0 tw-z-10 tw-flex !tw-size-full tw-h-[340px] tw-items-center tw-justify-center"
    >
      <q-spinner size="3rem" color="primary"></q-spinner>
    </div>
    <div
      ref="listRef"
      class="customScroll tw-relative tw-mb-2 tw-flex tw-max-h-[340px] tw-flex-col tw-gap-2 tw-overflow-auto tw-rounded-md tw-border tw-p-1"
    >
      <div
        v-for="(section, relationship) in extraFields"
        :key="relationship"
        class="tw-flex tw-flex-col"
        :class="{
          'tw-pointer-events-none tw-opacity-20':
            isLoadingRelationship || isSelectionLoading
        }"
      >
        <q-expansion-item
          dense
          expand-separator
          :label="relationship"
          expand-icon-class="tw-h-fit"
          header-class="tw-p-0 tw-h-8 tw-flex tw-flex-row tw-items-center tw-justify-between tw-px-1 tw-rounded-md tw-rounded-b-none tw-text-[12px]"
        >
          <div
            class="tw-flex tw-flex-col tw-gap-2 tw-rounded-md tw-rounded-t-none tw-bg-[#E8E8E8] tw-p-2"
          >
            <div class="tw-flex tw-items-center tw-justify-end">
              <q-btn
                v-if="relationship !== moduleData.descricao"
                @click="onLoadRelationships(section)"
                padding="2px 6px"
                class="tw-my-1 tw-rounded-md"
                size="xs"
                color="primary"
                flat
                dense
                no-caps
              >
                <span class="tw-text-[11px]">Buscar relacionamentos</span>
              </q-btn>
            </div>
            <div
              v-for="field in section"
              :key="field.campoRelacionamento"
              class="tw-rounded-md tw-bg-SGBRWhite tw-p-1"
            >
              <q-checkbox
                @update:model-value="handleSelection(field)"
                :model-value="currentSelectedMap.has(field.campoRelacionamento)"
                :true-value="true"
                :false-value="false"
                size="28px"
              ></q-checkbox>
              <span class="tw-text-xs tw-text-textsSGBR-gray">
                {{ field.descricao }}
              </span>
            </div>
          </div>
        </q-expansion-item>
      </div>
    </div>
  </section>

  <section
    v-else
    class="customScroll tw-mb-2 tw-flex tw-h-[200px] tw-flex-col tw-gap-4 tw-overflow-auto tw-rounded-md tw-p-1"
  >
    <div
      class="tw-flex tw-justify-center tw-rounded-md tw-bg-SGBRGrayBG tw-p-4"
    >
      <span class="tw-text-xs">Nenhum resultado encontrado.</span>
    </div>
  </section>

  <section class="tw-flex tw-justify-end tw-gap-4">
    <q-btn
      v-close-popup
      unelevated
      flat
      dense
      class="tw-rounded-md tw-bg-SGBRGrayBG tw-px-2 tw-font-medium tw-text-SGBRGray"
      padding="6px 12px"
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Esc" />

        <p class="!tw-text-inherit">Cancelar</p>
      </div>
    </q-btn>

    <q-btn
      @click="onConfirm"
      class="tw-rounded-md tw-bg-SGBRBlueLighten tw-px-2 tw-font-medium tw-text-white"
      padding="6px 12px"
      flat
      unelevated
      dense
    >
      <div class="tw-flex tw-flex-wrap tw-gap-2 !tw-text-inherit">
        <Shortkey v-if="showShortKey" text="Enter" />
        <p class="tw-inline-block !tw-text-inherit">Confirmar</p>
      </div>
    </q-btn>
  </section>
</template>

<script setup>
import notify from 'src/components/utils/notify';
import { Dialog } from 'quasar';
import { api_global } from 'src/boot/axios';
import Shortkey from 'src/components/generic/shortkey/Shortkey.vue';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys';
import ModelInputText from 'src/core/models/inputs/Text';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import handleApiErrors from 'src/services/handleApiErrors';
import { normalizeText } from 'src/services/utils';
import { useGlobal } from 'src/stores/global';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

const emits = defineEmits([
  'on-save',
  'on-cancel',
  'on-add-field',
  'on-add-extra-field',
  'on-remove-extra-field'
]);

const allFields = defineModel('all-fields', {
  type: Map,
  required: true
});

const props = defineProps({
  scope: {
    type: String,
    required: false,
    default: 'extra-fields-modal'
  },
  dataModal: {
    type: Object,
    required: false,
    default: () => ({})
  },
  select: {
    type: Object,
    required: false,
    default: () => {}
  },
  moduleData: {
    type: Object,
    required: true
  }
});

const global = useGlobal();
const showShortKey = global.showShortKey;
const searchInputRef = ref(null);
const searchString = ref('');
const isAllSelected = ref(false);
const isSelectionLoading = ref(false);
const isLoadingRelationship = ref(false);

const listRef = ref(false);

const initialSelectedMap = ref(new Map());
const currentSelectedMap = ref(new Map());

props.select.value.forEach((campo) => {
  initialSelectedMap.value.set(campo.campoRelacionamento, campo);
  currentSelectedMap.value.set(campo.campoRelacionamento, campo);
});

await nextTick();
const extraFields = computed(() => {
  const normalizedSearch = normalizeText(searchString.value);
  const matchedFields = Array.from(allFields.value.values()).filter((field) => {
    const normalizedName = normalizeText(field.nomeCampo);
    const normalizedDesc = normalizeText(field.descricao);
    const normalizedRelDesc = normalizeText(field.descricaoRelacionamento);

    return (
      normalizedName.includes(normalizedSearch) ||
      normalizedDesc.includes(normalizedSearch) ||
      normalizedRelDesc?.includes(normalizedSearch)
    );
  });

  return Object.groupBy(
    matchedFields,
    ({ descricaoRelacionamento }) =>
      descricaoRelacionamento || props.moduleData.descricao
  );
});

const handleSelection = (field) => {
  if (currentSelectedMap.value.has(field.campoRelacionamento)) {
    currentSelectedMap.value.delete(field.campoRelacionamento);
  } else {
    currentSelectedMap.value.set(field.campoRelacionamento, field);
  }
};

const onSelectAll = async (value) => {
  isSelectionLoading.value = true;

  let confirm = true;
  if (!value) {
    confirm = await openConfirmUnselectionModal();
    if (!confirm) {
      isSelectionLoading.value = false;
      return;
    }
  }

  isAllSelected.value = value;
  if (value) {
    Object.values(extraFields.value).forEach((section) => {
      section.forEach((field) => {
        currentSelectedMap.value.set(field.campoRelacionamento, field);
      });
    });
  } else {
    currentSelectedMap.value.clear();
  }

  isSelectionLoading.value = false;
};

const openConfirmUnselectionModal = async () => {
  return await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'unselect-all-extra-fields-modal',
        title: 'Desmarcar todos',
        description:
          'Ao confirmar, todos os campos selecionados serão desmarcados. Deseja prosseguir?',
        classCardSection: 'lg:tw-w-[450px]',
        dataModal: {}
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const onLoadRelationships = async (fields) => {
  const relationshipHierarchy = fields?.[0]?.relationshipHierarchy;
  const params = {
    filtersV2: [
      {
        field: 'controle',
        filterType: 'EQUALS',
        filterValue: fields?.[0]?.parentModulo
      }
    ],
    relationships: ['campos', 'relacionamentos', 'relacionamentos.campos'],
    page: 1,
    paginate: 20,
    codEmpresa: global?.company?.controle || ''
  };

  try {
    isLoadingRelationship.value = true;
    const { status, data } = await api_global.get('/api/relatorio-geral', {
      params
    });
    isLoadingRelationship.value = false;

    if (status !== 200 || !data?.data.length) return;
    getDeepRelationshipFields(data?.data?.[0], relationshipHierarchy);
  } catch (error) {
    handleApiErrors(error);
  } finally {
    isLoadingRelationship.value = false;
  }
};

async function getDeepRelationshipFields(relationship, relationshipHierarchy) {
  if (!relationship?.relacionamentos.length) {
    notify('Nenhum relacionamento encontrado');
    return;
  }

  const mainDescription = relationship?.descricao;
  for (const {
    relacionamento,
    descricao,
    campos
  } of relationship.relacionamentos) {
    if (
      !campos ||
      descricao == props.moduleData.descricao ||
      descricao == mainDescription
    )
      continue;

    for (const campo of campos) {
      const fullRelationship = `${relationshipHierarchy}.${relacionamento}.${campo.nomeCampo}`;

      allFields.value.set(fullRelationship, {
        ...campo,
        descricao: `${campo.descricao} - (${mainDescription} - ${descricao})`,
        campoRelacionamento: fullRelationship,
        relacionamento: fullRelationship,
        descricaoRelacionamento: `${mainDescription} - ${descricao}`,
        show: false,
        relationshipHierarchy: `${relationshipHierarchy}.${relacionamento}`
      });
    }
  }

  scrollToBottom();
}

const scrollToBottom = async () => {
  await nextTick();
  if (listRef.value) {
    listRef.value.scrollTo({
      top: listRef.value.scrollHeight,
      behavior: 'smooth'
    });
  }
};

const getChanges = () => {
  const selected = [];
  const unselected = [];

  initialSelectedMap.value.forEach((field) => {
    if (!currentSelectedMap.value.has(field.campoRelacionamento)) {
      unselected.push(field);
    }
  });

  currentSelectedMap.value.forEach((field) => {
    if (!initialSelectedMap.value.has(field.campoRelacionamento)) {
      selected.push(field);
    }
  });

  return {
    selected,
    unselected,
    current: [...currentSelectedMap.value.values()]
  };
};

const onConfirm = () => {
  emits('on-save', getChanges());
};

// Definir atalhos
const atalhos = [
  {
    key: 'f2',
    event: () => searchInputRef.value?.focus()
  },
  {
    key: 'enter',
    event: onConfirm
  }
];
useScopedHotkeys(atalhos, props.scope);

onMounted(() => {
  searchInputRef.value?.$el.focus();
});

// Automaticamente seleciona todos os campos se todos estiverem em initialSelectedMap.
watch(
  () => Array.from(initialSelectedMap.value).length,
  (selectedQuantity) => {
    isAllSelected.value =
      selectedQuantity === Array.from(allFields.value).length;
  },
  { immediate: true }
);
</script>
