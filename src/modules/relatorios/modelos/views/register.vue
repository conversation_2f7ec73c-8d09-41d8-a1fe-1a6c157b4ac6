<template>
  <SGRegisterPage
    ref="registerPageRef"
    id="register-report-template"
    :title="
      readonlyOrDisable
        ? 'Visualizar modelo de relatório'
        : controle
        ? 'Editar modelo de relatório'
        : 'Cadastro de modelo de relatório'
    "
    :focus-on-mounted="false"
    :buttons="actionBarButtons"
    :disable-warning-animation="isWarningAnimationDisabled"
    :readonly-or-disable="readonlyOrDisable"
    wrapper-class="!tw-pb-24"
    hide-side-nav
    beta
  >
    <SGCard
      id="informacoes-modelo"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      has-title-separator
      :has-circle="!readonlyOrDisable"
      remove-gap
      title="Informações do modelo"
    >
      <q-input
        v-model="fields.nome.value"
        :error="!!fields.nome.errorMessage"
        :error-message="fields.nome.errorMessage"
        :valid="fields.nome.meta.valid"
        :readonly="readonlyOrDisable"
        v-bind="ModelInputText"
        label="Nome do modelo"
        class="tw-col-span-6"
        required
      />

      <InputSelect
        ref="moduloSelectRef"
        @selected="handleModuloChange"
        @updated-raw="processFields"
        :model-value="fields.modulo.value"
        :error="!!fields.modulo.errorMessage"
        :error-message="fields.modulo.errorMessage"
        :valid="fields.modulo.meta.valid"
        :readonly="readonlyOrDisable"
        v-bind="{
          ...ModelInputSelectSearch,
          params: {
            apiRoute: '/api/relatorio-geral',
            central: true,
            filterOr: ['controle', 'descricao'],
            filtersV2: [
              {
                field: 'principal',
                filterType: 'EQUALS',
                filterValue: true
              }
            ],
            relationships: [
              'campos',
              'relacionamentos',
              'relacionamentos.campos'
            ],
            extraParameters: { codEmpresa: global?.company?.controle || '' }
          }
        }"
        :clearable="false"
        hint="Ao selecionar, todos os campos serão perdidos e substituídos pelos campos do módulo selecionado."
        class="tw-col-span-6 md:tw-mb-4 lg:tw-mb-1"
        label="Módulo"
        required
      />

      <q-input
        v-model="fields.descricao.value"
        :error="!!fields.descricao.errorMessage"
        :error-message="fields.descricao.errorMessage"
        :valid="fields.descricao.meta.valid"
        v-bind="ModelInputText"
        type="textarea"
        placeholder="Descrição do relatório"
        outlined
        label="Descrição"
        autogrow
        class="tw-col-span-6"
        maxlength="255"
        dense
        stack-label
        input-class="tw-text-SGBRGray tw-p-0"
        input-style="min-height: 3rem"
      />

      <div class="tw-col-span-6 md:tw-mt-1">
        <q-checkbox
          v-model="fields.cabecalho.value"
          :valid="fields.cabecalho.meta.valid"
          label="Incluir cabeçalho com informações da empresa"
          v-bind="ModelInputText"
          size="30px"
          class="tw-col-span-4"
        />
        <div class="tw-flex tw-items-center tw-gap-1 md:tw-mb-2 md:tw-mt-0">
          <span class="tw-font-medium tw-text-textsSGBR-gray">Orientação:</span>
          <q-option-group
            v-model="fields.orientacao.value"
            :options="ORIENTATION_TYPES"
            :valid="fields.orientacao.meta.valid"
            class="tw-flex tw-flex-row tw-gap-0"
            size="30px"
            inline
            dense
          />
        </div>
      </div>
    </SGCard>

    <SGCard
      id="report-template-fields"
      v-if="fields.modulo.value"
      title="Campos e filtros"
      :error-keys="['select']"
      :schema-errors="errors"
      :meta="meta"
      :cols="12"
      has-title-separator
      remove-gap
    >
      <template #buttons>
        <q-btn
          @click="showHelperModal"
          class="tw-mb-2 !tw-h-[30px] !tw-min-h-[30px]"
          color="primary"
        >
          Instruções
          <TooltipCustom text-tooltip="Guia para geração de relatórios" />
        </q-btn>
      </template>

      <ReportTemplate
        ref="reportTemplateRef"
        v-model:all-fields="allModuleFields"
        v-model:schema="fields"
        :schema-values="values"
        :module-data="moduleData"
        :is-loading="isLoading"
        :errors="errors"
      />
    </SGCard>
  </SGRegisterPage>
</template>

<script setup>
import RegisterUndo from 'components/modal/global/RegisterUndo.vue';
import { diffObjects } from 'components/utils/tests';
import { Dialog, useQuasar } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import notify from 'src/components/utils/notify';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputText from 'src/core/models/inputs/Text';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import HelperModal from 'src/modules/relatorios/modelos/components/modal/HelperModal.vue';
import ReportTemplate from 'src/modules/relatorios/modelos/components/ReportTemplate.vue';
import { useFormSchema } from 'src/modules/relatorios/modelos/model/useFormSchema';
import { useReportTemplateStore } from 'src/modules/relatorios/modelos/store/useReportTemplateStore';
import { ref, toRaw, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useGenerateReport } from '../composables/useGenerateReport';
import useReportRegisterActionBar from '../composables/useReportRegisterActionBar';
import { useGlobal } from 'src/stores/global';

const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  },
  data: {
    type: [Object, null],
    default: () => null,
    required: false
  },
  isGeneratingReport: {
    type: [Boolean, null],
    required: false,
    default: false
  }
});

const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const global = useGlobal();
// Stores
const reportStore = useReportTemplateStore();

// Refs
const isLoading = ref(false);
const isWarningAnimationDisabled = ref(true);
const readonlyOrDisable = ref(route.path.includes('visualizar'));
const registerPageRef = ref();
const moduloSelectRef = ref();
const moduleData = ref({});
const moduleFields = ref([]);
const relationshipsFields = ref([]);
const allModuleFields = ref(new Map());
const reportTemplateRef = ref();

const ORIENTATION_TYPES = [
  { label: 'Retrato', value: 'R' },
  { label: 'Paisagem', value: 'P' }
];

let controle =
  props?.data?.controle ??
  route?.params?.controle ??
  $q.sessionStorage.getItem('cloneId')?.id;

let editValues = false;

async function handleGetPreview() {
  const { generateReport, getReportParams } = useGenerateReport(true);

  const schemaValues = toRaw(values);

  clearSchemaEmptyFields(schemaValues);

  const orderBy = formatOrderByToObject(schemaValues.orderBy);

  let params = await getReportParams({
    report: { ...values, orderBy, format: 'PDF' },
    isCentral: false
  });
  await generateReport({ params, router });
}

async function moduleSelected(module) {
  const mainFields = processFields(module);

  if (mainFields) {
    select.setValue(mainFields);
    fields.value.filtersV2.setValue([]);
    fields.value.operation.setValue([]);
    fields.value.dynamicFiltersV2.setValue([]);
    fields.value.orderBy.setValue([]);
  }
}

const showHelperModal = async () => {
  return await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        componentRef: HelperModal,
        scope: 'report-helper-modal',
        title: 'Instruções',
        classCardSection: 'lg:tw-w-[600px]',
        hasCancel: false,
        hasSave: false,
        dataModal: {}
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const handleModuloChange = async (module) => {
  if (!module) return;

  const previousModulo = fields.value.modulo.value;
  const previousModel = fields.value.model.value;

  const shouldAskConfirmation =
    previousModulo !== null && module.controle !== previousModulo;

  const confirmed = shouldAskConfirmation
    ? await openModuleChangeConfirmationModal()
    : true;

  if (confirmed) {
    fields.value.modulo.setValue(module.controle);
    fields.value.model.setValue(module.model);
    moduleSelected(module);
  } else {
    fields.value.model.setValue(previousModel);
    fields.value.modulo.setValue(previousModulo); // Att schema.
    moduloSelectRef.value?.setModel(previousModulo); // Att o select.
  }

  reportTemplateRef.value?.searchInputRef?.focus();
};

const openModuleChangeConfirmationModal = async () => {
  return await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        scope: 'change-module-confirmation',
        title: 'Mudança de módulo',
        description:
          'Todos os campos, filtros e totalizadores serão perdidos. Deseja continuar?',
        classCardSection: 'lg:tw-w-[450px]',
        dataModal: {}
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

const processFields = (module) => {
  if (!module || !module?.campos || !module?.relacionamentos) {
    moduleData.value = null;
    moduleFields.value = [];
    relationshipsFields.value = [];
    allModuleFields.value.clear();
    return;
  }

  allModuleFields.value.clear();
  isLoading.value = true;
  moduleData.value = module;

  const mainFields = module.campos.map((campo) => ({
    ...campo,
    campoRelacionamento: campo.nomeCampo,
    operation: '',
    isCustom: false,
    show: false
  }));

  mainFields.forEach((field) => {
    allModuleFields.value.set(field.campoRelacionamento, field);
  });

  const relationFields = module.relacionamentos.flatMap(
    (rel) =>
      rel.campos?.map((campo) => {
        const fullPath = `${rel.relacionamento}.${campo.nomeCampo}`;
        const field = {
          ...campo,
          campoRelacionamento: fullPath,
          relacionamento: fullPath,
          descricaoRelacionamento: rel.descricao,
          show: false,
          relationshipHierarchy: rel.relacionamento
        };
        allModuleFields.value.set(fullPath, field);
        return field;
      }) ?? []
  );

  relationshipsFields.value = relationFields;
  isLoading.value = false;
  return mainFields;
};

function showErrorMessages(errors) {
  if ('filtersV2' in errors) {
    notify('Preencha os filtros fixos corretamente antes de prosseguir');
    return;
  }

  if ('select' in errors) {
    notify('Adicione ao menos um campo para geração do relatório.');
    return;
  }

  if ('dynamicFiltersV2' in errors) {
    notify('Preencha os filtros dinâmicos corretamente antes de prosseguir');
    return;
  }

  if ('operation' in errors) {
    notify('Preencha os totalizadores corretamente antes de prosseguir');
  }

  if ('group' in errors) {
    notify('Agrupamentos: ' + errors.group);
  }
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, {
    context: values
  });

  if (!validation.valid) {
    registerPageRef?.value?.scrollToError(validation);
    showErrorMessages(validation.errors);
  }
  isSubmitting.value = false;

  return validation.valid;
}

const clearSchemaEmptyFields = (schema) => {
  const fieldsToValidate = [
    'filtersV2',
    'dynamicFiltersV2',
    'operation',
    'group',
    'orderBy'
  ];

  for (const field of fieldsToValidate) {
    if (schema[field].length === 1) {
      const fieldIsEmpty = Object.values(schema[field][0]).every(
        (item) => !item
      );

      if (fieldIsEmpty) {
        fields.value[field].setValue([]);
      }
    }
  }
};

async function save() {
  const schemaValues = toRaw(values);

  clearSchemaEmptyFields(schemaValues);

  if (!(await isFormValid(registerPageRef))) return;

  let response;

  schemaValues.orderBy = formatOrderByToObject(schemaValues.orderBy);

  let payload = {
    nome: schemaValues.nome,
    model: schemaValues.model,
    modulo: schemaValues.modulo,
    cabecalho: schemaValues.cabecalho,
    orientacao: schemaValues.orientacao,
    filtros: { ...schemaValues }
  };

  if (controle) {
    response = await reportStore.put({ payload, controle });
  } else {
    response = await reportStore.post({ payload });
  }

  if (response.success) cancel();
}

function reset() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(async () => {
    await handleReset();

    if (!values.modulo) {
      moduleData.value = {};
      moduleFields.value = [];
      relationshipsFields.value = [];
      allModuleFields.value.clear();
    }
  });
}

async function cancel() {
  isWarningAnimationDisabled.value = true;

  if (props?.modal) emit('cancel');
  else await router.push('/relatorios/modelos');
}

function formatOrderByToArray(orderBy) {
  if (!orderBy || typeof orderBy !== 'object') return [];

  return Object.entries(orderBy).map(([field, order]) => ({
    field,
    order
  }));
}

function formatOrderByToObject(orderBy) {
  if (!orderBy || !Array.isArray(orderBy)) return {};

  return orderBy.reduce((arr, field) => {
    arr[field.field] = field.order;
    return arr;
  }, {});
}

async function onCreated() {
  if (controle) {
    const isDuplicating = $q.sessionStorage.getItem('cloneId');

    const isDuplicatingFromCentral = isDuplicating?.central;

    const store = reportStore;

    const { success, data } = await store.get({
      controle,
      duplicate: isDuplicatingFromCentral ? isDuplicating?.id : false
    });

    if (!success) cancel();
    const { filtros, ...raw } = data?.rowsData ?? {};

    editValues = {
      ...raw,
      ...filtros,
      modulo: Number(data?.rowsData?.modulo),
      orderBy: formatOrderByToArray(filtros?.orderBy)
    };

    if (isDuplicating?.id) {
      controle = null;
      editValues.controle = isDuplicating.id ? null : data?.rowsData.controle;
      $q.sessionStorage.setItem('cloneId', false);
    }
  }
}

await onCreated();
const {
  values,
  initialValues,
  handleReset,
  errors,
  fields,
  meta,
  isSubmitting,
  validate
} = useFormSchema(editValues);

const { select } = fields.value;

// Desfazer alterações.
watch(
  values,
  async () => {
    if (
      (Number(localStorage?.disableUndoEdit) && controle) ||
      (Number(localStorage?.disableUndoCreate) && !controle)
    ) {
      isWarningAnimationDisabled.value = true;
    } else {
      isWarningAnimationDisabled.value = !Object.keys(
        diffObjects(initialValues.value, values)
      ).length;
    }
  },
  {
    immediate: true,
    deep: true
  }
);

const actionBarEvents = {
  save: {
    callback: save
  },
  // saveAndGenerate: {
  //   callback: () => save(true)
  // },
  preview: {
    condition: () =>
      global.roles.includes('RELATORIO.MODELO:PRE-VISUALIZAR') &&
      fields.value.select.value.filter((field) => field.show).length > 0,
    callback: handleGetPreview
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useReportRegisterActionBar({
  params: {
    modal: props.modal,
    isWarningAnimationDisabled
  },
  events: actionBarEvents
});
</script>
