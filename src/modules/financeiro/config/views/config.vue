<template>
  <div>
    <SGRegisterSkeleton v-if="isLoadingPage" />

    <SGRegisterPage
      v-else
      ref="registerPageRef"
      id="sessao-emissao-pdv"
      title="Configurações do financeiro"
      :buttons="actionBarButtons"
      :modal="modal"
      :disable-warning-animation="isWarningAnimationDisabled"
    >
      <section class="tw-col-span-full">
        <q-tabs
          v-model="tab"
          dense
          class="tw-col-span-full tw-rounded-t-md tw-text-textsSGBR-gray"
          align="left"
          narrow-indicator
          active-color="primary"
          indicator-color="primary"
        >
          <q-tab
            name="user"
            label="Usuário"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
          />

          <q-tab
            name="company"
            label="Empresa"
            class="tw-p-0 tw-text-sm"
            content-class="tw-py-0 tw-px-4"
            :disable="
              !global?.roles?.includes('FINANCEIRO.RECEBER:CONFIGURACAO') ||
              !global?.roles?.includes('FINANCEIRO.BANCO:CONFIGURACAO') ||
              !global?.roles?.includes('FINANCEIRO.CAIXA:CONFIGURACAO')
            "
          >
            <TooltipCustom
              v-if="
                !global?.roles?.includes('FINANCEIRO.RECEBER:CONFIGURACAO') ||
                !global?.roles?.includes('FINANCEIRO.BANCO:CONFIGURACAO') ||
                !global?.roles?.includes('FINANCEIRO.CAIXA:CONFIGURACAO')
              "
              text-tooltip="Você não possui permissão para alterar as
            configurações da empresa"
            />
          </q-tab>
        </q-tabs>

        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tab"
          animated
          class="tw-col-span-full tw-bg-inherit"
          transition-prev="jump-right"
          transition-next="jump-left"
          :transition-duration="120"
          @transition="tabChange"
        >
          <q-tab-panel
            name="user"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <SGCard
              title="Configurações do usuário
"
              id="sessao-user-general"
              :cols="8"
              :schema-errors="errors"
              :meta="meta"
              remove-gap
              has-title-separator
              has-circle
            >
              <div class="tw-col-span-full tw-my-1 max-md:tw-mb-4">
                <p class="tw-text-textsSGBR-gray">Tipo impressão</p>
                <q-option-group
                  v-model="fields.usuario.tipoImpressao.value"
                  :error="!!fields.usuario.tipoImpressao.errorMessage"
                  :error-message="fields.usuario.tipoImpressao.errorMessage"
                  :valid="fields.usuario.tipoImpressao.meta.valid"
                  :options="typesPrint"
                  color="primary"
                  size="30px"
                  inline
                  class="tw-mb-2 tw-flex tw-flex-wrap tw-gap-x-4 tw-gap-y-2 max-md:tw-flex-col"
                />
              </div>

              <Logotipo
                v-model:fields="fields.usuario"
                :has-default="true"
                class="tw-col-span-full"
              />

              <div
                class="tw-col-span-full tw-mt-4 tw-flex tw-flex-wrap tw-gap-customGridGapCards"
              >
                <Margins
                  title="Margens impressão (A4)"
                  v-model:fields="fields.usuario"
                  :has-margin-default="true"
                  type="A4"
                />

                <Margins
                  title="Margens impressão térmica (80mm)"
                  v-model:fields="fields.usuario"
                  :has-margin-default="true"
                />

                <Margins
                  title="Margens impressão térmica (58mm)"
                  v-model:fields="fields.usuario"
                  :has-margin-default="true"
                  type="58"
                />
              </div>
            </SGCard>

            <SGCard
              title="Impressão rápida"
              id="fast-print-integration"
              :schema-errors="errors"
              :error-keys="['usuario.impressaoRapida.impressora']"
              :meta="meta"
              :cols="8"
              remove-gap
              has-circle
              has-title-separator
              v-if="isCaixaRoute || isReceiveRoute"
            >
              <q-checkbox
                @update:model-value="handleEnableFastPrint"
                v-model="
                  fields.usuario.impressaoRapida.habilitarImpressaoRapida.value
                "
                :error="
                  !!fields.usuario.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :error-message="
                  fields.usuario.impressaoRapida.habilitarImpressaoRapida
                    .errorMessage
                "
                :valid="
                  fields.usuario.impressaoRapida.habilitarImpressaoRapida.meta
                    .valid
                "
                size="2rem"
                label="Habilitar impressão rápida"
                class="tw-col-span-full"
              />

              <q-checkbox
                v-model="fields.usuario.impressaoRapida.usarPadraoUsuario.value"
                :error="
                  !!fields.usuario.impressaoRapida.usarPadraoUsuario
                    .errorMessage
                "
                :error-message="
                  fields.usuario.impressaoRapida.usarPadraoUsuario.errorMessage
                "
                :valid="
                  fields.usuario.impressaoRapida.usarPadraoUsuario.meta.valid
                "
                :disable="
                  !fields.usuario.impressaoRapida.habilitarImpressaoRapida.value
                "
                size="2rem"
                label="Usar impressão rápida padrão do usuário"
                class="tw-col-span-full tw-mb-4"
              />

              <div
                class="tw-col-span-full tw-flex tw-flex-col tw-gap-4"
                :class="{
                  'tw-pointer-events-none tw-opacity-40':
                    !fields.usuario.impressaoRapida.habilitarImpressaoRapida
                      .value ||
                    fields.usuario.impressaoRapida.usarPadraoUsuario.value
                }"
              >
                <FastPrintSteps
                  v-model:fields="fields.usuario.impressaoRapida"
                  :is-fast-print-enabled="
                    fields.usuario.impressaoRapida.habilitarImpressaoRapida
                      .value &&
                    !fields.usuario.impressaoRapida.usarPadraoUsuario.value
                  "
                />
              </div>
            </SGCard>
          </q-tab-panel>
          <q-tab-panel
            name="company"
            class="tw-flex tw-flex-col tw-gap-6 tw-rounded-md tw-bg-inherit"
          >
            <SGCard
              title="Gerais"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              id="sessao-company-general"
              has-title-separator
              has-circle
              container-class="tw-gap-y-1"
              v-if="isReceiveRoute"
            >
              <q-checkbox
                v-if="
                  global?.roles?.includes('FINANCEIRO.RECEBER:CONFIGURACAO') &&
                  isReceiveRoute
                "
                v-model="fields.mostrarValorFaltanteRecibo.value"
                :error="!!fields.mostrarValorFaltanteRecibo.errorMessage"
                :error-message="fields.mostrarValorFaltanteRecibo.errorMessage"
                :valid="fields.mostrarValorFaltanteRecibo.meta.valid"
                label="Mostrar valor financeiro das parcelas em aberto do cliente na impressão"
                class="tw-col-span-4"
                size="2rem"
              />
            </SGCard>
            <SGCard
              id="sessao-financeiro"
              title="Financeiro"
              :schema-errors="errors"
              :meta="meta"
              :cols="8"
              remove-gap
              has-title-separator
              has-circle
              v-if="!isReceiveRoute"
            >
              <InputSelect
                v-model="fields.financeiro.codPlanoConta.value"
                :error="!!fields.financeiro.codPlanoConta.errorMessage"
                :error-message="fields.financeiro.codPlanoConta.errorMessage"
                :valid="fields.financeiro.codPlanoConta.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/plano-conta',
                    filterOr: ['controle', 'descricao'],
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                role-prefix="CADASTRO.PLANO-CONTA"
                class="tw-col-span-4"
                label="Plano de conta"
                required
                add-button
                @on-add="openRegisterPlanoConta"
              />
              <CostCenterRegisterModal
                v-if="costCenterRegisterModalModel"
                @update:model-modal="
                  (newValue) => (costCenterRegisterModalModel = newValue)
                "
                @on-save="updateCostCenterOptions"
                @on-cancel="costCenterRegisterModalModel = false"
                :model-value="costCenterRegisterModalModel"
              />
              <InputSelect
                v-model="fields.financeiro.codCentroCusto.value"
                :error="!!fields.financeiro.codCentroCusto.errorMessage"
                :error-message="fields.financeiro.codCentroCusto.errorMessage"
                :valid="fields.financeiro.codCentroCusto.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/centro/custo',
                    filterOr: ['controle', 'descricao'],
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                role-prefix="CADASTRO.CENTRO-CUSTO"
                class="tw-col-span-4"
                label="Centro de custo"
                required
                add-button
                @on-add="() => (costCenterRegisterModalModel = true)"
              />

              <InputSelect
                v-model="fields.financeiro.codEspecie.value"
                :error="!!fields.financeiro.codEspecie.errorMessage"
                :error-message="fields.financeiro.codEspecie.errorMessage"
                :valid="fields.financeiro.codEspecie.meta.valid"
                v-bind="{
                  ...ModelInputSelectSearch,
                  params: {
                    apiRoute: '/api/especie',
                    filterOr: ['controle', 'descricao'],
                    orderBy: {
                      controle: 'asc'
                    }
                  }
                }"
                :clearable="false"
                option-label="descricaoCodigo"
                option-value="controle"
                role-prefix="CADASTRO.ESPECIE"
                class="tw-col-span-4"
                label="Espécie"
                required
                add-button
                @on-add="openRegisterEspecies"
              />
            </SGCard>

            <SGCard
              title="WhatsApp"
              id="whatsapp-config"
              :cols="12"
              :schema-errors="errors"
              remove-gap
              has-circle
              has-title-separator
            >
              <WhatsApp
                v-model:fields="fields"
                :errors="errors"
                :show-default="true"
                :config-type="3"
                :show-send-message-button="true"
              />
            </SGCard>
          </q-tab-panel>
        </q-tab-panels>
      </section>
    </SGRegisterPage>
  </div>
</template>

<script setup>
import { useQuasar } from 'quasar';
import useRegister from 'src/components/actionBar/composables/useRegister';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import RegisterUndo from 'src/components/modal/global/RegisterUndo.vue';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import Logotipo from 'src/core/components/config/Logotipo.vue';
import Margins from 'src/core/components/config/Margins.vue';

import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import SGRegisterSkeleton from 'src/core/components/SG/Skeleton/SGRegisterSkeleton.vue';
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';
import { default as CostCenterRegisterModal } from 'src/modules/cadastros/centro-custo/views/register.vue';
import { default as EspecieRegisterModal } from 'src/modules/cadastros/especies/views/register.vue';
import { default as PlanoContaRegisterModal } from 'src/modules/cadastros/plano-contas/views/register.vue';
import { useUserConfigStore } from 'src/modules/navbarUsuario/usuario/config/store/useUserConfigStore.js';

import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import FastPrintSteps from 'src/modules/navbarUsuario/usuario/config/components/FastPrintSteps.vue';
import { useGlobal } from 'src/stores/global';
import { ref, toRaw, toRefs, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useFormSchemaConfig } from '../models/useFormSchemaConfig';
import { useFinancialConfigStore } from '../store/useFinancialConfigStore';
import { useFinancialReceiveConfigStore } from '../store/useFinancialReceive';
const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const { modal } = toRefs(props);
let editValues = false;
const global = useGlobal();
const $q = useQuasar();
const router = useRouter();
const registerPageRef = ref();
const isWarningAnimationDisabled = ref(true);
const tab = ref('user');

// Stores
const userConfigStore = useUserConfigStore();
const financeiroConfigStore = useFinancialConfigStore();
const financeiroReceberConfigStore = useFinancialReceiveConfigStore();
financeiroReceberConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  }
};
financeiroConfigStore.table.filters = {
  deleted_at: {
    filterType: ''
  }
};

// Ref
const route = useRoute();
const costCenterRegisterModalModel = ref();

const isReceiveRoute = route.fullPath.includes('receber');
const isCaixaRoute = route.fullPath.includes('caixa');
const isLoadingPage = ref(true);
const costCenterInputRef = ref();
const planoContaInputRef = ref();
const especiesInputRef = ref();

const typesPrint = [
  { label: 'A4', value: '1' },
  { label: 'A4 resumida', value: '2' },
  { label: 'Térmica (80mm)', value: '3' },
  { label: 'Térmica (58mm)', value: '4' }
];

async function getReceberConfig() {
  if (!global?.roles?.includes('FINANCEIRO.RECEBER:CONFIGURACAO')) {
    return { success: true, data: { rowsData: [] } };
  }

  const promiseRecber = financeiroReceberConfigStore.get();
  return promiseRecber;
}
async function getFinanceiroConfig() {
  if (
    !global?.roles?.includes('FINANCEIRO.BANCO:CONFIGURACAO') ||
    !global?.roles?.includes('FINANCEIRO.CAIXA:CONFIGURACAO')
  ) {
    return { success: true, data: { rowsData: [] } };
  }

  const promiseRecber = financeiroConfigStore.get();
  return promiseRecber;
}

async function getConfig() {
  isLoadingPage.value = true;

  // Requisições em paralelo para buscar configuracoes (user, pdv e email)
  const [userConfigPromise, receberConfigPromise, financeiroConfigPromise] =
    await Promise.all([
      userConfigStore.get({ deletedAt: false }),
      getReceberConfig(),
      getFinanceiroConfig()
    ]);

  isLoadingPage.value = false;

  const { success: userSuccess, data: userConfigData } = userConfigPromise;

  const { success: receberSuccess, data: receberConfigData } =
    receberConfigPromise;
  const { data: finaceiroConfigData } = financeiroConfigPromise;

  if (!userSuccess || !receberSuccess) cancel();

  const { rowsData: userConfigs } = userConfigData;
  const userConfig = userConfigs?.[0] ?? {};
  const { impressaoRapida } = isReceiveRoute
    ? userConfig.financeiro ?? {}
    : userConfig.caixa ?? {};
  // const { impressaoRapida } = userConfig.usuario ?? {};

  const { rowsData: configsReceber } = receberConfigData ?? {};
  const configReceber = configsReceber?.[0] ?? {};

  const { rowsData: configsFinaceiro } = finaceiroConfigData ?? {};
  const configFinanceiro = configsFinaceiro?.[0] ?? {};

  editValues = {
    ...configReceber,
    usuario: {
      ...(isCaixaRoute ? userConfig.caixa : userConfig.financeiro),
      createdAt: userConfig?.createdAt,
      impressaoRapida: {
        ...impressaoRapida,
        modulo: isReceiveRoute ? 5 : 6,
        habilitarImpressaoRapida:
          impressaoRapida?.habilitarImpressaoRapida ?? false,
        usarPadraoUsuario: impressaoRapida?.usarPadraoUsuario ?? true
      }
    },
    mostrarValorFaltanteRecibo: configReceber.mostrarvalorfaltanterecibo,
    financeiro: {
      controle: configFinanceiro.controle,
      codCentroCusto: configFinanceiro.codCentroCusto,
      codPlanoConta: configFinanceiro.codPlanoConta,
      codEspecie: configFinanceiro.codEspecie
    }
  };
}

await getConfig();
const {
  fields,
  values,
  initialValues,
  setFieldValue,
  errors,
  meta,
  isSubmitting,
  validate,
  handleReset
} = useFormSchemaConfig(editValues);

async function tabChange() {
  setTimeout(() => {
    registerPageRef.value.reMountCards('.q-tab-panel');
  }, 255);
}
function updateCostCenterOptions() {
  costCenterRegisterModalModel.value = false;
  costCenterInputRef.value?.reMount();
}
function openRegisterPlanoConta() {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: PlanoContaRegisterModal,
      scope: 'register-plano-conta'
    }
  }).onOk(async () => {
    await planoContaInputRef.value?.reMount();
  });
}
function openRegisterEspecies() {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: EspecieRegisterModal,
      scope: 'register-especies'
    }
  }).onOk(async () => {
    await especiesInputRef.value?.reMount();
  });
}

function resetForm() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    handleReset();
  });
}

function handleEnableFastPrint(enableFastPrint) {
  if (!enableFastPrint) {
    setFieldValue('usuario.impressaoRapida.usarPadraoUsuario', true);
  }
}
function cancel() {
  isWarningAnimationDisabled.value = true;
  const fullPath = router.currentRoute.value.fullPath;
  const redirectRouter = fullPath.replace('/configuracoes', '');

  router.push(redirectRouter).catch(() => router.push('/financeiro/pagar'));
  emit('cancel');
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

async function save() {
  if (!(await isFormValid())) return;

  let payload = toRaw(values);

  const userPayload = isReceiveRoute
    ? {
        createdAt: payload.usuario?.createdAt,
        financeiro: {
          ...payload.usuario
        },
        impressaoRapida: {
          ...payload.usuario?.impressaoRapida
        }
      }
    : isCaixaRoute
    ? {
        createdAt: payload.usuario?.createdAt,
        caixa: {
          ...payload.usuario
        },
        impressaoRapida: {
          ...payload.usuario?.impressaoRapida
        }
      }
    : {
        createdAt: payload.usuario?.createdAt,
        financeiro: {
          ...payload.usuario
        }
      };

  delete payload.usuario;
  delete userPayload?.codUsuario;

  // Verifica se já existe um registro de configurações salva, se sim, atualiza, caso contrário, cria.

  const promises = [sendUserConfigRequest(userPayload)];

  if (isReceiveRoute) {
    promises.push(sendFinaceiroReceberConfigRequest(payload));
  } else {
    promises.push(sendFinaceiroConfigRequest(payload.financeiro));
  }

  const [userPromise, financialPromise] = await Promise.all(promises);

  if (financialPromise.success && userPromise.success) {
    notify('Dados salvos com sucesso!', 'positive');
    cancel();
  }
}

async function sendFinaceiroReceberConfigRequest(payload) {
  if (
    !global?.roles?.includes('FINANCEIRO.RECEBER:CONFIGURACAO') &&
    isReceiveRoute
  )
    return { success: true };

  return financeiroReceberConfigStore.put({
    payload: { mostrarValorFaltanteRecibo: payload.mostrarValorFaltanteRecibo },
    controle: payload.controle,
    silence: true
  });
}
async function sendFinaceiroConfigRequest(payload) {
  if (
    !global?.roles?.includes('FINANCEIRO.CAIXA:CONFIGURACAO') ||
    !global?.roles?.includes('FINANCEIRO.BANCO:CONFIGURACAO')
  )
    return { success: true };

  if (initialValues.financeiro?.controle) {
    return financeiroConfigStore.put({
      payload: payload,
      controle: payload.controle,
      silence: true
    });
  } else {
    return financeiroConfigStore.post({ payload });
  }
}
async function sendUserConfigRequest(userPayload) {
  if (userPayload?.createdAt) {
    return userConfigStore.put({
      payload: userPayload,
      silence: true
    });
  } else {
    return userConfigStore.post({
      payload: userPayload,
      silence: true
    });
  }
}

// Desfazer alterações.
watch(
  values,
  async () => {
    if (Number(localStorage?.disableUndoEdit)) {
      isWarningAnimationDisabled.value = true;
    } else {
      isWarningAnimationDisabled.value = !Object.keys(
        diffObjects(initialValues.value, values)
      ).length;
    }
  },
  {
    immediate: true,
    deep: true
  }
);

// Objeto que mapeia os eventos do actionBar aos seus respectivos callbacks.
const actionBarEvents = {
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: resetForm
  }
};

// Instancia o composable useRegister para utilizar a actionbar.
const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});
</script>
