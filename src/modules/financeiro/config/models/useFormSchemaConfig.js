import { toTypedSchema } from '@vee-validate/yup';
import { useField, useFieldArray, useForm } from 'vee-validate';
import { ref } from 'vue';
import { boolean, number, object, string } from 'yup';

/**
 * Retorna os objetos de formulários vinculados com suas configurações de validação.
 * @returns {Object} Um objeto contendo os formulários vinculados e funções auxiliares.
 */
export function useFormSchemaConfig(initialValues = false) {
  const schema = object({
    usuario: object({
      createdAt: string().default('').nullable(),

      tipoImpressao: string().default('1').nullable(),

      logotipoPadrao: boolean().default(true).nullable(),
      logotipoImprimir: boolean().default(true).nullable(),

      margemBaixo: number().default(0).nullable(),
      margemEsquerda: number().default(0).nullable(),
      margemDireita: number().default(0).nullable(),
      margemTopo: number().default(0).nullable(),
      margemPadrao: boolean().default(true).nullable(),

      margemBaixoA4: number().default(0).nullable(),
      margemEsquerdaA4: number().default(0).nullable(),
      margemDireitaA4: number().default(0).nullable(),
      margemTopoA4: number().default(0).nullable(),
      margemPadraoA4: boolean().default(true).nullable(),

      margemBaixo58: number().default(0).nullable(),
      margemEsquerda58: number().default(0).nullable(),
      margemDireita58: number().default(0).nullable(),
      margemTopo58: number().default(0).nullable(),
      margemPadrao58: boolean().default(true).nullable(),
      impressaoRapida: object({
        modulo: number().default(1), // 1 - PDV
        habilitarImpressaoRapida: boolean().default(false),
        usarPadraoUsuario: boolean().default(true),
        ipConexaoRede: string()
          .default('')
          .nullable()
          .transform((value) => (value === null ? '' : value)),
        impressora: string()
          .default('')
          .when(
            ['usarPadraoUsuario', 'habilitarImpressaoRapida'],
            ([usarPadraoUsuario, habilitarImpressaoRapida], schema) => {
              return !usarPadraoUsuario && habilitarImpressaoRapida
                ? schema.required('Obrigatório selecionar uma impressora')
                : schema.nullable();
            }
          )
      })
    }),

    mostrarValorFaltanteRecibo: boolean().default(false).nullable(),
    padraoWhatsApp: boolean().default(false).nullable(),
    financeiro: object({
      controle: string().default(null).nullable(),
      codCentroCusto: string().default(1).nullable(),
      codPlanoConta: string().default(1).nullable(),
      codEspecie: string().default(1).nullable()
    })
  });

  // Configura os valores do formulário e suas validações.
  const form = useForm({
    validationSchema: toTypedSchema(schema),
    validateOnMount: false,
    initialValues: initialValues
      ? Object.keys(initialValues).length === 1
        ? initialValues
        : schema
            .json()
            .cast(initialValues, { assert: false, stripUnknown: true })
      : undefined,
    initialErrors: {}
  });

  // Função recursiva para criar campos dinamicamente
  function createFields(schema, parentKey = '') {
    const fields = {};
    Object.keys(schema.fields).forEach((key) => {
      const fieldKey = parentKey ? `${parentKey}.${key}` : key;
      const fieldSchema = schema.fields[key];
      if (fieldSchema.type === 'object') {
        fields[key] = createFields(fieldSchema, fieldKey);
      } else if (fieldSchema.type === 'array' && fieldSchema.schema) {
        fields[key] = useFieldArray(fieldKey);
      } else {
        fields[key] = useField(fieldKey);
      }
    });
    return fields;
  }

  const fields = ref(createFields(schema));
  return { ...form, fields, useField };
}
