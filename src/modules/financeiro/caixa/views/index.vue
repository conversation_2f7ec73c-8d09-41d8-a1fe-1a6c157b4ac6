<template>
  <SGPage
    id="box-page"
    :title="
      isBankPage
        ? `Caixa da ${global?.bankSelected?.descricao ?? 'conta bancária'}`
        : 'Caixa'
    "
    desc="extratos"
    :key="isBankPage"
  >
    <!-- SLOT DE DESCRICAO -->
    <template #description>
      <div>
        <SGConfigButton
          route="/financeiro/caixa/configuracoes"
          text-tooltip="Abrir configurações do financeiro"
        />
      </div>
    </template>
    <!-- FIM SLOT DE DESCRICAO -->

    <ReceiptModal
      @on-save="handlePrintComprovante"
      @on-close="() => (modalRef = false)"
      :selected="tableSelectedItems"
      v-if="modalRef"
      v-model:model-modal="modalRef"
    />
    <template #beforeTitle>
      <q-btn
        v-if="$route.path.includes('contas/caixa')"
        unelevated
        icon="keyboard_return"
        dense
        to="/financeiro/contas"
      >
        <span
          class="text-white tw-ml-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-p-px tw-px-2 tw-text-[0.8em] tw-uppercase"
          v-if="showShortKey"
          >ESC
        </span>
      </q-btn>
    </template>

    <template #buttons>
      <SGButton
        v-authorized="'RELATORIO.MODELO:GERAR'"
        class="refactInput"
        @click="handleGerarRelatoriosModal"
        atalho="alt+r"
        >Relatórios</SGButton
      >

      <q-btn
        v-authorized="'FINANCEIRO.CAIXA:TRANSFERIR-SALDO'"
        flat
        dense
        unelevated
        class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
        @click="openTransferModal"
      >
        <Shortkey
          class="tw-mr-2"
          v-if="showShortKey"
          :text="`${altOrOption} + t`"
        />
        TRANSFERÊNCIA
      </q-btn>
      <SGRegisterButton
        v-if="$route.path.includes('contas/caixa')"
        v-authorized="'FINANCEIRO.CAIXA:CADASTRAR'"
        title="CADASTRAR MOVIMENTO NO BANCO"
        :to="`/financeiro/contas/caixa/cadastro`"
      />
      <SGRegisterButton
        v-if="!$route.path.includes('contas/caixa')"
        v-authorized="'FINANCEIRO.CAIXA:CADASTRAR'"
        title="CADASTRAR MOVIMENTO NO CAIXA"
        to="/financeiro/caixa/cadastro"
      />
    </template>

    <InfoModal
      v-if="infoModal.enableModal"
      title="Dados da parcela"
      :data-modal="infoModal.data"
      v-model="infoModal.enableModal"
    />

    <TransferInfoModal
      v-if="transferInfoModal.enableModal"
      title="Dados da transferência"
      :data-modal="transferInfoModal.data"
      v-model="transferInfoModal.enableModal"
    />

    <SGIndexSkeleton v-if="loading.page" />
    <SGCard v-else id="receipt-page-card">
      <Totalizadores :data="saldoData" :is-date-filtering="isDateFiltering" />

      <SGTable
        @after-request="
          () => {
            getSaldo();
          }
        "
        id="cashIndex"
        v-model:selected="tableSelectedItems"
        :store="financial"
        :columns="columns"
        :actions="tableActions"
        :table-bind="tableBind"
        :filters-v2="true"
        search-input-column-name="descricao"
      >
        <template #extra-filter>
          <div class="tw-flex tw-flex-col tw-gap-4">
            <SGDateFilters
              :handle-clear-date="true"
              ref="dateFiltersRef"
              :date-fields="dateFields"
              :store="financial"
              :hide-dropdown="true"
            />
          </div>
        </template>
      </SGTable>
    </SGCard>
    <ActionBar
      v-if="tableSelectedItems.length !== 0"
      :action-text="selectedLengthText"
      :buttons="actionBarButtons"
    />
  </SGPage>
</template>

<script setup>
import SGCard from 'components/generic/SGCard.vue';
import SGPage from 'components/generic/SGPage.vue';
import SGRegisterButton from 'components/generic/SGRegisterButton.vue';
import SGConfigButton from 'src/core/components/SG/Buttons/SGConfigButton.vue';
import InfoModal from 'components/modal/financial/InfoModal.vue';
import {
  getDateTimeFromString,
  getFirstAndCurrentDayOfCurrentMonth
} from 'components/utils/dates';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import ActionBar from 'src/components/actionBar/index.vue';
import { currencyFormat } from 'src/components/utils/currencyFormat';

import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { applyEllipsisToString, isDateValid } from 'src/services/utils';

import { useGlobal } from 'stores/global';
import {
  computed,
  onUnmounted,
  onMounted,
  reactive,
  ref,
  toRaw,
  watch
} from 'vue';
import SGIndexSkeleton from 'src/core/components/SG/Skeleton/SGIndexSkeleton.vue';
import Totalizadores from 'src/modules/financeiro/caixa/components/Totalizadores.vue';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import SGDateFilters from 'src/core/components/SG/Filters/SGDateFilters.vue';
import { useMovimentoStore } from 'src/modules/financeiro/caixa/store/useMovimentoStore.js';
import handleApiErrors from 'src/services/handleApiErrors';
import TransferModal from 'src/modules/financeiro/caixa/components/modal/transferencia/TransferModal.vue';
import { useRoute, useRouter } from 'vue-router';
import { useBankAccount } from 'stores/api/bank/bankAccount';
import ReceiptModal from 'components/modal/financial/ReceiptModal.vue';
import { print } from 'src/services/FastPrintService';
import Shortkey from 'src/components/generic/shortkey/Shortkey.vue';
import TransferInfoModal from 'src/components/modal/financial/TransferInfoModal.vue';
import useCaixaTableActionBar from '../composables/useCaixaTableActionBar';
import { processMobilePrint } from 'src/services/processMobilePrint';
import SGButton from 'src/components/generic/SGButton.vue';
import TabelaModelosRelatorio from 'src/modules/relatorios/modelos/components/TabelaModelosRelatorio.vue';

const global = useGlobal();
const { showShortKey, altOrOption } = storeToRefs(global);
const router = useRouter();
const $q = useQuasar();
const dateFiltersRef = ref(null);

// Instância Route
const route = useRoute();
const { params } = route;

// Stores
const financial = useMovimentoStore();
financial.loading.page = true;
const { loading } = storeToRefs(financial);

// Instância do store conta bancaria
const bankAccount = useBankAccount();
const { data: bankAccountData } = storeToRefs(bankAccount);

const isDateFiltering = ref(false);

// Filtro de data inicial
const { firstDay, lastDay } = getFirstAndCurrentDayOfCurrentMonth();

financial.table.filters = {
  dataVencimento: {
    field: 'dataPagamento',
    filterType: 'DATE_TIME_RANGE',
    filterValue: {
      startDate:
        isDateValid(firstDay) && firstDay <= lastDay
          ? firstDay + ' 00:00:00'
          : '',
      endDate:
        isDateValid(lastDay) && firstDay <= lastDay ? lastDay + ' 23:59:59' : ''
    }
  }
};

financial.table.orderBy = {
  dataPagamento: 'desc'
};

// Ref para armazenar itens selecionados na tabela e referência do store
const tableSelectedItems = ref([]);

const dateFields = ref([
  {
    field: 'dataPagamento',
    label: 'Data de pagamento'
  },
  {
    field: 'dataVencimento',
    label: 'Data de vencimento'
  },
  {
    field: 'created_at',
    label: 'Data de criação'
  }
]);

const tableBind = ref({
  selection: 'single',
  relatorioRapido: true,
  rowKey: 'controle',
  apiRelatorioCompleto: '/api/financeiro/movimento/relatorio',
  roleRelatorioRapido: 'FINANCEIRO.CAIXA:GERAR-RELATORIO-RAPIDO',
  roleRelatorioCompleto: 'FINANCEIRO.CAIXA:GERAR-RELATORIO-COMPLETO'
});

// Ref para controlar o modal de informações
const saldoData = ref();
// Ref para controlar a exibição do modal de pagamento
const modalRef = ref(false);
// Ref para controlar o modal de informações
const infoModal = ref({ enableModal: false, data: {} });
const transferInfoModal = ref({ enableModal: false, data: {} });

// Computed para exibir o texto com o total de registros selecionados
const selectedLengthText = computed(() => {
  if (tableSelectedItems.value.length > 1) {
    return `${tableSelectedItems.value.length} registros selecionados`;
  } else {
    return `${tableSelectedItems.value.length} registro selecionado`;
  }
});
// Watcher para verificar se está na rota do caixa
watch(
  () => route.path,
  async (newValue) => {
    if (newValue.includes('financeiro/caixa')) {
      financial.table.filters = {
        quitado: {
          field: 'quitado',
          filterType: 'EQUALS',
          filterValue: true
        },
        codContaBancaria: {
          field: 'codContaBancaria',
          filterType: 'NULL'
        }
      };
      // await updateFinancialAndSaldo();
    }
  }
);

const isBankPage = computed(() =>
  route.path.includes('financeiro/contas/caixa')
);

onMounted(async () => {
  financial.loading.page = true;
  financial.table.filters = {
    ...financial.table.filters,
    quitado: {
      field: 'quitado',
      filterType: 'EQUALS',
      filterValue: true
    }
  };

  if (params.controle && route.path.includes('contas/caixa')) {
    financial.table.filters.codContaBancaria = {
      field: 'codContaBancaria',
      filterType: 'EQUALS',
      filterValue: params.controle
    };
    await bankAccount.get(params.controle);
    global.bankSelected = bankAccountData.value;
  } else {
    financial.table.filters.codContaBancaria = {
      field: 'codContaBancaria',
      filterType: 'NULL'
    };
  }
  await Promise.all([financial.get({ filtersV2: true }), getSaldo()]);

  financial.loading.page = false;
});

onUnmounted(() => {
  financial.resetData();
});

async function updateFinancialAndSaldo() {
  await Promise.all([
    financial.updateRowsPerPage(),
    getSaldo({ filtersV2: true })
  ]);
}
const isRelatorioModalOpen = ref(false);

const handleGerarRelatoriosModal = async () => {
  if (isRelatorioModalOpen.value) return;

  isRelatorioModalOpen.value = true;
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: TabelaModelosRelatorio,
      scope: 'TabelaModelosRelatorio',
      hasCancel: false,
      hasSave: false,
      title: 'Relatórios - FINANCEIRO',
      classesTopBox: 'lg:tw-w-[1000px]',
      dataModal: {
        modulo: 'App\\Models\\Cliente\\Financeiro\\Financeiro'
      }
    }
  })
    .onOk(() => {
      isRelatorioModalOpen.value = false;
    })
    .onCancel(() => {
      isRelatorioModalOpen.value = false;
    });
};

const getSaldo = async () => {
  let filter = {};
  if (isBankPage.value) {
    filter.codContaBancaria = params.controle;
  } else {
    filter.codCentroCusto = 'all';
  }

  const requestParams = {
    // filters: { ...financial.table.filters }
    ...filter,
    filtersV2: [...Object.values(financial.table.filters)]
  };
  requestParams.filtersV2 = requestParams.filtersV2.filter(
    (filter) => filter.field !== 'codContaBancaria'
  );
  try {
    const { status, data } = await api.get(`/api/financeiro/movimento/saldo`, {
      params: requestParams
    });

    if (status === 200 && data) {
      saldoData.value = data;

      const hasDateFilter =
        'dataPagamento' in requestParams.filtersV2 ||
        'dataVencimento' in requestParams.filtersV2 ||
        'created_at' in requestParams.filtersV2;

      const hasEmptyDateFilters =
        (!requestParams.filtersV2.dataPagamento ||
          requestParams.filtersV2.dataPagamento.length === 0) &&
        (!requestParams.filtersV2.dataVencimento ||
          requestParams.filtersV2.dataVencimento.length === 0) &&
        (!requestParams.filtersV2.created_at ||
          requestParams.filtersV2.created_at.length === 0);

      if (hasDateFilter) {
        isDateFiltering.value = !hasEmptyDateFilters;
      } else {
        isDateFiltering.value = false;
      }
    }
  } catch (error) {
    handleApiErrors(error);
  }
};

// Abrir modal
const openTransferModal = () => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: TransferModal,
      scope: 'transfer-modal',
      hasCancel: false,
      hasSave: false,
      classCardSection: 'lg:tw-w-[650px]',
      classesTopBox: '!tw-justify-start',
      title: 'Transferir para',
      dataModal: {}
    }
  }).onOk(async () => {
    await updateFinancialAndSaldo();
  });
};

// Função para abrir o modal de informações com os dados da linha selecionada
function openModalData(row) {
  if (row?.transferencia) {
    // Se for transferência, abre modal de transferência
    transferInfoModal.value = { enableModal: true, data: row };
  } else {
    // Caso normal
    infoModal.value = { enableModal: true, data: row };
  }
}

const tableActions = ref([
  {
    name: 'search',
    icon: {
      name: 'search'
    },
    tooltip: 'Visualizar movimento',
    disableOnCondition: true,
    condition: (row) =>
      (global.roles?.includes('FINANCEIRO.CAIXA:VISUALIZAR') && row.quitado) ||
      row.cancelado,
    action: (row) => openModalData(row)
  },
  {
    name: 'print',
    icon: {
      name: 'print'
    },
    tooltip: 'Imprimir Comprovante',
    disableOnCondition: true,
    condition: (row) =>
      global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO') &&
      row.quitado &&
      row.type === 'REC' &&
      !row.transferencia,
    action: (row) => printComprovante(row)
  }
]);

function cellColorStatus(row, withBg = '') {
  const ENTRADA = row?.type === 'REC'; // ou row.status == 1
  const SAIDA = row?.type === 'PAY'; // ou row.status == 2

  if (ENTRADA) return `status-cel-green${withBg}`;
  if (SAIDA) return `status-cel-red${withBg}`;
}

// Definição das colunas da tabela
const columns = reactive([
  {
    name: 'controle',
    field: 'controle',
    required: true,
    label: 'Cód.',
    align: 'left',
    sortable: true,
    filterType: 'EQUALS',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'documento',
    field: 'documento',
    label: 'Documento',
    sortable: true,
    align: 'left',
    filterType: 'EQUALS',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'descricao',
    field: 'descricao',
    label: 'Descrição',
    sortable: true,
    align: 'left',
    enableTooltip: true,
    filterType: 'ILIKE',
    classes: `!tw-max-w-72`,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'pessoa.razaoSocial',
    field: (row) => row?.pessoa?.razaoSocial,
    label: 'Pessoa',
    sortable: true,
    align: 'left',
    enableTooltip: true,
    filterType: 'ILIKE',
    classes: `!tw-max-w-72`,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'valorEntrada',
    field: (row) => 'R$ ' + currencyFormat(row.valorEntrada),
    label: 'Valor entrada',
    sortable: true,
    align: 'left',
    filterType: 'VALUE_RANGE',
    isMoney: true,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'valorSaida',
    field: (row) => 'R$ ' + currencyFormat(row.valorSaida),
    label: 'Valor saída',
    sortable: true,
    align: 'left',
    filterType: 'VALUE_RANGE',
    isMoney: true,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'valorPago',
    field: (row) => 'R$ ' + currencyFormat(row.valorPago),
    label: 'Valor pago',
    sortable: true,
    align: 'left',
    filterType: 'VALUE_RANGE',
    isMoney: true,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'valorDesconto',
    field: (row) => 'R$ ' + currencyFormat(row.valorDesconto),
    label: 'Valor desconto',
    sortable: true,
    align: 'left',
    filterType: 'VALUE_RANGE',
    isMoney: true,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'financeiroWithPlanoConta.descricao',
    field: (row) => row?.financeiroWithPlanoConta?.descricao,
    label: 'Plano conta',
    sortable: true,
    align: 'left',
    sort: 'disabled',
    filterType: 'ILIKE',
    enableTooltip: true,
    tooltip: (row) => row?.financeiroWithPlanoConta?.descricao,
    format: (val) => applyEllipsisToString(val, 15) || '-',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'financeiroWithCentroCusto.descricao',
    field: (row) => row?.financeiroWithCentroCusto?.descricao,
    label: 'Centro custo',
    sortable: true,
    align: 'left',
    sort: 'disabled',
    filterType: 'ILIKE',
    tooltip: (row) => row?.financeiroWithCentroCusto?.descricao,
    format: (val) => applyEllipsisToString(val, 15) || '-',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'created_at',
    field: (row) =>
      row.createdAt?.substr(0, 10)?.split('-')?.reverse()?.join('/'),
    label: 'Data parcela',
    sortable: true,
    align: 'left',
    sort: 'none',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'dataVencimento',
    field: (row) =>
      row.dataVencimento?.substr(0, 10)?.split('-')?.reverse()?.join('/'),
    label: 'Data vencimento',
    sortable: true,
    align: 'left',
    sort: 'none',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'dataPagamento',
    field: (row) =>
      row.dataPagamento?.substr(0, 10)?.split('-')?.reverse()?.join('/'),
    tooltip: (row) => getDateTimeFromString(row?.dataPagamento),
    label: 'Data pagamento',
    sortable: true,
    align: 'left',
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'codEspecie',
    field: (row) => row?.financeiroWithEspecie,
    tooltip: (row) => row?.financeiroWithEspecie?.descricaoCodigo,
    format: (row) => row?.descricaoCodigo,
    label: 'Espécie',
    sortable: true,
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE',
    extraFilters: [
      {
        field: 'financeiroWithEspecie.descricao',
        filterType: 'ILIKE',
        filterValue: 'financeiroWithEspecie.descricao'
      },
      {
        operator: 'OR',
        field: 'codEspecie',
        filterType: 'EQUALS',
        filterValue: 'financeiroWithEspecie.controle'
      }
    ],
    group: true,
    classes: `!tw-max-w-72`,
    cellClasses: cellColorStatus,
    filtersV2: true
  },
  {
    name: 'type',
    field: (row) => (row?.type == 'REC' ? 'Entrada' : 'Saída'),
    label: 'Tipo',
    align: 'center',
    classes: `sticky-table-column-with-line !tw-min-w-40 !tw-w-36 !tw-max-w-36 !tw-right-[80px]`,
    headerClasses: 'sticky-table-column-header-with-line !tw-right-[80px]',
    cellClass: '!tw-min-w-36 !tw-w-36 !tw-max-w-36 !tw-text-center',
    cellClasses: (row) => cellColorStatus(row, '-with-bg'),
    fixed: true,
    required: true,
    filtersV2: true
  },
  {
    name: 'actions',
    field: 'actions',
    label: 'Ações',
    align: 'center',
    headerClasses: '!tw-min-w-[80px] !tw-w-[80px] !tw-max-w-[80px] ',
    fixed: true,
    required: true,
    filtersV2: true
  }
]);

// Função para fazer a impressão de apenas um comprovante
const printSingleClient = async (controles, eventType = false) => {
  const ModeloComprovante = {
    A4: 1,
    A4Compacta: 2,
    Termica80: 3,
    Termica58: 4
  };
  const route = eventType ? `?modelo=${ModeloComprovante[eventType]}` : ``;
  const response = await api.post(
    `/api/financeiro/movimento/caixa/comprovante${route}`,
    {
      controles
    },
    {
      responseType: 'arraybuffer'
    }
  );

  if (response.status == 200) {
    const shouldPrint = !(await processMobilePrint(response.data, true));
    if (shouldPrint) {
      const module = 'caixa';
      print(response.data, module);
    }
  }
};

// Função para abrir o modal de informações da parcela selecionada
const handleViewSelected = () => {
  if (tableSelectedItems.value.length > 0) {
    openModalData(toRaw(tableSelectedItems.value[0]));
    tableSelectedItems.value = [];
  }
};
const printComprovante = async (comprovante, eventType = false) => {
  const controles = [comprovante.controle];

  printSingleClient(controles, eventType);
};

// Função imprimir comprovante pela actionBar
const handlePrintComprovante = (eventType = false) => {
  printComprovante(tableSelectedItems.value[0], eventType);
  tableSelectedItems.value = [];
  updateFinancialAndSaldo();
};

//Definir ações da action bar
const actionBarEvents = {
  view: {
    callback: handleViewSelected,
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:VISUALIZAR')
  },
  cancel: {
    callback: () => {
      tableSelectedItems.value = [];
    }
  },
  printComprovante: {
    callback: handlePrintComprovante,
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO')
  },
  printComprovanteA4: {
    callback: () => handlePrintComprovante('A4'),
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO')
  },
  printComprovanteA4Compacta: {
    callback: () => handlePrintComprovante('A4Compacta'),
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO')
  },
  printComprovanteTermica80: {
    callback: () => handlePrintComprovante('Termica80'),
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO')
  },
  printComprovanteTermica58: {
    callback: () => handlePrintComprovante('Termica58'),
    condition: () => global.roles?.includes('FINANCEIRO.CAIXA:IMPRIMIR-RECIBO')
  }
};

const actionBarButtons = useCaixaTableActionBar({
  selected: tableSelectedItems,
  events: actionBarEvents
});

const hotkeyScope = 'cash-index';
const atalhos = [
  {
    key: 'f11',
    event: () => router.push('/financeiro/caixa/configuracoes'),
    condition: () =>
      global.roles?.includes('FINANCEIRO.CAIXA:CONFIGURACAO') &&
      !isBankPage.value
  },
  {
    key: 'alt+t',
    event: () => openTransferModal(),
    condition: computed(() =>
      global.roles?.includes('FINANCEIRO.CAIXA:TRANSFERIR-SALDO')
    )
  },
  {
    key: 'f4',
    event: () => {
      if (route.path.includes('contas/caixa')) {
        router.push('/financeiro/contas/caixa/cadastro');
      } else {
        router.push('/financeiro/caixa/cadastro');
      }
    },
    condition: computed(() =>
      global.roles?.includes('FINANCEIRO.CAIXA:CADASTRAR')
    )
  },
  {
    key: 'esc',
    event: () => router.push('/financeiro/contas'),
    condition: route.path.includes('contas/caixa')
  },
  {
    key: 'alt+r',
    event: () => handleGerarRelatoriosModal(),
    condition: computed(() => global.roles?.includes('RELATORIO.MODELO:GERAR'))
  }
];

useScopedHotkeys(atalhos, hotkeyScope);
</script>
