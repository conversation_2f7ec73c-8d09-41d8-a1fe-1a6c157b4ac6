<template>
  <SGRegisterSkeleton v-if="isLoadingPage" />

  <SGRegisterPage
    v-else
    ref="registerPageRef"
    title="Configurações gerais da empresa"
    id="company_config"
    action-text="Gostaria de salvar?"
    :modal="modal"
    :buttons="actionBarButtons"
    :disable-warning-animation="isWarningAnimationDisabled"
  >
    <SGCard
      title="Gerais"
      id="general-config"
      :cols="12"
      :schema-errors="errors"
      :error-keys="['emailXml.emailsAdicionais']"
      :meta="meta"
      remove-gap
      has-circle
      has-title-separator
    >
      <q-checkbox
        v-model="fields.mostrarNomeFantasia.value"
        :error="!!fields.mostrarNomeFantasia.errorMessage"
        :error-message="fields.mostrarNomeFantasia.errorMessage"
        :valid="fields.mostrarNomeFantasia.meta.valid"
        :disable="!global.roles?.includes('EMPRESA.CONFIGURACAO:EDITAR')"
        size="2rem"
        label="Mostrar nome fantasia em documentos fiscais, cupons e relatórios"
        class="tw-col-span-full"
      />

      <q-input
        v-model="fields.qtdCasasDecimais.value"
        :error="!!fields.qtdCasasDecimais.errorMessage"
        :error-message="fields.qtdCasasDecimais.errorMessage"
        :valid="fields.qtdCasasDecimais.meta.valid"
        v-bind="ModelInputText"
        input-class="tw-text-right"
        label="Número de casas decimais (produtos e vendas)"
        mask="##"
        required
        class="tw-col-span-5 tw-mt-2"
        label-slot
        unmasked-value
      >
        <template #hint>
          <p class="tw-text-inheritSize tw-text-SGBRBlueLighten">
            Campos de valor unitário e preço de venda
          </p>
        </template>
      </q-input>
    </SGCard>

    <EmailConfig
      v-model:fields="fields"
      v-model:errors="errors"
      v-model:values="values"
      :validate="validate"
      :required-on-filled="true"
      :readonly-fields="!global.roles?.includes('EMPRESA.CONFIGURACAO:EDITAR')"
    />

    <SGCard
      title="E-mail para envio de xml"
      id="e-mailXml"
      :cols="12"
      :schema-errors="errors"
      :error-keys="['emailXml.emailsAdicionais']"
      :meta="meta"
      remove-gap
      has-circle
      has-title-separator
    >
      <EmailXml
        ref="emailXmlRef"
        :fields="fields.emailXml"
        :required-on-filled="true"
        :errors="errors"
        :readonly-fields="
          !global.roles?.includes('EMPRESA.CONFIGURACAO:EDITAR')
        "
      />
    </SGCard>
    <SGCard
      title="WhatsApp"
      id="whatsapp"
      :cols="12"
      :schema-errors="errors"
      :error-keys="['whatsapp']"
      :meta="meta"
      remove-gap
      has-circle
      has-title-separator
    >
      <WhatsApp
        ref="whatsappRef"
        :fields="fields.whatsapp"
        :required-on-filled="true"
        :errors="errors"
        :always-enable-connect="true"
        :show-send-message-button="false"

        :config-type="1"
      />
    </SGCard>
  </SGRegisterPage>
</template>

<script setup>
import useRegister from 'components/actionBar/composables/useRegister';
import RegisterUndo from 'components/modal/global/RegisterUndo.vue';
import { useQuasar } from 'quasar';
import notify from 'src/components/utils/notify';
import { diffObjects } from 'src/components/utils/tests';
import SGCard from 'src/core/components/SG/Card/NewSGCard.vue';
import EmailConfig from 'src/core/components/SG/Email/EmailConfig.vue';
import EmailXml from 'src/core/components/SG/Email/EmailXml.vue';
import SGRegisterPage from 'src/core/components/SG/Register/NewSGRegisterPage.vue';
import SGRegisterSkeleton from 'src/core/components/SG/Skeleton/SGRegisterSkeleton.vue';
import { useFormSchema } from 'src/modules/navbarUsuario/empresa/config/models/useFormSchema';
import { useEmailConfigStore } from 'src/modules/vendas/pdv/store/useEmailConfigStore';
import { useEmailXmlConfigStore } from 'src/modules/vendas/pdv/store/useEmailXmlConfigStore';
import { useGlobal } from 'src/stores/global';
import { ref, toRaw, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useCompanyConfigStore } from 'src/modules/navbarUsuario/empresa/config/stores/useCompanyConfigStore';
import ModelInputText from 'src/core/models/inputs/Text';
import redirectToPrevSystemRoute from 'src/router/utils/redirectToPrevSystemRoute.js'
import WhatsApp from 'src/core/components/SG/WhatsApp/WhatsApp.vue';
const emit = defineEmits(['cancel', 'ok']);
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const { modal } = toRefs(props);
const $q = useQuasar();
const isWarningAnimationDisabled = ref(true);
const router = useRouter();

// Stores
const global = useGlobal();
const companyConfigStore = useCompanyConfigStore();
companyConfigStore.table.filters = {
  ...companyConfigStore.table.filters,
  codEmpresa: {
    filterType: 'EQUALS',
    filterValue: global.company.controle
  }
};

const emailConfigStore = useEmailConfigStore();
emailConfigStore.table.filters = {
  ...emailConfigStore.table.filters,
  tipo: {
    filterValue: 1,
    filterType: 'EQUALS'
  }
};

const emailXmlConfigStore = useEmailXmlConfigStore();

// Refs
const isLoadingPage = ref(true);
const registerPageRef = ref();
const emailXmlRef = ref();
let editValues = false;

async function getConfig() {
  isLoadingPage.value = true;
  const [companyConfigPromise, emailConfigPromise] = await Promise.all([
    companyConfigStore.get({ deletedAt: false }),
    emailConfigStore.get({ deletedAt: false })
  ]);
  isLoadingPage.value = false;

  const { success: companySuccess, data: companyConfigData } =
    companyConfigPromise;
  const { success: emailSuccess, data: emailConfigData } = emailConfigPromise;

  if (!companySuccess || !emailSuccess) cancel();

  const { rowsData: companyConfigs } = companyConfigData;
  const companyConfig = companyConfigs?.[0] ?? {};

  const { rowsData: emailConfigs } = emailConfigData;
  const { configEmail, configEmailXml } = emailConfigs?.[0] ?? {};

  editValues = {
    ...companyConfig,
    codEmpresa: global.company.controle,
    email: configEmail,
    emailXml: { ...configEmailXml, padraoEmailXml: false }
  };
}

await getConfig();
const {
  fields,
  values,
  initialValues,
  errors,
  meta,
  isSubmitting,
  validate,
  handleReset
} = useFormSchema(editValues);

function cancel() {
  isWarningAnimationDisabled.value = true;

  if (modal.value) emit('cancel');
  router.push('/dashboard');
}

function resetForm() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(() => {
    emailXmlRef.value?.clearField();
    handleReset();
  });
}

async function isFormValid() {
  if (isSubmitting.value) return false;
  isSubmitting.value = true;

  const validation = await validate(values, { context: values });
  if (!validation.valid) registerPageRef.value?.scrollToError(validation);
  isSubmitting.value = false;

  return validation.valid;
}

async function save() {
  if (!(await isFormValid())) return;
  let payload = toRaw(values);

  const emailPayload = payload.email;
  const emailXmlPayload = payload.emailXml;
  const companyPayload = payload;

  const [companyPromise, emailPromise, emailXmlPromise] = await Promise.all([
    sendCompanyConfigRequest(initialValues.value, companyPayload),
    sendEmailConfigRequest(emailPayload),
    sendEmailXmlConfigRequest(emailXmlPayload)
  ]);

  if (
    companyPromise?.success &&
    emailPromise?.success &&
    emailXmlPromise?.success
  ) {
    notify('Dados salvos com sucesso!', 'positive');

    redirectToPrevSystemRoute(router);
  }
}

async function sendCompanyConfigRequest(initialValues, payload) {
  return companyConfigStore.put({
    payload,
    silence: true
  });
}

async function sendEmailConfigRequest(emailPayload) {
  return await emailConfigStore.put({
    payload: { configEmail: emailPayload },
    silence: true
  });
}

async function sendEmailXmlConfigRequest(xmlEmailPayload) {
  return await emailXmlConfigStore.put({
    payload: { configEmailXml: xmlEmailPayload },
    silence: true
  });
}

// Desfazer alterações.
watch(
  values,
  async () => {
    isWarningAnimationDisabled.value = !Object.keys(
      diffObjects(initialValues.value, values)
    ).length;
  },
  {
    immediate: true,
    deep: true
  }
);

const actionBarEvents = {
  save: {
    callback: save,
    condition: () => global.roles?.includes('EMPRESA.CONFIGURACAO:EDITAR')
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: resetForm
  }
};

const actionBarButtons = useRegister({
  params: { modal: props.modal, isWarningAnimationDisabled },
  events: actionBarEvents
});
</script>
