import { toTypedSchema } from '@vee-validate/yup';
import { userTemplate } from 'src/components/ConfigEmpresa/Email/HtmlTemplates/userTemplate';
import { useGlobal } from 'src/stores/global';
import { useField, useFieldArray, useForm } from 'vee-validate';
import { ref } from 'vue';
import { array, boolean, number, object, string } from 'yup';
import { emailRegex } from 'src/components/utils/emailRegex';

/**
 * Retorna os objetos de formulários vinculados com suas configurações de validação.
 * @returns {Object} Um objeto contendo os formulários vinculados e funções auxiliares.
 */
export function useFormSchema(initialValues = false) {
  const global = useGlobal();

  const schema = object({
    controle: string().default('').nullable(),
    codEmpresa: number().default(global.company.controle).nullable(),
    mostrarNomeFantasia: boolean().default(false),
    qtdCasasDecimais: number().default(2).nullable().required('O campo é obrigatório.').min(2, 'Minimo de 2 casas decimais').max(10, 'Máximo de 10 casas decimais').transform((value, originalValue) => (!originalValue ? 0 : value)),
    email: object({
      tipo: number().default(1).nullable(),
      enviaEmailEmissao: boolean()
        .default(false)
        .required('O campo é obrigatório.'),
      smtpServidor: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          const { parent } = ctx;

          if (
            !value &&
            (parent.smtpPorta ||
              parent.smtpUsuario ||
              parent.smtpEmail ||
              parent.smtpSenha)
          ) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }

          return true;
        }),
      smtpPorta: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          const { parent } = ctx;

          if (
            !value &&
            (parent.smtpServidor ||
              parent.smtpUsuario ||
              parent.smtpEmail ||
              parent.smtpSenha)
          ) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }

          return true;
        }),
      smtpUsuario: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          const { parent } = ctx;

          if (
            !value &&
            (parent.smtpServidor ||
              parent.smtpPorta ||
              parent.smtpEmail ||
              parent.smtpSenha)
          ) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }

          if (value && value.includes('@') ? !emailRegex.test(value) : false) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpEmail: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          const { parent } = ctx;

          if (
            !value &&
            parent.smtpUsuario &&
            !parent.smtpUsuario.includes('@') &&
            !emailRegex.test(parent.smtpUsuario) &&
            (parent.smtpServidor || parent.smtpPorta || parent.smtpSenha)
          ) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }

          if (value && !emailRegex.test(value)) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      smtpSenha: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          const { parent } = ctx;

          if (
            !value &&
            (parent.smtpServidor ||
              parent.smtpUsuario ||
              parent.smtpEmail ||
              parent.smtpPorta)
          ) {
            return ctx.createError({ message: 'O campo é obrigatório.' });
          }

          return true;
        }),
      smtpSSL: boolean().default(false).required('O campo é obrigatório.'),
      smtpAutoTls: boolean().default(true).required('O campo é obrigatório.'),
      assuntoEmail: string().default('').nullable(),
      descricaoRemetente: string().default('').nullable(),
      arquivosPartes: boolean()
        .default(false)
        .required('O campo é obrigatório.'),
      tamanhoArquivo: string().default('15').nullable().test((value, ctx) => {
        return (value > 0 && !ctx.parent.padrao) || ctx.parent.padrao || (!ctx.parent.padrao && !ctx.parent.arquivosPartes) || (ctx.parent.arquivosPartes && ctx.createError({ message: 'Minimo de 1 MB' }));
      }),
      emailsAdicionais: array().default([]).nullable(),
      footerHtml: string().default(userTemplate).nullable()
    }),
    emailXml: object({
      tipo: number().default(1).nullable(),
      padraoEmailXml: boolean().default(true).nullable(),
      assuntoEmailXml: string().default('').nullable(),
      emailPrincipalXml: string()
        .default('')
        .nullable()
        .test((value, ctx) => {
          if (value && !emailRegex.test(value)) {
            return ctx.createError({ message: 'E-mail inválido.' });
          }
          return true;
        }),
      emailsAdicionais: array().default([]).nullable()
    }),
    whatsapp: object({
      padraoWhatsApp: boolean().default(false).nullable(),
    
    })
  });

  // Configura os valores do formulário e suas validações.
  const form = useForm({
    validationSchema: toTypedSchema(schema),
    validateOnMount: false,
    initialValues: initialValues
      ? Object.keys(initialValues).length === 1
        ? initialValues
        : schema
            .json()
            .cast(initialValues, { assert: false, stripUnknown: true })
      : undefined,
    initialErrors: {}
  });

  // Função recursiva para criar campos dinamicamente
  function createFields(schema, parentKey = '') {
    const fields = {};
    Object.keys(schema.fields).forEach((key) => {
      const fieldKey = parentKey ? `${parentKey}.${key}` : key;
      const fieldSchema = schema.fields[key];
      if (fieldSchema.type === 'object') {
        fields[key] = createFields(fieldSchema, fieldKey);
      } else if (fieldSchema.type === 'array' && fieldSchema.schema) {
        fields[key] = useFieldArray(fieldKey);
      } else {
        fields[key] = useField(fieldKey);
      }
    });
    return fields;
  }

  const fields = ref(createFields(schema));
  return { ...form, fields, useField };
}
