<template>
  <SGPage title="Smart POS" id="register-smartPos-page">
    <template #buttons>
      <SGButton
        v-authorized="'RELATORIO.MODELO:GERAR'"
        class="refactInput"
        @click="handleGerarRelatoriosModal"
        atalho="alt+r"
        >Relatórios</SGButton
      >
      <SGRegisterButton
        v-authorized="'CADASTRO.SMARTPOS:CADASTRAR'"
        title="CADASTRAR"
        :on-click="handleRegister"
      />
    </template>

    <SGCard id="register-smartPos-page-table">
      <template v-if="loading.page">
        <SGIndexSkeleton />
      </template>

      <SGTable
        v-else
        id="smart-pos"
        v-model:selected="tableSelectedItems"
        :store="smartPosStore"
        :columns="columnsRef"
        :actions="tableActions"
        :table-bind="tableBind"
        search-input-column-name="nomeMaquina"
      >
        <template #extra-filter>
          <SGActiveInactiveFilter
            @on-filter="
              () => {
                tableSelectedItems = [];
              }
            "
            :store="smartPosStore"
          />
        </template>
      </SGTable>
    </SGCard>

    <!-- BARRA DE AÇÕES DA TABELA -->
    <ActionBar
      v-if="tableSelectedItems.length !== 0"
      :action-text="selectedLengthText"
      :buttons="actionBarButtons"
    />
    <!-- FIM BARRA DE AÇÕES DA TABELA -->
  </SGPage>
</template>

<script setup>
// Core
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { computed, onMounted, onUnmounted, ref } from 'vue';

// Components
import { Dialog } from 'quasar';
import ActionBar from 'src/components/actionBar/index.vue';
import SGPage from 'components/generic/SGPage.vue';
import SGCard from 'components/generic/SGCard.vue';
import SGTable from 'src/core/components/SG/Table/SGTable.vue';
import RegisterDelete from 'components/modal/global/RegisterDelete.vue';
import SGIndexSkeleton from 'src/core/components/SG/Skeleton/SGIndexSkeleton.vue';
import SGActiveInactiveFilter from 'src/core/components/SG/Table/Filter/SGActiveInactiveFilter.vue';
import RegisterSmartPosDialog from 'src/modules/cadastros/smartPOS/components/RegisterSmartPosDialog.vue';

// Composables
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';

// Stores
import { useGlobal } from 'stores/global';
import { useSmartPosStore } from 'src/modules/cadastros/smartPOS/store/useSmartPosStore';
import SGRegisterButton from 'src/core/components/SG/Register/SGRegisterButton.vue';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import useSmartposTableActionBar from '../composables/useSmartposTableActionBar';
import TabelaModelosRelatorio from 'src/modules/relatorios/modelos/components/TabelaModelosRelatorio.vue';
import SGButton from 'src/components/generic/SGButton.vue';
const $q = useQuasar();
const global = useGlobal();
const smartPosStore = useSmartPosStore();

const tableSelectedItems = ref([]);

const { loading } = storeToRefs(smartPosStore);
onMounted(async () => {
  smartPosStore.loading.page = true;
  await smartPosStore.get();
  smartPosStore.loading.page = false;
});

const selectedLengthText = computed(() => {
  if (tableSelectedItems.value.length > 1) {
    return `${tableSelectedItems.value.length} registros selecionados`;
  } else {
    return `${tableSelectedItems.value.length} registro selecionado`;
  }
});

const isRelatorioModalOpen = ref(false);

const handleGerarRelatoriosModal = async () => {
  if (isRelatorioModalOpen.value) return;

  isRelatorioModalOpen.value = true;
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: TabelaModelosRelatorio,
      scope: 'TabelaModelosRelatorio',
      hasCancel: false,
      hasSave: false,
      title: 'Relatórios - SMARTPOS',
      classesTopBox: 'lg:tw-w-[1000px]',
      dataModal: {
        modulo: 'App\\Models\\Cliente\\Smartpos\\Smartpos'
      }
    }
  })
    .onOk(() => {
      isRelatorioModalOpen.value = false;
    })
    .onCancel(() => {
      isRelatorioModalOpen.value = false;
    });
};

const isRegisterModalOpen = ref(false);

function openRegisterModal() {
  if (isRegisterModalOpen.value) return;

  isRegisterModalOpen.value = true;

  Dialog.create({
    component: MiniModalLayout,
    componentProps: {
      componentRef: RegisterSmartPosDialog,
      scope: 'add-edit-smart-pos',
      title: editData.value?.nomeMaquina
        ? 'Editar Smart POS'
        : 'Cadastrar Smart POS',
      classCardSection: 'lg:tw-w-[480px]',
      classesTopBox: '!tw-mr-6',
      hasCloseIcon: false,
      hasCancel: false,
      hasSave: false,
      dataModal: {
        editData: editData.value
      }
    }
  })
    .onOk(() => {
      isRegisterModalOpen.value = false;
    })
    .onCancel(() => {
      isRegisterModalOpen.value = false;
    });
}

const tableBind = ref({
  selection: 'single',
  // Validação pois não tem permissão no back
  relatorioRapido: global.plan === 'BÁSICO' ? false : true,
  rowKey: 'controle'
});

/* PARTE DE EDIÇÃO DA MAQUINA */
const handleRemoveRegister = () => {
  deleteItem(tableSelectedItems.value[0]);
};

const editCallback = () => {
  edit(tableSelectedItems.value[0]);
};

const tableActions = ref([
  {
    name: 'edit',
    icon: {
      name: 'img:/icons/edit.svg'
    },
    tooltip: 'Editar maquina',
    condition: (row) =>
      global.roles?.includes('CADASTRO.SMARTPOS:EDITAR') && !row.deletedAt,
    action: (row) => edit(row)
  },
  {
    name: 'delete',
    icon: {
      name: 'img:/icons/trash-closed-red.svg',
      color: 'negative',
      class: 'tw-bg-[#fd9999] tw-rounded-[4px]',
      size: '0.8rem'
    },
    tooltip: 'Excluir maquina',
    condition: (row) =>
      global.roles?.includes('CADASTRO.SMARTPOS:EXCLUIR') && !row.deletedAt,
    action: (row) => deleteItem(row)
  }
]);

const editData = ref();
/* FUNÇÃO DE QUANDO VAI EDITAR A MAQUINA */
const edit = (item, isNew = false) => {
  if (isNew || !item) {
    editData.value = {
      nomeMaquina: '',
      controle: null
    };
  } else {
    editData.value = {
      nomeMaquina: item.nomeMaquina,
      controle: item.controle
    };
  }

  tableSelectedItems.value = [];

  openRegisterModal();
};

/* COLUNAS DA TABELA DE SUB GRUPO */
const columns = [
  {
    name: 'controle',
    field: 'controle',
    tooltip: (row) => row?.controle,
    label: 'Código',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE',
    required: true
  },
  {
    name: 'nomeMaquina',
    field: 'nomeMaquina',
    tooltip: (row) => row?.nomeMaquina,
    label: 'Nome',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE'
  },
  {
    name: 'credenciadora',
    field: 'credenciadora',
    tooltip: (row) => row?.credenciadora,
    label: 'Credenciadora',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE'
  },
  {
    name: 'serialMaquina',
    field: 'serialMaquina',
    tooltip: (row) => row?.serialMaquina,
    label: 'Serial',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE'
  },
  {
    name: 'codAssociacao',
    field: 'codAssociacao',
    tooltip: (row) => row?.codAssociacao,
    label: 'Cod. associação',
    align: 'left',
    sort: 'none',
    filterType: 'ILIKE'
  },
  {
    name: 'actions',
    field: 'actions',
    label: 'Ações',
    align: 'center',
    required: true,
    fixed: true
  }
];

const columnsRef = ref(columns);

/* FIM COLUNAS DA TABELA DE SUB GRUPO */

const actionBarEvents = {
  delete: {
    callback: handleRemoveRegister,
    condition: () => global.roles?.includes('CADASTRO.SMARTPOS:EXCLUIR')
  },
  edit: {
    callback: editCallback,
    condition: () => global.roles?.includes('CADASTRO.SMARTPOS:EDITAR')
  }
};

/* USETABLE É A FUNÇÃO QUE RETORNA OS BOTÕES */
const actionBarButtons = useSmartposTableActionBar({
  selected: tableSelectedItems,
  events: actionBarEvents
});

/* FIM FUNÇÕES DA ACTION BAR */

function deleteItem(item) {
  $q.dialog({
    component: RegisterDelete,
    componentProps: {
      itemName: item.nomeMaquina,
      description: ` Todas as espécies associadas a esta máquina serão desassociadas. Tem certeza que deseja excluir`
    }
  }).onOk(async () => {
    const response = await smartPosStore.delete({ controle: item.controle });

    if (response.success) {
      await smartPosStore.updateRowsPerPage();
      tableSelectedItems.value = [];
    }
  });
}

onUnmounted(() => {
  smartPosStore.resetData();
});

function handleRegister() {
  editData.value = {
    nomeMaquina: '',
    controle: null
  };

  openRegisterModal();
}

// Definir atalhos
const hotkeyScope = 'smartPos-index';

const atalhos = [
  {
    key: 'f4',
    event: () => handleRegister(),
    condition: computed(() =>
      global.roles?.includes('CADASTRO.SMARTPOS:CADASTRAR')
    )
  },
  {
    key: 'alt+r',
    event: () => handleGerarRelatoriosModal(),
    condition: computed(() => global.roles?.includes('RELATORIO.MODELO:GERAR'))
  }
];

useScopedHotkeys(atalhos, hotkeyScope);
</script>
