import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from 'boot/axios'

export const useWhatsAppStore = defineStore('whatsapp', () => {
  // Estado das conexões por tipo
  const connections = ref({
    1: { isConnected: false, number: '', sessionId: '' }, // padrao
    2: { isConnected: false, number: '', sessionId: '' }, // venda  
    3: { isConnected: false, number: '', sessionId: '' }  // Financeiro
  })

  const useDefault = ref({
    2: false, 
    3: false
  })

  // Computed para pegar conexão da empresa
  const companyConnection = computed(() => connections.value[1])

  // Função para atualizar conexão
  function updateConnection(type, connectionData) {
    connections.value[type] = { ...connections.value[type], ...connectionData }
    saveToLocalStorage()
  }

  // Função para atualizar "usar padrão"
  function updateUseDefault(type, value) {
    useDefault.value[type] = value
    saveToLocalStorage()
  }

  // Função para obter conexão efetiva (considerando "usar padrão")
  function getEffectiveConnection(type) {
    if (type === 1) {
      return connections.value[1]
    }
    
    if (useDefault.value[type] && connections.value[1].isConnected) {
      return connections.value[1]
    }
    
    return connections.value[type]
  }

  // Salvar no localStorage
  function saveToLocalStorage() {
    localStorage.setItem('whatsapp-store', JSON.stringify({
      connections: connections.value,
      useDefault: useDefault.value
    }))
  }

  // Carregar do localStorage
  function loadFromLocalStorage() {
    const stored = localStorage.getItem('whatsapp-store')
    if (stored) {
      const data = JSON.parse(stored)
      connections.value = data.connections || connections.value
      useDefault.value = data.useDefault || useDefault.value
    }
  }

  // Limpar conexão
  function clearConnection(type) {
    connections.value[type] = { isConnected: false, number: '', sessionId: '' }
    saveToLocalStorage()
  }

  // Buscar conexões ativas da API
  async function fetchActiveConnections() {
    try {
      const response = await api.get('/api/whatsapp/conexoes')
      if (response.data && response.data.length > 0) {
        // Atualiza o store com as conexões ativas
        response.data.forEach(connection => {
          const tipo = parseInt(connection.tipo)
          if (connections.value[tipo]) {
            updateConnection(tipo, {
              isConnected: true,
              number: connection.celular,
              sessionId: connection.sessionId || ''
            })
          }
        })
      }
      return response.data
    } catch (error) {
      return []
    }
  }

  // Buscar conexão específica por tipo
  async function fetchConnectionByType(tipo) {
    try {
      const response = await api.get('/api/whatsapp/conexoes', { params: { tipo } })
      if (response.data && response.data.length > 0) {
        const connection = response.data[0]
        updateConnection(tipo, {
          isConnected: true,
          number: connection.celular,
          sessionId: connection.sessionId || ''
        })
        return connection.celular
      }
      return ''
    } catch (error) {
      return ''
    }
  }

  return {
    connections,
    useDefault,
    companyConnection,
    updateConnection,
    updateUseDefault,
    getEffectiveConnection,
    saveToLocalStorage,
    loadFromLocalStorage,
    clearConnection,
    fetchActiveConnections,
    fetchConnectionByType
  }
})