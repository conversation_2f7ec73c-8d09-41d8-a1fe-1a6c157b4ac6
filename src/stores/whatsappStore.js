import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWhatsAppStore = defineStore('whatsapp', () => {
  // Estado das conexões por tipo
  const connections = ref({
    1: { isConnected: false, number: '', sessionId: '' }, // Empresa
    2: { isConnected: false, number: '', sessionId: '' }, // NFe  
    3: { isConnected: false, number: '', sessionId: '' }  // Financeiro
  })

  // Estado do "usar padrão" por tipo
  const useDefault = ref({
    2: false, // NFe usa padrão da empresa?
    3: false  // Financeiro usa padrão da empresa?
  })

  // Computed para pegar conexão da empresa
  const companyConnection = computed(() => connections.value[1])

  // Função para atualizar conexão
  function updateConnection(type, connectionData) {
    connections.value[type] = { ...connections.value[type], ...connectionData }
    saveToLocalStorage()
  }

  // Função para atualizar "usar padrão"
  function updateUseDefault(type, value) {
    useDefault.value[type] = value
    saveToLocalStorage()
  }

  // Função para obter conexão efetiva (considerando "usar padrão")
  function getEffectiveConnection(type) {
    if (type === 1) {
      return connections.value[1]
    }
    
    if (useDefault.value[type] && connections.value[1].isConnected) {
      return connections.value[1]
    }
    
    return connections.value[type]
  }

  // Salvar no localStorage
  function saveToLocalStorage() {
    localStorage.setItem('whatsapp-store', JSON.stringify({
      connections: connections.value,
      useDefault: useDefault.value
    }))
  }

  // Carregar do localStorage
  function loadFromLocalStorage() {
    const stored = localStorage.getItem('whatsapp-store')
    if (stored) {
      const data = JSON.parse(stored)
      connections.value = data.connections || connections.value
      useDefault.value = data.useDefault || useDefault.value
    }
  }

  // Limpar conexão
  function clearConnection(type) {
    connections.value[type] = { isConnected: false, number: '', sessionId: '' }
    saveToLocalStorage()
  }

  return {
    connections,
    useDefault,
    companyConnection,
    updateConnection,
    updateUseDefault,
    getEffectiveConnection,
    saveToLocalStorage,
    loadFromLocalStorage,
    clearConnection
  }
})