<template>
  <q-input
    @click="handleInputClick"
    ref="inputRef"
    :required="props.required"
    stack-label
    :borderless="props.borderless"
    v-model="formattedValue"
    @update:model-value="
      (novoValor) => {
        if (novoValor.includes('-') && !props.allowNegative) formattedValue = 0;
      }
    "
    :label-slot="props.label !== null"
    :bottom-slots="props.bottomSlots ?? undefined"
    :input-class="props.inputClass"
    :label="props.label ?? undefined"
    :rules="props.moneyRules"
    :readonly="props.disabled || props.readonly"
    :class="
      props.disabled && !props.textTooltip ? 'tw-pointer-events-none' : ''
    "
  >
    <template v-if="slots.hint" #hint>
      <slot name="hint"></slot>
    </template>
    <template v-if="slots.prepend" #prepend>
      <slot name="prepend"></slot>
    </template>
    <TooltipCustom
      v-if="props.textTooltip"
      :text-tooltip="props.textTooltip"
    ></TooltipCustom>
  </q-input>
</template>

<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { useSlots, watch } from 'vue';
import { useCurrencyInput } from 'vue-currency-input';

const slots = useSlots();

const props = defineProps({
  modelValue: {
    type: [Number, String, null],
    required: true,
    default: 0
  },
  bottomSlots: { type: Boolean, required: false, default: null },
  inputClass: { type: String, required: false, default: 'tw-text-right' },
  required: { type: Boolean, required: false, default: false },
  label: { type: [String, null], required: false, default: null },
  borderless: { type: Boolean, required: false, default: false },
  moneyRules: { type: Array, default: () => [] },
  disabled: { type: Boolean, required: false, default: false },
  readonly: { type: Boolean, required: false, default: false },
  decimalsQuantity: { type: [Number, String], required: false, default: 2 },
  allowNegative: { type: Boolean, required: false, default: false },
  autoDecimalDigits: { type: Boolean, required: false, default: false },
  max: { type: Number, required: false, default: +Infinity },
  min: { type: Number, required: false, default: -Infinity },
  textTooltip: { type: String, required: false, default: null },
  currencyDisplay: { type: [String, null], required: false, default: 'hidden' }
});

const { inputRef, formattedValue, setValue } = useCurrencyInput({
  currency: 'BRL',
  locale: 'pt-BR',
  hideCurrencySymbolOnFocus: false,
  hideGroupingSeparatorOnFocus: false,
  autoDecimalDigits: props.autoDecimalDigits,
  currencyDisplay: props.currencyDisplay,
  precision: props.decimalsQuantity,
  valueRange: {
    min: props.allowNegative ? -props.max : props.min ? props.min : 0,
    max: props.max
  }
});

const validateInput = () => {
  inputRef.value?.validate();
};

const focus = () => {
  inputRef.value?.focus();
};

const handleInputClick = () => {
  inputRef.value?.select();
};

const getRef = () => {
  return inputRef.value;
};

defineExpose({
  validateInput,
  focus,
  getRef
});

watch(
  () => props.modelValue,
  (value) => {
    setValue(value);
  }
);
</script>
