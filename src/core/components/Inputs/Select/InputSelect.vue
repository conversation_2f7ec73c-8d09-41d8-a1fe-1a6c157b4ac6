<template>
  <q-select
    ref="selectRef"
    class="refactSelect"
    :loading="isFetching"
    :required="attributes?.required || props?.required"
    :hide-selected="(attributes?.options ?? options)?.length === 0"
    v-model="model"
    popup-content-class="select-menu-class"
    :clearable="clearable"
    @update:model-value="onModelChange"
    @popup-show="handleShowPopup"
    @popup-hide="handleHidePopup"
    @input-value="handleInputValueChange"
    @clear="onClear"
    :options="filtered_options"
    :readonly="readonly || null"
    :disabled="disabled || null"
  >
    <template v-if="!attributes.multiple" #selected-item="{ opt }">
      <div class="tw-w-full tw-max-w-[95%] tw-overflow-hidden tw-text-ellipsis">
        <div
          class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-nowrap"
          :class="{ 'tw-pt-3': !noLabel, 'tw-pt-1': noLabel && $attrs.label }"
        >
          <template v-if="typeof opt === 'string'">
            {{ opt }}
            <TooltipCustom :text-tooltip="opt ?? null" />
          </template>
          <template v-else-if="typeof attributes['option-label'] === 'string'">
            {{ opt[attributes['option-label']] }}
            <TooltipCustom
              :text-tooltip="opt[attributes['option-label']] ?? null"
            />
          </template>
          <template v-else>
            {{ attributes['option-label'](opt) }}
            <TooltipCustom
              :text-tooltip="attributes['option-label'](opt) ?? null"
            />
          </template>
        </div>
      </div>
    </template>

    <template
      v-if="params?.optionDescription || params?.optionDetails"
      #option="{ itemProps, opt, selected, toggleOption, index }"
    >
      <q-infinite-scroll
        @load="onLoad"
        :offset="params?.optionDetails ? 200 : 100"
      >
        <q-item
          v-bind="itemProps"
          class="tw-flex tw-h-fit tw-flex-row tw-items-center tw-justify-between !tw-py-2"
          :class="{
            'tw-bg-[#f5f5f5]': index % 2 === 0,
            '!tw-opacity-1':
              itemProps.focused || itemProps['aria-selected'] == 'true'
          }"
        >
          <div
            class="!tw-flex !tw-w-[99%] tw-flex-col tw-items-start tw-justify-center"
          >
            <q-checkbox
              dense
              size="32px"
              :model-value="selected"
              v-if="attributes.multiple"
              @update:model-value="onCheckboxClick(opt, toggleOption)"
            />
            <p
              class="!tw-w-[90%] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap tw-text-textsSGBR-gray"
              :class="{
                'tw-bg-[#f5f5f5]': index % 2 === 0,
                '!tw-text-SGBRBlueLighten': itemProps['aria-selected'] == 'true'
              }"
            >
              <template v-if="typeof opt === 'string'">
                {{ opt }}
                <TooltipCustom :text-tooltip="opt ?? null" />
              </template>
              <template
                v-else-if="typeof attributes['option-label'] === 'string'"
              >
                {{ opt[attributes['option-label']] }}
                <TooltipCustom
                  :text-tooltip="opt[attributes['option-label']] ?? null"
                />
              </template>
              <template v-else>
                {{ attributes['option-label'](opt) }}
                <TooltipCustom
                  :text-tooltip="attributes['option-label'](opt) ?? null"
                />
              </template>
            </p>
            <p
              class="!tw-w-[90%] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap tw-pl-2 tw-text-[10px] tw-text-textsSGBR-gray"
              :class="{
                'tw-bg-[#f5f5f5]': index % 2 === 0,
                '!tw-text-SGBRBlueLighten': itemProps['aria-selected'] == 'true'
              }"
            >
              {{ params?.optionDescription(opt) }}
              <TooltipCustom
                :text-tooltip="params?.optionDescription(opt) ?? null"
              />
            </p>
            <p
              class="!tw-w-[90%] tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap tw-pl-2 tw-text-[10px] tw-text-textsSGBR-gray"
              :class="{
                'tw-bg-[#f5f5f5]': index % 2 === 0,
                '!tw-text-SGBRBlueLighten': itemProps['aria-selected'] == 'true'
              }"
            >
              {{ params?.optionDetails(opt) }}
              <TooltipCustom
                :text-tooltip="params?.optionDetails(opt) ?? null"
              />
            </p>
          </div>
          <q-btn
            v-authorized="
              props.rolePrefix ? `${props.rolePrefix}:EDITAR` : props.editRole
            "
            v-if="attributes.editButton || editButton"
            padding="2px"
            size="8px"
            color="primary"
            flat
            dense
            @click="onEditClick($event, opt?.controle)"
          >
            <EditIcon />
            <TooltipCustom text-tooltip="Editar registro" />
          </q-btn>
        </q-item>
      </q-infinite-scroll>
    </template>

    <template
      v-else
      #option="{ itemProps, opt, selected, toggleOption, index }"
    >
      <q-infinite-scroll @load="onLoad" :offset="100">
        <q-item
          v-bind="itemProps"
          class="tw-flex tw-h-fit tw-flex-row tw-items-center tw-justify-between"
          :class="[
            {
              'tw-bg-[#f5f5f5]': index % 2 === 0,
              '!tw-opacity-1':
                itemProps.focused || itemProps['aria-selected'] == 'true'
            },
            $slots.customMoreInfo ? 'tw-flex tw-h-fit tw-flex-col' : '!tw-h-6'
          ]"
          @click="emits('selected', opt)"
        >
          <div class="!tw-flex !tw-w-[99%] tw-items-center tw-gap-2">
            <q-checkbox
              dense
              size="32px"
              :model-value="selected"
              v-if="attributes.multiple"
              @update:model-value="onCheckboxClick(opt, toggleOption)"
            />
            <p
              class="tw-overflow-hidden tw-text-ellipsis tw-whitespace-normal tw-text-nowrap tw-text-textsSGBR-gray"
              :class="{
                'tw-bg-[#f5f5f5]': index % 2 === 0,
                '!tw-text-SGBRBlueLighten': itemProps['aria-selected'] == 'true'
              }"
            >
              <template v-if="typeof opt === 'string'"
                >{{ opt }}
                <TooltipCustom :text-tooltip="opt ?? null" />
              </template>
              <template
                v-else-if="typeof attributes['option-label'] === 'string'"
                >{{ opt[attributes['option-label']] }}

                <TooltipCustom
                  :text-tooltip="opt[attributes['option-label']] ?? null"
                />
              </template>
              <template v-else>
                {{ attributes['option-label'](opt) }}
                <TooltipCustom
                  :text-tooltip="attributes['option-label'](opt) ?? null"
                />
              </template>
            </p>
          </div>
          <q-btn
            v-authorized="
              props.rolePrefix ? `${props.rolePrefix}:EDITAR` : props.editRole
            "
            v-if="attributes.editButton || editButton"
            padding="2px"
            size="8px"
            color="primary"
            flat
            dense
            @click="
              (ev) => {
                ev.preventDefault();
                ev.stopPropagation();
                emits('onEdit', opt?.controle);
              }
            "
          >
            <EditIcon />
            <TooltipCustom text-tooltip="Editar registro" />
          </q-btn>
          <slot name="customMoreInfo" :data="opt"></slot>
        </q-item>
      </q-infinite-scroll>
    </template>

    <template v-if="attributes.addButton ?? addButton" #after-options>
      <div
        v-authorized="
          props.rolePrefix ? `${props.rolePrefix}:CADASTRAR` : props.addRole
        "
        class="tw-sticky tw-bottom-0 tw-border-t-2 tw-border-gray-300"
      >
        <q-btn
          color="primary"
          label="+ Adicionar"
          flat
          class="tw-sticky tw-w-full tw-items-start tw-bg-white"
          @click="emits('onAdd')"
        />
      </div>
    </template>
    <template #[slotTemplate]>
      <div class="tw-sticky tw-top-0 tw-z-[1] tw-bg-white">
        <q-input
          ref="searchRef"
          color="primary"
          flat
          dense
          debounce="500"
          placeholder="Pesquisar"
          v-model="selectInput[0].filterText"
          @update:model-value="
            (text) => handleInputValueChange(text, 0, filterOr)
          "
          v-if="canSearch"
        >
          <template #prepend>
            <q-icon name="search" size="16px" class="tw-ml-1" />
          </template>
          <template #append>
            <div></div>
          </template>
        </q-input>
      </div>
      <div v-if="!results && loading.table" class="tw-w-full">
        <div class="q-item tw-flex tw-items-center">
          <p
            class="tw-w-full tw-text-start tw-font-bold tw-text-SGBRBlueLighten"
          >
            Carregando...
          </p>
        </div>
      </div>
      <div v-if="!results && !loading.table" class="tw-w-full">
        <div class="q-item tw-flex tw-items-center">
          <p
            class="tw-w-full tw-text-start tw-font-bold tw-text-SGBRBlueLighten"
          >
            Nada encontrado...
          </p>
        </div>
        <div
          class="tw-sticky tw-bottom-0 tw-border-t-2 tw-border-gray-300"
          v-if="attributes.addButton ?? addButton"
          v-authorized="`${props.rolePrefix}:CADASTRAR`"
        >
          <q-btn
            color="primary"
            label="+ Adicionar"
            flat
            class="tw-sticky tw-w-full tw-items-start tw-bg-white"
            @click="$emit('onAdd')"
          />
        </div>
      </div>
    </template>

    <template v-if="attributes.fixedPlaceholder && model == null" #before>
      <span
        class="fixedPlaceholder tw-pointer-events-none tw-absolute tw-left-[13px] tw-top-[22px] tw-z-10"
      >
        {{ attributes.fixedPlaceholder }}
      </span>
    </template>
  </q-select>
</template>

<script setup>
/* eslint-disable */
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import _ from 'lodash';
import { useSplitAttrs } from 'quasar';
import { api, api_global } from 'src/boot/axios';
import EditIcon from 'src/components/icons/EditIcon.vue';
import handleApiErrors from 'src/services/handleApiErrors';
import {
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  toRefs,
  watch,
  reactive,
  computed
} from 'vue';
import { normalizeText } from 'src/services/utils';

const props = defineProps({
  params: {
    type: [Object, null],
    required: true
  },
  addButton: {
    type: [Boolean, null],
    required: false,
    default: false
  },
  editButton: {
    type: [Boolean, null],
    required: false,
    default: false
  },
  refetchOnChange: {
    type: [Boolean, null],
    required: false,
    default: false
  },
  clearable: {
    type: [Boolean, null],
    required: false,
    default: true
  },
  rolePrefix: {
    type: String,
    required: false
  },
  addRole: {
    type: [String, Boolean],
    required: false,
    default: false
  },
  editRole: {
    type: [String, Boolean],
    required: false,
    default: false
  },
  relationships: {
    type: [Array, String],
    required: false,
    defautl: () => []
  },
  noLabel: {
    type: [Boolean, null],
    required: false,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  canSearch: {
    type: Boolean,
    default: false
  }
});

const { attributes } = useSplitAttrs();
const { params, addButton, editButton, refetchOnChange, clearable, noLabel } =
  toRefs(props);

const emits = defineEmits([
  'ref',
  'update:modelValue',
  'onAdd',
  'onEdit',
  'updatedRaw',
  'selected'
]);

const model = defineModel({
  type: [Object, String, Number, null, Boolean],
  required: true,
  default: () => []
});

const isPopupOpened = ref(false);
const searchFilter = ref(params.value?.filtersV2 ? [] : {});
const orderBy = ref(params.value?.orderBy);
const rowsPerPage = ref(params.value?.rowsPerPage ?? 20);
const filterOr = ref(params.value['filter-or'] ?? params.value.filterOr ?? []);
const relationships = ref(
  params.value.relationships ?? props.relationships ?? []
);

const filterBy = attributes.value?.['filter-by'];
const emitValue = attributes.value?.['emit-value'];
const optionValue = attributes.value?.['option-value'];
const optionLabel = attributes.value?.['option-label'];

const currentPage = ref(1);
const lastPage = ref(1);
const selectRef = ref();
const page = ref(1);
const isFetching = ref(false);
const options = ref([]);

async function onModelChange() {
  // const obj = findOption(model.value) || null;
  // emits('selected', obj);

  selectRef.value?.updateInputValue('');
  selectRef.value?.blur();
}

const onLoad = (_, done) => updateScroll(done);
const onCheckboxClick = (opt, toggleOption) => toggleOption(opt);
const onEditClick = (ev, controle) => {
  ev.preventDefault();
  ev.stopPropagation();
  emits('onEdit', controle);
};
const selectInput = reactive([
  {
    filterText: '',
    filteredSelectOptions: []
  }
]);

const loading = ref({
  table: false
});
const filtered_options = computed(() => {
  return selectInput[0].filterText &&
    selectInput[0].filteredSelectOptions.length > 0
    ? selectInput[0].filteredSelectOptions
    : options.value;
});

const results = computed(() => {
  return (
    options.value.length > 0 ||
    (selectInput[0].filterText &&
      selectInput[0].filteredSelectOptions.length > 0)
  );
});

const slotTemplate = computed(() =>
  options.value.length > 0 ? 'before-options' : 'no-option'
);
/**
 * Função assíncrona para buscar opções do componente de seleção.
 * @param {Object} options - Opções para a busca.
 * @param {number} options.pageParam - Parâmetro da página para a busca (padrão: 1).
 * @returns {Object} - Objeto contendo os dados da página e informações adicionais.
 * @throws {Error} - Erro caso ocorra algum problema na busca.
 */
const fetchOptions = async (remount = false) => {
  if (!params.value?.apiRoute) return;
  isFetching.value = true;

  try {
    // Filtros e ordenação
    let requestParams = {
      page: page.value,
      paginate: rowsPerPage.value
    };

    const hasFiltersV2 = params.value?.filtersV2 != null;

    if (hasFiltersV2) {
      requestParams = {
        ...requestParams,
        filtersV2: [...params.value?.filtersV2, ...searchFilter.value],
        relationships: relationships.value,
        ...params.value?.extraParameters
      };

      if (
        (model.value?.length || Number.isInteger(model.value)) &&
        !options.value?.length &&
        !attributes.value['option-value-rename']
      ) {
        requestParams.filtersV2 = [
          ...requestParams?.filtersV2,
          {
            field: emitValue ? optionValue : optionLabel,
            filterType: 'FIXED',
            filterValue: model.value?.controle || model.value
          }
        ];
      }
    } else {
      requestParams = {
        ...requestParams,
        filters: {
          deleted_at: {
            filterType: null
          },
          ...searchFilter.value,
          ...params.value?.filters,
          relationships: relationships.value
        },
        ...params.value?.extraParameters
      };

      if (
        model.value?.length &&
        !options.value?.length &&
        !attributes.value['option-value-rename'] &&
        Object.values(searchFilter.value).length === 0
      ) {
        requestParams.filters = {
          [emitValue ? optionValue : optionLabel]: {
            filterType: 'FIXED',
            filterValue: model.value?.controle || model.value
          },
          ...requestParams.filters
        };
      }
    }
    if (orderBy.value) {
      requestParams.orderBy = orderBy.value;
    }

    // if (!model.value && hasFiltersV2) {
    //  delete requestParams.filtersV2.controle
    // }

    // Request
    const API = params.value?.central ? api_global : api;
    const response = await API.get(params.value.apiRoute, {
      params: requestParams
    });
    const { data, fixed } = response.data;

    // Paginação
    currentPage.value = Number(response.data?.currentPage);
    lastPage.value = Number(response.data?.lastPage);

    // Dados
    let emit = false;
    if (fixed) {
      data.unshift(fixed);
      emit = true;
    }

    // Renomeação em caso de multiplos.
    const rename = attributes.value?.['option-value-rename'];
    let tempOptions = rename ? renameOptionValue(data, rename) : data;
    options.value = remount ? tempOptions : options.value.concat(tempOptions);

    if (emit) emits('updatedRaw', fixed);
  } catch (error) {
    handleApiErrors(error);
  } finally {
    isFetching.value = false;
  }
};

/**
 * Função responsável pela requisição do scroll infinito.
 * @param {Function} done - Função de callback que indica se a atualização do scroll foi concluída.
 * @returns {Promise<void>}
 */
const updateScroll = async (done) => {
  if (
    params.value?.apiRoute &&
    currentPage.value < lastPage.value &&
    !isFetching.value
  ) {
    page.value++;
    await fetchOptions();
    done(true);
  }
  done(false);
};

/**
 * Função que realiza uma determinada tarefa.
 * @param {string} string - String que representa o que é digitado no campo.
 * @returns {Promise<void>}
 */
const handleInputValueChange = async (string) => {
  searchRef.value?.focus();
  await nextTick();

  if (!string) {
    searchFilter.value = params.value?.filtersV2 ? [] : {};
    if (params.value?.apiRoute) await reMount();
    else options.value = params?.value?.optionsStatic;
    return;
  }

  if (params.value?.apiRoute) {
    const searchString = string?.toLowerCase();
    // filterOr soberano ao filterBy
    let newFilter = null;

    if (filterOr.value?.length) {
      newFilter = params.value?.filtersV2
        ? [
            {
              operator: 'AND',
              group: filterOr.value.map((param, key) => {
                let newParams = {
                  field: param,
                  filterType: 'ILIKE',
                  filterValue: searchString
                };

                if (key > 0) {
                  newParams.operator = 'OR';
                }

                return newParams;
              })
            }
          ]
        : {
            [filterOr.value[0]]: {
              filterType: 'ILIKE_OR',
              filterValue: {
                fields: filterOr.value.map((filter) => filter.toLowerCase()),
                value: searchString
              }
            }
          };
    } else {
      newFilter = params.value?.filtersV2
        ? [
            {
              field: filterBy ?? optionLabel,
              filterType: 'ILIKE',
              filterValue: searchString
            }
          ]
        : {
            [filterBy ?? optionLabel]: {
              filterType: 'ILIKE',
              filterValue: searchString
            }
          };
    }

    searchFilter.value = newFilter;
    selectRef.value.showPopup();
    await reMount(true);
    return;
  }

  // TODO: melhorar normalização da string para select estáticos (optionsStatic)
  const searchString = normalizeText(string);
  options.value = params?.value?.optionsStatic.filter((opt) => {
    const optionValue = normalizeText(String(opt[optionLabel] || ''));
    return optionValue.includes(searchString || '');
  });
};

const renameOptionValue = (options, rename) => {
  return options.map((item) => {
    let itemMap = item;
    const optionValue = attributes.value['option-value'];
    itemMap = {
      ...itemMap,
      [optionValue]: item[rename]
    };
    delete itemMap[rename];
    return itemMap;
  });
};
const searchRef = ref(null);
watch(searchRef, (newValue) => {
  if (newValue) {
    searchRef.value.$el.addEventListener('keydown', (event) => {
      const offset = event.key === 'ArrowUp' ? -1 : 1;
      if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
        selectRef.value?.moveOptionSelection(offset);
        const index = selectRef.value?.getOptionIndex();
        selectRef.value?.scrollTo(index);
      }
      if (event.key === 'Enter') {
        const index = selectRef.value?.getOptionIndex();
        if (index || index === 0)
          selectRef.value?.toggleOption(filtered_options.value[index]);
      }
    });
  }
});

const handleShowPopup = async () => {
  const { disabled, readonly } = attributes.value;
  if (disabled || readonly) {
    selectRef.value.hidePopup();
    return;
  }
  selectInput[0].filterText = '';
  isPopupOpened.value = true;

  await nextTick();
  searchRef.value?.$el?.focus();
};

const handleHidePopup = () => {
  isPopupOpened.value = false;
  searchFilter.value = params.value?.filtersV2 ? [] : {};
  selectRef.value.blur();
  selectRef.value.$el.querySelector('input').blur();
  selectRef.value.updateInputValue('');
  selectRef.value.hidePopup();
  reMount();
};

const reMount = async (clear = true) => {
  page.value = 1;
  searchRef.value?.$el?.blur();
  await fetchOptions(true);
};

const onClear = () => {
  searchFilter.value = params.value?.filtersV2 ? [] : {};
  if (isPopupOpened.value) reMount(true);
};
const setModel = (value) => {
  model.value = value;
};

// Funções a serem acessadas pela ref.
defineExpose({
  reMount,
  emitRaw,
  setModel,
  ref: selectRef
});

const scrollEvent = ref();
onMounted(async () => {
  const dialog = document.querySelector('.sgbrDialogs');
  if (dialog) {
    scrollEvent.value = dialog?.addEventListener('scroll', () => {
      selectRef.value?.updateMenuPosition();
    });
  }

  if (params.value?.apiRoute && !refetchOnChange.value)
    return await fetchOptions();
  options.value = params.value?.optionsStatic || [];
});

onBeforeUnmount(() => {
  const dialog = document.querySelector('.sgbrDialogs');
  if (dialog && scrollEvent.value) {
    dialog.removeEventListener('scroll', scrollEvent.value);
  }
});

// A qualquer mudança em um filtro reativo, será remontado
watch(
  () => params.value?.filtersV2 ?? params.value?.filters,
  (newFilters, oldFilters) => {
    if (!_.isEqual(newFilters, oldFilters)) reMount();
  }
);

const findOption = (value) => {
  if (!value) return null;
  return options.value?.find(
    (option) => option[optionValue] == value || option == value
  );
};

function emitRaw(value) {
  if (!value) {
    emits('updatedRaw', null);
    return;
  }

  if (options.value?.length) {
    const obj = findOption(value);
    if (!obj) {
      emits('updatedRaw', null);
      return;
    }

    emits('updatedRaw', obj);
  }
}

// Lida com backspace no input tornando null a variavel.
const focusOnInputIfNull = (newModel) => {
  if (newModel === null) searchRef.value?.focus();
};

watch(
  () => isFetching.value,
  (newVal) => {
    searchRef.value?.$el?.focus();
  }
);

// Alterações externas no model serão emitidas no updatedRaw.
watch(
  () => model?.value,
  (newModel) => {
    focusOnInputIfNull(newModel);
    if (selectRef.value) emitRaw(newModel);
    if (refetchOnChange.value && selectRef.value) {
      options.value = [];
      reMount();
    }
  },
  {
    immediate: true
  }
);
</script>
<style>
.select-menu-class {
  height: 'fit-content';
  width: 50px;
  max-height: 280px !important;
}

.q-field__before:has(.fixedPlaceholder) {
  padding: 0 !important;
}

.q-field--highlighted .q-field__before:has(.fixedPlaceholder) {
  display: none;
}
</style>
