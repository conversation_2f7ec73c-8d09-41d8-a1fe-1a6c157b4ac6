<template>
  <component ref="sortableContainerRef" :is="$props.tag" :class="$props.class">
    <slot name="header"></slot>
    <slot
      v-for="(item, index) of list"
      :key="getKey(item)"
      :element="item"
      :index="index"
      name="item"
    ></slot>
    <slot name="footer"></slot>
  </component>
</template>

<script setup>
import Sortable from 'sortablejs';
import { onUnmounted, ref, useAttrs, watch } from 'vue';

const props = defineProps({
  options: {
    type: Object,
    default: null,
    required: false
  },
  list: {
    type: [Array, Object],
    default: () => [],
    required: true
  },
  itemKey: {
    type: [String, Function],
    default: '',
    required: true
  },
  tag: {
    type: String,
    default: 'div',
    required: false
  }
});

const emit = defineEmits([
  'choose',
  'unchoose',
  'start',
  'end',
  'add',
  'update',
  'sort',
  'remove',
  'filter',
  'move',
  'clone',
  'change'
]);

const attrs = useAttrs();
const isDragging = ref(false);
const sortableContainerRef = ref(null);
const sortable = ref(null);
const getKey = (item) => {
  if (typeof props.itemKey === 'function') {
    return props.itemKey(item);
  }

  if (typeof props.itemKey === 'string') item[props.itemKey];
  return props.itemKey;
};

defineExpose({
  sortableContainerRef,
  sortable,
  isDragging
});

watch(sortableContainerRef, (newDraggable) => {
  if (newDraggable) {
    sortable.value = new Sortable(newDraggable, {
      ...props.options,
      onChoose: (event) => emit('choose', event),
      onUnchoose: (event) => emit('unchoose', event),
      onStart: (event) => {
        isDragging.value = true;
        emit('start', event);
      },
      onEnd: (event) => {
        setTimeout(() => {
          isDragging.value = false;
          emit('end', { ...event, list: [...props.list] });
        });
      },
      onAdd: (event) => emit('add', event),
      onUpdate: (event) => emit('update', event),
      onSort: (event) => emit('sort', event),
      onRemove: (event) => emit('remove', event),
      onFilter: (event) => emit('filter', event),
      onMove: (event, originalEvent) =>
        'onMoveCapture' in attrs
          ? attrs.onMoveCapture(event, originalEvent)
          : emit('move', event, originalEvent),
      onClone: (event) => emit('clone', event),
      onChange: (event) => emit('change', event)
    });
  }
});

watch(
  () => props.options,
  (options) => {
    if (options && sortable?.value) {
      for (const property in options) {
        sortable.value.option(property, options[property]);
      }
    }
  }
);

onUnmounted(() => {
  if (sortable.value) {
    sortable.value.destroy();
    sortableContainerRef.value = null;
    sortable.value = null;
  }
});
</script>
