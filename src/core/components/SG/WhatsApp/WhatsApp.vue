<template>
  <q-checkbox
    v-if="props.showDefault"
    @update:model-value="changeUseDefault"
    v-model="fields.padraoWhatsApp.value"
    :error="!!fields.padraoWhatsApp.errorMessage"
    :error-message="fields.padraoWhatsApp.errorMessage"
    :valid="fields.padraoWhatsApp.meta.valid"
    :disable="readonlyFields || isConnected"
    label="Usar padrão da empresa"
    class="tw-col-span-full tw-mb-3 tw-w-fit"
    size="2rem"
    dense
  />
  <div
    v-if="props.showDefault && fields.padraoWhatsApp.value && defaultCompanyNumber.value"
    class="tw-col-span-full tw-mb-3"
  >
    <p class="tw-text-sm tw-text-gray-600">WhatsApp padrão: {{ defaultCompanyNumber.value }}</p>
  </div>
  <div
    v-if="!isConnected"
    class="tw-col-span-full tw-flex tw-justify-center sm:tw-justify-start"
  >
    <q-btn
      @click="showWhatsAppModal"
      :disable="
        props.alwaysEnableConnect
          ? false
          : fields.padraoWhatsApp?.value === true ||
            (isConnected && !fields.padraoWhatsApp)
      "
      :loading="isConnecting"
      color="positive"
      dense
      unelevated
      no-caps
      class="tw-w-[230px]"
    >
      <p class="tw-text-inherit">Conectar com o WhatsApp</p>
    </q-btn>
  </div>

  <div
    v-if="isConnected"
    class="tw-col-span-full tw-flex tw-w-full tw-items-center tw-justify-between"
  >
    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-h-3 tw-w-3 tw-rounded-full tw-bg-green-500"></div>
      <p class="tw-m-0 tw-text-start">{{ connectedNumber }}</p>
    </div>

    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-mt-2 tw-flex tw-w-full tw-flex-wrap tw-gap-2">
        <q-btn
          v-if="props.showSendMessageButton"
          @click="() => (modalHtml = true)"
          color="positive"
          icon="send"
          dense
          round
          size="sm"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-green-500 !tw-shadow-[0_0_0_0]"
        >
          <TooltipCustom text-tooltip="Enviar mensagem" />
        </q-btn>

        <q-btn
          v-if="!props.showOnlySendButton || props.showOnlyDisconnectButton"
          @click="disconnectWhatsApp"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-red-500 !tw-shadow-[0_0_0_0]"
          round
          size="sm"
          color="primary"
          icon="logout"
          :loading="isDisconnecting"
        >
          <TooltipCustom text-tooltip="Desconectar" />
        </q-btn>
      </div>
      <HtmlEditor
        v-if="modalHtml"
        v-model="modalHtml"
        :html-editor-model="userTemplate"
        @on-save="handleSaveAndSend"
        />
    </div>
  </div>
</template>
<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { ref, toRefs, defineModel, watch, onMounted } from 'vue';
import WhatsAppModal from 'src/core/components/SG/WhatsApp/WhatsAppModal.vue';
import { useQuasar } from 'quasar';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { api } from 'src/boot/axios';
import HtmlEditor from 'components/ConfigEmpresa/Email/HtmlEditor.vue';
import { userTemplate } from 'components/ConfigEmpresa/Email/HtmlTemplates/userTemplate.js';
import { defineExpose } from 'vue';

// Adicionar no início do script
import { useWhatsAppStore } from 'src/stores/whatsappStore'
import { storeToRefs } from 'pinia'

// Adicionar após as outras declarações
const whatsappStore = useWhatsAppStore()
const { connections, useDefault } = storeToRefs(whatsappStore)
//Props
const props = defineProps({
  showDefault: {
    type: Boolean,
    default: false
  },
  errors: {
    type: Object,
    required: true
  },
  readonlyFields: {
    type: Boolean,
    default: false
  },
  showOnlySendButton: {
    type: Boolean,
    default: false
  },
  showSendMessageButton: {
    type: Boolean,
    default: false
  },
  showOnlyDisconnectButton: {
    type: Boolean,
    default: false
  },
  alwaysEnableConnect: {
    type: Boolean,
    default: false
  },
  configType: {
    type: Number, // 1 = padrao, 2 = venda, 3 = financeiro
    required: true
  }
});

const modalHtml = ref(false);

//Global
const $q = useQuasar();

const fields = defineModel('fields', { type: Object, required: true });
const { readonlyFields } = toRefs(props);

// Estados da conexão WhatsApp
const isConnecting = ref(false);
const isConnected = ref(false);
const connectedNumber = ref('');
const qrCodeValue = ref('');
const qrBase64 = ref('');
const isDisconnecting = ref(false);
const showQrCode = ref(false);
const whatsappDialogRef = ref(null);
const isShowWhatsAppModalOpen = ref(false);
const lastConnectionError = ref('');
const qrCheckInterval = ref(null);
const sessionId = ref('');
const isSendingMessage = ref(false);

const defaultCompanyNumber = ref('');
async function deleteWhatsAppConfig() {
  console.log('Iniciando processo de deleção da configuração do WhatsApp...');
  try {
  // Constrói a URL com o tipo diretamente no caminho
  await api.delete(`/api/whatsapp/configuracoes/${props.configType}`);

  // Limpa o controle após deletar a configuração
  if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }
  } catch (error) {
    console.error('Erro ao deletar configuração do WhatsApp:', error);
  }
}
// NOVO: Função para buscar uma configuração de WhatsApp por tipo
async function fetchWhatsAppConfig(tipo) {
  try {

    const response = await api.get(`/api/whatsapp/configuracoes`, { params: { tipo } });
    if (response.data && response.data.length > 0) {
      // Retorna o primeiro número encontrado para aquele tipo
      return response.data[0].celular;
    }
    return '';
  } catch (error) {
    console.error(`Erro ao buscar configuração do WhatsApp (tipo ${tipo}):`, error);
    return '';
  }
}

// Watch para garantir que o checkbox inicie desmarcado
watch(() => fields.value?.padraoWhatsApp?.value, (newValue) => {
  if (newValue === undefined || newValue === null) {
    fields.value.padraoWhatsApp.value = false;
  }
}, { immediate: true });

// NOVO: Função para salvar a configuração do WhatsApp
async function saveWhatsAppConfig(celular) {
  try {
    const payload = {
      tipo: props.configType,
      celular: celular
    };
    await api.post('/api/whatsapp/configuracoes/', payload);
    console.log(`Configuração do WhatsApp (tipo ${props.configType}) salva com sucesso!`);
  } catch (error) {
    console.error('Erro ao salvar configuração do WhatsApp:', error);
    $q.notify({
      type: 'negative',
      position: 'top',
      message: 'Não foi possível salvar a configuração do WhatsApp.',
    });
  }
}


onMounted(async () => {
  console.log(`Iniciando onMounted para tipo ${props.configType}`)

  // Carregar estado do store
  whatsappStore.loadFromLocalStorage()

  // Se não for empresa (tipo 1), carregar número padrão da empresa PRIMEIRO
  if (props.configType !== 1) {
    await loadDefaultCompanyNumber()
  }

  // Depois verificar conexão existente (que agora respeita o checkbox)
  await verificarConexaoExistente()

  console.log(`onMounted finalizado para tipo ${props.configType} - isConnected: ${isConnected.value}, checkbox: ${fields.value.padraoWhatsApp?.value}`)
})

async function loadDefaultCompanyNumber() {
  try {
    const companyNumber = await fetchWhatsAppConfig(1) // Busca config da empresa
    defaultCompanyNumber.value = companyNumber

    // Atualiza o store com a conexão da empresa se ela existir
    if (companyNumber) {
      whatsappStore.updateConnection(1, {
        isConnected: true,
        number: companyNumber
      })
    }

    // APENAS se estiver marcado para usar padrão, mostra a conexão da empresa
    if (fields.value.padraoWhatsApp?.value && companyNumber) {
      isConnected.value = true
      connectedNumber.value = companyNumber
      console.log(`Carregando padrão da empresa para tipo ${props.configType}: ${companyNumber}`)
    }
  } catch (error) {
    console.error('Erro ao carregar número padrão da empresa:', error)
  }
}
async function changeUseDefault(useDefault) {
  // Atualiza o store
  whatsappStore.updateUseDefault(props.configType, useDefault)

  if (useDefault) {
    // APENAS MOSTRA a conexão da empresa (não cria nova conexão)
    const companyConnection = whatsappStore.getEffectiveConnection(1)

    if (companyConnection.isConnected) {
      isConnected.value = true
      connectedNumber.value = companyConnection.number
      console.log(`Usando padrão da empresa para tipo ${props.configType}: ${companyConnection.number}`)
    } else {
      isConnected.value = false
      connectedNumber.value = ''
      $q.notify({
        type: 'negative',
        position: 'top',
        message: 'Nenhum WhatsApp padrão foi configurado para a empresa.',
        icon: 'warning'
      })
    }
  } else {
    // VOLTA para a conexão específica deste módulo
    console.log(`Voltando para conexão específica do tipo ${props.configType}`)
    await verificarConexaoExistente() // Recarrega a conexão própria do módulo
  }
}
async function verificarConexaoExistente() {
  console.log(`Verificando conexão existente para tipo ${props.configType}`)

  // Se for NFe e estiver marcado para usar padrão da empresa, não verifica conexão própria
  if (props.configType !== 1 && fields.value.padraoWhatsApp?.value) {
    console.log(`Tipo ${props.configType} está usando padrão da empresa - não verificando conexão própria`)
    return
  }

  try {
    // Busca na API a conexão específica deste tipo
    const response = await api.get('/api/whatsapp/configuracoes', {
      params: { tipo: props.configType }
    })

    if (response.data && response.data.length > 0) {
      const config = response.data[0]
      isConnected.value = true
      connectedNumber.value = config.celular

      // Atualiza o store com a informação da API
      whatsappStore.updateConnection(props.configType, {
        isConnected: true,
        number: config.celular
      })
      console.log(`Conexão própria encontrada na API para tipo ${props.configType}:`, config.celular)
    } else {
      // Não há conexão própria na API - limpa tudo
      isConnected.value = false
      connectedNumber.value = ''

      // Garante que o store também está limpo
      whatsappStore.clearConnection(props.configType)
      console.log(`Nenhuma conexão própria encontrada para tipo ${props.configType} - limpando store`)
    }
  } catch (error) {
    console.error('Erro ao verificar configuração do WhatsApp:', error)
    isConnected.value = false
    connectedNumber.value = ''

    // Limpa o store em caso de erro
    whatsappStore.clearConnection(props.configType)
  }
}

/**
 * Função para lidar com o salvamento do editor e o envio da mensagem.
 * @param {string} messageContent - O conteúdo HTML/texto do editor.
 */
async function handleSaveAndSend(messageContent) {
  // 1. Opcional: Salva o conteúdo no campo do formulário, mantendo a funcionalidade original.
  if (fields.value.whatsapp?.mensagemPadrao) {
    fields.value.whatsapp.mensagemPadrao.setValue(messageContent);
  }

  // 2. Chama a função para enviar a mensagem.
  await sendMessage(messageContent);

  // 3. Fecha o modal do editor após o envio.
  modalHtml.value = false;
}

/**
 * Envia uma mensagem de WhatsApp através da API.
 * @param {string} htmlMessage - A mensagem no formato HTML vinda do editor.
 */
async function sendMessage(htmlMessage) {
  // --- INÍCIO DA CORREÇÃO ---

  // 1. NOVO: Converte o HTML para texto puro.
  // Criamos um elemento div temporário na memória.
  const tempDiv = document.createElement('div');
  // Inserimos o HTML dentro dele.
  tempDiv.innerHTML = htmlMessage;
  // Extraímos apenas o conteúdo de texto, o que remove todas as tags.
  const plainTextMessage = (tempDiv.textContent || tempDiv.innerText || "").trim();

  // 2. ALTERADO: Validamos o texto puro.
  if (!plainTextMessage) {
    $q.notify({
      type: 'warning',
      position: 'top',
      message: 'A mensagem não pode estar vazia.',
      icon: 'warning'
    });
    return;
  }

  isSendingMessage.value = true;
  $q.notify({
    type: 'info',
    position: 'top',
    message: 'Enviando mensagem...',
    timeout: 1500 // Notificação curta
  });

  try {
    const payload = {
      celular: connectedNumber.value,
      // 3. ALTERADO: Usamos a variável com o texto puro no payload.
      mensagem: plainTextMessage,
      destinatario: connectedNumber.value
    };

    await api.post('/api/whatsapp/api/mensagem', payload);

    $q.notify({
      type: 'positive',
      position: 'top',
      message: 'Mensagem enviada com sucesso!',
      icon: 'check_circle'
    });
  } catch (error) {
    console.error('Erro ao enviar mensagem:', error);
    const validationError = error.response?.data?.errors?.mensagem?.[0];
    const generalMessage = error.response?.data?.message || 'Falha ao enviar a mensagem.';
    $q.notify({
      type: 'negative',
      position: 'top',
      message: validationError || generalMessage, // Dá prioridade ao erro de validação.
      icon: 'error'
    });
  } finally {
    isSendingMessage.value = false;
  }
}

// --- FIM DA NOVA IMPLEMENTAÇÃO ---

// Funções existentes (connect, disconnect, etc.)
watch(isConnected, (newValue) => {
  if (newValue) {
    // if (fields.value?.padraoWhatsApp) {
    //   fields.value.padraoWhatsApp.value = false;
    // }
    qrBase64.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }
  }
});

watch(lastConnectionError, (newValue) => {
  console.log('Erro de conexão:', newValue);
});

// Watcher para sincronização com o store
watch(
  () => {
    try {
      return whatsappStore?.getEffectiveConnection?.(props.configType) || null
    } catch (error) {
      console.warn('Erro ao acessar whatsappStore:', error)
      return null
    }
  },
  (newConnection) => {
    try {
      if (!newConnection || !newConnection.isConnected) {
        // Se não há conexão no store
        if (props.configType === 1) {
          // Se for empresa, sempre limpa
          isConnected.value = false
          connectedNumber.value = ''
        } else if (useDefault.value?.[props.configType]) {
          // Se for outro módulo usando padrão da empresa, limpa também
          isConnected.value = false
          connectedNumber.value = ''
          console.log(`Módulo tipo ${props.configType} perdeu conexão porque empresa desconectou`)
        } else {
          // Se for módulo com conexão própria, não mexe
          console.log(`Módulo tipo ${props.configType} mantém sua própria conexão`)
        }
        return
      }

      // Para Empresa (tipo 1): sempre atualiza
      // Para outros tipos: só atualiza se não estiver usando padrão da empresa
      const shouldUpdate = props.configType === 1 || !useDefault.value?.[props.configType]

      if (shouldUpdate) {
        isConnected.value = Boolean(newConnection.isConnected)
        connectedNumber.value = newConnection.number || ''
        console.log(`Watch atualizou UI para tipo ${props.configType}: ${newConnection.number}`)
      }
    } catch (error) {
      console.warn('Erro no watch do WhatsApp:', error)
    }
  },
  { deep: true, immediate: true }
)
async function showWhatsAppModal() {
  if (isShowWhatsAppModalOpen.value) return;
  isShowWhatsAppModalOpen.value = true;

  const receivedQrBase64 = await connectWhatsApp();

  if (receivedQrBase64) {
    whatsappDialogRef.value = $q
      .dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: WhatsAppModal,
          scope: 'WhatsAppModal',
          hasCancel: true,
          hasSave: false,
          dataModal: {
            qrBase64: receivedQrBase64
          },
          title: 'Conecte-se ao WhatsApp',
          classesTopBox: '!tw-mb-2 lg:tw-w-[450px] tw-text-center',
          descriptionClass: 'tw-w-full tw-break-words tw-whitespace-pre-wrap'
        }
      })
      .onDismiss(() => {
        isShowWhatsAppModalOpen.value = false;
        if (qrCheckInterval.value) {
          clearInterval(qrCheckInterval.value);
          qrCheckInterval.value = null;
        }
      });
  } else {
    isShowWhatsAppModalOpen.value = false;
  }
}

function getTestNumberByType() {
  switch (props.configType) {
    case 1: // Empresa
      return '553491003526'; // Número de teste para empresa
    case 2: // NF-e
      return '5534991745020'; // Número de teste para NF-e
    default:
      return '553490000000'; // Número padrão
  }
}

async function connectWhatsApp() {
  isConnecting.value = true;
  try {
    const celular = getTestNumberByType();
    const response = await api.post('/api/whatsapp/api/conectar', {
      celular: celular
    });

    const { sessionId: newSessionId, qrBase64: newQrBase64 } = response.data;

    sessionId.value = newSessionId;
    qrBase64.value = newQrBase64;
    showQrCode.value = true;

    startStatusCheck();

    return newQrBase64;
  } catch (error) {
    console.error('Erro ao conectar:', error);
    const errorMessage = error.response?.data?.message || 'Erro ao conectar com WhatsApp';
    lastConnectionError.value = errorMessage;
    showQrCode.value = false;

    $q.notify({
      type: 'negative',
      position: 'top',
      message: errorMessage,
      icon: 'error'
    });
    return null;
  } finally {
    isConnecting.value = false;
  }
}


// Verificar status da conexão
async function checkWhatsAppStatus() {
  if (!sessionId.value) return;

  try {
    const celular = getTestNumberByType();
    const response = await api.put('/api/whatsapp/api/status', {
      celular:celular,
      sessionId: sessionId.value
    });

    const session = response.data.sessoes.find(
      (s) => s.sessionId === sessionId.value
    );

    if (!session) {
      console.error('Sessão não encontrada:', sessionId.value);
      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);
      return;
    }

    if (session.isConnected) {
      isConnected.value = true;

      const newConnectedNumber = session.nomeCelular;

      connectedNumber.value = newConnectedNumber;
      // Atualizar o store
        whatsappStore.updateConnection(props.configType, {
          isConnected: true,
          number: newConnectedNumber,
          sessionId: sessionId.value
        })  
      showQrCode.value = false;

      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);

      if (whatsappDialogRef.value) {
        whatsappDialogRef.value.hide();
      }

        // --- INÍCIO DA MODIFICAÇÃO ---
       // NOVO: Salva a configuração após conectar com sucesso
       // APENAS salva se não estiver usando padrão da empresa
       if (!fields.value.padraoWhatsApp?.value) {
         await saveWhatsAppConfig(newConnectedNumber);
         console.log(`Configuração salva para tipo ${props.configType}: ${newConnectedNumber}`)
       } else {
         console.log(`Não salvando configuração - usando padrão da empresa para tipo ${props.configType}`)
       }

      $q.notify({
        type: 'positive',
        position: 'top',
        message: 'WhatsApp conectado com sucesso!',
        timeout: 5000
      });
    }
  } catch (error) {
    console.error('Erro ao verificar status:', error);
  }
}

// Iniciar verificação periódica
function startStatusCheck() {
  if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);

  qrCheckInterval.value = setInterval(() => {
    checkWhatsAppStatus();
  }, 5000);
}

//Desconectar do Wpp
async function disconnectWhatsApp() {
  isDisconnecting.value = true;
  try {
    console.log('Iniciando desconexão do WhatsApp...');
    const celular = getTestNumberByType();  // Usa o número baseado no tipo

    console.log('Número conectado:', connectedNumber.value);

    // Primeiro deleta a configuração específica do módulo
    await deleteWhatsAppConfig();

    console.log('Configuração deletada, agora desconectando sessão...');
    await api.put('/api/whatsapp/api/desconectar', {
      celular: celular
    });

    console.log('Sessão desconectada com sucesso.');

    // Limpa todos os estados locais
    isConnected.value = false;
    connectedNumber.value = '';

    // Se for empresa, limpa o número padrão para outros módulos
    if (props.configType === 1) {
      defaultCompanyNumber.value = '';
    }

    // Não força o desmarcamento do checkbox - deixa o usuário decidir
    // fields.value.padraoWhatsApp.value = false;

    // defaultCompanyNumber.value = ''; // Limpa o número padrão da empresa
    // fields.value.whatsapp.controle = null; // Limpa o controle da configuração

     // Limpa o controle apenas se o objeto whatsapp existir
     if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }

      // Limpa o store apenas para este tipo de configuração
      whatsappStore.clearConnection(props.configType)
      console.log(`Conexão limpa no store para tipo ${props.configType}`)
    qrCodeValue.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }

    console.log('Atualizando UI...');

    $q.notify({
      type: 'positive',
      position: 'top',
      message: 'WhatsApp desconectado com sucesso!',
      timeout: 5000
    });
  } catch (error) {
    console.error('Erro ao desconectar:', error);
    $q.notify({
      type: 'negative',
      message: 'Erro ao desconectar: ' + error.message,
      timeout: 5000
    });
  } finally {
    isDisconnecting.value = false;
    console.log('Processo de desconexão finalizado');
  }
}

defineExpose({
  isConnected,
  connectedNumber,
  changeUseDefault
});
</script>
