<template>
  <q-checkbox
    v-if="props.showDefault"
    @update:model-value="changeUseDefault"
    v-model="fields.padraoWhatsApp.value"
    :error="!!fields.padraoWhatsApp.errorMessage"
    :error-message="fields.padraoWhatsApp.errorMessage"
    :valid="fields.padraoWhatsApp.meta.valid"
    :disable="readonlyFields || shouldDisableCheckbox"
    label="Usar padrão da empresa"
    class="tw-col-span-full tw-mb-3 tw-w-fit"
    size="2rem"
    dense
  />
  <div
    v-if="props.showDefault && fields.padraoWhatsApp.value && defaultCompanyNumber.value"
    class="tw-col-span-full tw-mb-3"
  >
    <p class="tw-text-sm tw-text-gray-600">WhatsApp padrão: {{ defaultCompanyNumber.value }}</p>
  </div>
  <div
    v-if="!isConnected"
    class="tw-col-span-full tw-flex tw-justify-center sm:tw-justify-start"
  >
    <q-btn
      @click="showWhatsAppModal"
      :disable="
        props.alwaysEnableConnect
          ? false
          : fields.padraoWhatsApp?.value === true ||
            (isConnected && !fields.padraoWhatsApp)
      "
      :loading="isConnecting"
      color="positive"
      dense
      unelevated
      no-caps
      class="tw-w-[230px]"
    >
      <p class="tw-text-inherit">Conectar com o WhatsApp</p>
    </q-btn>
  </div>

  <div
    v-if="isConnected"
    class="tw-col-span-full tw-flex tw-w-full tw-items-center tw-justify-between"
  >
    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-h-3 tw-w-3 tw-rounded-full tw-bg-green-500"></div>
      <p class="tw-m-0 tw-text-start">{{ connectedNumber }}</p>
    </div>

    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-mt-2 tw-flex tw-w-full tw-flex-wrap tw-gap-2">
        <q-btn
          v-if="props.showSendMessageButton"
          @click="() => (modalHtml = true)"
          color="positive"
          icon="send"
          dense
          round
          size="sm"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-green-500 !tw-shadow-[0_0_0_0]"
        >
          <TooltipCustom text-tooltip="Enviar mensagem" />
        </q-btn>

        <q-btn
          v-if="!props.showOnlySendButton || props.showOnlyDisconnectButton"
          @click="disconnectWhatsApp"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-red-500 !tw-shadow-[0_0_0_0]"
          round
          size="sm"
          color="primary"
          icon="logout"
          :loading="isDisconnecting"
          :disable="shouldDisableDisconnectButton"
        >
          <TooltipCustom text-tooltip="Desconectar" />
        </q-btn>
      </div>
      <HtmlEditor
        v-if="modalHtml"
        v-model="modalHtml"
        :html-editor-model="userTemplate"
        @on-save="handleSaveAndSend"
        />
    </div>
  </div>
</template>
<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { ref, toRefs, defineModel, watch, onMounted, computed } from 'vue';
import WhatsAppModal from 'src/core/components/SG/WhatsApp/WhatsAppModal.vue';
import { useQuasar } from 'quasar';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { api } from 'src/boot/axios';
import HtmlEditor from 'components/ConfigEmpresa/Email/HtmlEditor.vue';
import { userTemplate } from 'components/ConfigEmpresa/Email/HtmlTemplates/userTemplate.js';
import { defineExpose } from 'vue';
import { useWhatsAppStore } from 'src/stores/whatsappStore'

// Store centralizado para gerenciar conexões WhatsApp entre módulos
const whatsappStore = useWhatsAppStore()
const props = defineProps({
  showDefault: {
    type: Boolean,
    default: false
  },
  errors: {
    type: Object,
    required: true
  },
  readonlyFields: {
    type: Boolean,
    default: false
  },
  showOnlySendButton: {
    type: Boolean,
    default: false
  },
  showSendMessageButton: {
    type: Boolean,
    default: false
  },
  showOnlyDisconnectButton: {
    type: Boolean,
    default: false
  },
  alwaysEnableConnect: {
    type: Boolean,
    default: false
  },
  configType: {
    type: Number, // 1 = empresa, 2 = nfe, 3 = financeiro, 4 = pdv, 5 = dav, 6 = mdfe
    required: true
  }
});

const modalHtml = ref(false);
const $q = useQuasar();
const fields = defineModel('fields', { type: Object, required: true });
const { readonlyFields } = toRefs(props);

// Estados da conexão WhatsApp
const isConnecting = ref(false);
const isConnected = ref(false);
const connectedNumber = ref('');
const qrCodeValue = ref('');
const qrBase64 = ref('');
const isDisconnecting = ref(false);
const showQrCode = ref(false);
const whatsappDialogRef = ref(null);
const isShowWhatsAppModalOpen = ref(false);
const lastConnectionError = ref('');
const qrCheckInterval = ref(null);
const sessionId = ref('');
const isSendingMessage = ref(false);
const defaultCompanyNumber = ref('');

// Controla quando desabilitar botão desconectar e checkbox
const shouldDisableDisconnectButton = computed(() => {
  if (props.configType === 1) return false // Empresa nunca desabilita
  // Para NFe (2), PDV (4), DAV (5), MDF-e (6), Financeiro (3)
  return [2, 3, 4, 5, 6].includes(props.configType) && fields.value?.padraoWhatsApp?.value === true
})

const shouldDisableCheckbox = computed(() => {
  if (props.configType === 1) return false // Empresa nunca desabilita checkbox
  // Para NFe (2), PDV (4), DAV (5), MDF-e (6), Financeiro (3)
  if ([2, 3, 4, 5, 6].includes(props.configType)) {
    const ownConnection = whatsappStore.connections[props.configType]
    return ownConnection?.isConnected && !fields.value?.padraoWhatsApp?.value
  }
  return false
})
async function deleteWhatsAppConfig() {
  try {
    await api.delete(`/api/whatsapp/configuracoes/${props.configType}`);
    if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }
  } catch (error) {
    if (error.response && error.response.status === 404) {
      console.log(`[WhatsApp Info] Configuração com tipo ${props.configType} não precisou ser deletada (já não existia).`);
      return;
    }
    throw error;
  }
}


// Garante que checkbox inicie desmarcado
watch(() => fields.value?.padraoWhatsApp?.value, (newValue) => {
  if (fields.value?.padraoWhatsApp && (newValue === undefined || newValue === null)) {
    fields.value.padraoWhatsApp.value = false;
  }
}, { immediate: true });

async function saveWhatsAppConfig() {
  try {
    const payload = { tipo: props.configType };
    await api.post('/api/whatsapp/configuracoes/', payload);
  } catch (error) {
    $q.notify({
      color: 'negative',
      position: 'top',
      message: 'Não foi possível salvar a configuração do WhatsApp.'
    });
  }
}


onMounted(async () => {
  console.log('[WhatsApp Debug] onMounted iniciado para configType:', props.configType)
  whatsappStore.loadFromLocalStorage()

  // Sempre carrega o número padrão da empresa primeiro
  await loadDefaultCompanyNumber()

  // Depois verifica a conexão específica do módulo atual
  await verificarConexaoExistente()

  console.log('[WhatsApp Debug] onMounted finalizado - isConnected:', isConnected.value, 'connectedNumber:', connectedNumber.value)
})

async function loadDefaultCompanyNumber() {
  try {
    // Busca conexão da empresa (tipo 1) diretamente da API
    const response = await api.get('/api/whatsapp/conexoes', { params: { tipo: 1 } })

    if (response.data && response.data.length > 0) {
      const companyConfig = response.data.find(config => parseInt(config.tipo) === 1)
      if (companyConfig && companyConfig.numero) {
        defaultCompanyNumber.value = companyConfig.numero

        // Atualiza o store da empresa também
        whatsappStore.updateConnection(1, {
          isConnected: true,
          number: companyConfig.numero,
          sessionId: companyConfig.sessionId || ''
        })

        console.log('[WhatsApp Debug] Número padrão da empresa carregado:', companyConfig.numero)
      } else {
        defaultCompanyNumber.value = ''
        console.log('[WhatsApp Debug] Nenhuma conexão da empresa encontrada')
      }
    } else {
      defaultCompanyNumber.value = ''
      console.log('[WhatsApp Debug] API não retornou dados para empresa')
    }
  } catch (error) {
    console.log('[WhatsApp Debug] Erro ao carregar número padrão da empresa:', error.response?.status || error.message)
    defaultCompanyNumber.value = ''
  }
}

// Gerencia mudança do checkbox "Usar padrão da empresa"
function changeUseDefault(useDefault) {
  whatsappStore.updateUseDefault(props.configType, useDefault)

  if (useDefault) {
    const companyConnection = whatsappStore.connections[1]
    if (companyConnection?.isConnected) {
      isConnected.value = true
      connectedNumber.value = companyConnection.number,
      sessionId.value = companyConnection.sessionId

      const specificConnection = whatsappStore.connections[props.configType]
      if (specificConnection?.isConnected) {

        deleteWhatsAppConfig()
        whatsappStore.clearConnection(props.configType)
      }
    } else if (defaultCompanyNumber.value) {
      isConnected.value = true
      connectedNumber.value = defaultCompanyNumber.value
      sessionId.value = ''

    } else {
      isConnected.value = false
      connectedNumber.value = '',
      sessionId.value = ''
      checkCompanyConnectionInBackground()
    }
  } else {
    isConnected.value = false
    connectedNumber.value = '',
    sessionId.value = ''

    const specificConnection = whatsappStore.connections[props.configType]
    if (specificConnection?.isConnected) {
      isConnected.value = true
      connectedNumber.value = specificConnection.number,
      sessionId.value = specificConnection.sessionId
    }
  }
}

// Verifica conexão da empresa em background sem bloquear UI
async function checkCompanyConnectionInBackground() {
  try {
    await loadDefaultCompanyNumber()

    // Só atualiza se o checkbox ainda estiver marcado
    if (fields.value?.padraoWhatsApp?.value) {
      const companyConnection = whatsappStore.connections[1]

      if (companyConnection?.isConnected && defaultCompanyNumber.value) {
        isConnected.value = true
        connectedNumber.value = companyConnection.number
      } else {
        $q.notify({
          position: 'top',
          color: 'warning',
          message: 'Nenhum WhatsApp padrão foi configurado para a empresa.'
        })
      }
    }
  } catch (error) {
    // Só mostra erro se o checkbox ainda estiver marcado
    if (fields.value?.padraoWhatsApp?.value) {
      $q.notify({
        position: 'top',
        color: 'warning',
        message: 'Nenhum WhatsApp padrão foi configurado para a empresa.'
      })
    }
  }
}

  async function verificarConexaoExistente() {
  try {
    const storeConnection = whatsappStore.connections[props.configType];
    if (storeConnection?.isConnected) {
      console.log('[WhatsApp Debug] Conexão encontrada no store:', storeConnection);
      isConnected.value = true;
      connectedNumber.value = storeConnection.number;
      return;
    }

    const response = await api.get('/api/whatsapp/conexoes', {
      params: { tipo: props.configType }
    });

    if (response.data && response.data.length > 0) {
      const correctConfig = response.data.find(config => parseInt(config.tipo) === props.configType);

      if (correctConfig && correctConfig.numero) {
        console.log('[WhatsApp Debug] Conexão encontrada na API:', correctConfig);
        isConnected.value = true;
        connectedNumber.value = correctConfig.numero;

        whatsappStore.updateConnection(props.configType, {
          isConnected: true,
          number: correctConfig.numero,
          sessionId: correctConfig.sessionId || ''
        });
      } else {
        console.log('[WhatsApp Debug] Nenhuma configuração válida encontrada na API para tipo:', props.configType);
        console.log('[WhatsApp Debug] Dados retornados:', response.data);
        isConnected.value = false;
        connectedNumber.value = '';
        whatsappStore.clearConnection(props.configType);
      }
    } else {
      console.log('[WhatsApp Debug] A API não retornou dados de conexão existentes para tipo:', props.configType);
      isConnected.value = false;
      connectedNumber.value = '';
      whatsappStore.clearConnection(props.configType);
    }
  } catch (error) {
    console.error('[WhatsApp Debug] Erro ao verificar conexão existente para tipo', props.configType, ':', error);
    isConnected.value = false;
    connectedNumber.value = '';
    whatsappStore.clearConnection(props.configType);

    // Não mostra notificação de erro para evitar spam de erros durante inicialização
    // A ausência de conexão é um estado normal, não um erro
  }
}
async function handleSaveAndSend(messageContent) {
  if (fields.value.whatsapp?.mensagemPadrao) {
    fields.value.whatsapp.mensagemPadrao.setValue(messageContent);
  }
  await sendMessage(messageContent);
  modalHtml.value = false;
}

// Envia mensagem WhatsApp convertendo HTML para texto
async function sendMessage(htmlMessage) {
  console.log('[WhatsApp Debug] Tentando enviar mensagem...')
  console.log('[WhatsApp Debug] isConnected:', isConnected.value)
  console.log('[WhatsApp Debug] sessionId:', sessionId.value)
  console.log('[WhatsApp Debug] connectedNumber:', connectedNumber.value)
  console.log('[WhatsApp Debug] configType:', props.configType)
  console.log('[WhatsApp Debug] usando padrão da empresa:', fields.value?.padraoWhatsApp?.value)

  if (!isConnected.value) {
    $q.notify({
      color: 'negative',
      position: 'top',
      message: 'WhatsApp não está conectado. Por favor, conecte-se primeiro.'
    });
    return;
  }

  if (!connectedNumber.value) {
    $q.notify({
      color: 'negative',
      position: 'top',
      message: 'Número do WhatsApp não encontrado. Por favor, reconecte.'
    });
    return;
  }

  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlMessage;
  const plainTextMessage = (tempDiv.textContent || tempDiv.innerText || "").trim();

  if (!plainTextMessage) {
    $q.notify({
     position: 'top',
     color: 'warning',
      message: 'A mensagem não pode estar vazia.'
    });
    return;
  }

  isSendingMessage.value = true;
  $q.notify({
    position: 'top',
   color: 'warning',
    message: 'Enviando mensagem...',
    timeout: 1500
  });

  try {
    // Verifica se a conexão ainda está ativa no backend antes de enviar
    console.log('[WhatsApp Debug] Verificando conexão ativa no backend...')
    const statusResponse = await api.get('/api/whatsapp/conexoes', {
      params: { tipo: props.configType }
    })

    if (!statusResponse.data || statusResponse.data.length === 0) {
      throw new Error('Conexão WhatsApp não encontrada no backend. Por favor, reconecte.')
    }

    const activeConnection = statusResponse.data.find(conn => parseInt(conn.tipo) === props.configType)
    if (!activeConnection) {
      throw new Error(`Conexão WhatsApp não encontrada para o tipo ${props.configType}. Por favor, reconecte.`)
    }

    console.log('[WhatsApp Debug] Conexão ativa confirmada:', activeConnection)

    const payload = {
      tipo: props.configType,
      whatsappMensagem: plainTextMessage,
      destinatario: connectedNumber.value
    };

    console.log('[WhatsApp Debug] Enviando payload:', payload)
    await api.post('/api/whatsapp/api/mensagem', payload);

    $q.notify({
      position: 'top',
      color: 'positive',
      message: 'Mensagem enviada com sucesso!'
    });
  } catch (error) {
    console.error('[WhatsApp Debug] Erro ao enviar mensagem:', error)

    let errorMessage = 'Falha ao enviar a mensagem.'

    if (error.message && error.message.includes('Conexão WhatsApp não encontrada')) {
      errorMessage = error.message
      // Limpa o estado local já que a conexão não existe mais no backend
      isConnected.value = false
      connectedNumber.value = ''
      whatsappStore.clearConnection(props.configType)
    } else if (error.response?.data?.errors?.whatsappMensagem?.[0]) {
      errorMessage = error.response.data.errors.whatsappMensagem[0]
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message

      // Se o backend diz que não encontrou conexão, limpa o estado local
      if (errorMessage.includes('Conexão não encontrada')) {
        isConnected.value = false
        connectedNumber.value = ''
        whatsappStore.clearConnection(props.configType)
        errorMessage += ' Por favor, reconecte o WhatsApp.'
      }
    }

    $q.notify({
      color: 'negative',
      position: 'top',
      message: errorMessage
    });
  } finally {
    isSendingMessage.value = false;
  }
}

watch(isConnected, (newValue) => {
  if (newValue) {
    qrBase64.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }
  }
});

watch(lastConnectionError, () => {
  // Watcher para erros de conexão
});

// Sincroniza mudanças do store com a UI quando não usando padrão
watch(
  () => whatsappStore.connections[props.configType],
  (newConnection) => {
    if (!fields.value?.padraoWhatsApp?.value && newConnection) {
      isConnected.value = newConnection.isConnected
      connectedNumber.value = newConnection.number
    }
  },
  { deep: true }
)

// Sincroniza mudanças da conexão da empresa quando usando padrão
watch(
  () => whatsappStore.connections[1], // Observa conexão da empresa
  (companyConnection) => {
    // Só atualiza se estiver usando padrão da empresa e não for a própria empresa
    if (fields.value?.padraoWhatsApp?.value && [2, 3, 4, 5, 6].includes(props.configType) && companyConnection) {
      if (companyConnection.isConnected) {
        isConnected.value = true
        connectedNumber.value = companyConnection.number
      } else {
        // Empresa desconectou, limpa a tela atual
        isConnected.value = false
        connectedNumber.value = ''
        defaultCompanyNumber.value = ''
      }
    }
  },
  { deep: true }
)
async function showWhatsAppModal() {
  if (isShowWhatsAppModalOpen.value) return;
  isShowWhatsAppModalOpen.value = true;

  const receivedQrBase64 = await connectWhatsApp();

  if (receivedQrBase64) {
    whatsappDialogRef.value = $q
      .dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: WhatsAppModal,
          scope: 'WhatsAppModal',
          hasCancel: true,
          hasSave: false,
          dataModal: {
            qrBase64: receivedQrBase64
          },
          title: 'Conecte-se ao WhatsApp',
          classesTopBox: '!tw-mb-2 lg:tw-w-[450px] tw-text-center',
          descriptionClass: 'tw-w-full tw-break-words tw-whitespace-pre-wrap'
        }
      })
      .onDismiss(() => {
        isShowWhatsAppModalOpen.value = false;
        if (qrCheckInterval.value) {
          clearInterval(qrCheckInterval.value);
          qrCheckInterval.value = null;
        }
      });
  } else {
    isShowWhatsAppModalOpen.value = false;
  }
}


async function connectWhatsApp() {
  isConnecting.value = true;
  try {
    const response = await api.post('/api/whatsapp/api/conectar', {
      tipo: props.configType
    });

    const { sessionId: newSessionId, qrBase64: newQrBase64 } = response.data;

    sessionId.value = newSessionId;
    qrBase64.value = newQrBase64;
    showQrCode.value = true;

    startStatusCheck();

    return newQrBase64;
  } catch (error) {
    const errorMessage = error.response?.data?.message || 'Erro ao conectar com WhatsApp';
    lastConnectionError.value = errorMessage;
    showQrCode.value = false;

    $q.notify({
      color: 'negative',
      position: 'top',
      message: errorMessage
    });
    return null;
  } finally {
    isConnecting.value = false;
  }
}


// Verifica status da conexão e finaliza processo de conexão
async function checkWhatsAppStatus() {
  if (!sessionId.value) return;

  try {
    const response = await api.put('/api/whatsapp/api/status', {
      tipo: props.configType,
      sessionId: sessionId.value
    });

    const session = response.data.sessoes.find(
      (s) => s.sessionId === sessionId.value
    );

    if (!session) {
      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);
      return;
    }

    if (session.isConnected) {
      isConnected.value = true;
      const newConnectedNumber = session.numero;
      connectedNumber.value = newConnectedNumber

      whatsappStore.updateConnection(props.configType, {
        isConnected: true,
        number: newConnectedNumber,
        sessionId: sessionId.value
      })

      if (props.configType === 2 && !fields.value.padraoWhatsApp?.value) {
        fields.value.padraoWhatsApp.value = false
        whatsappStore.updateUseDefault(props.configType, false)
      }

      showQrCode.value = false;
      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);
      if (whatsappDialogRef.value) whatsappDialogRef.value.hide();

      if (!fields.value.padraoWhatsApp?.value) {
        await saveWhatsAppConfig(newConnectedNumber);
      }

      $q.notify({
        position: 'top',
        color: 'positive',
        message: 'WhatsApp conectado com sucesso!'
      });
    }
  } catch (error) {
    console.error('Erro ao verificar status:', error);
  }
}

function startStatusCheck() {
  if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);
  qrCheckInterval.value = setInterval(() => {
    checkWhatsAppStatus();
  }, 5000);
}

// Desconecta WhatsApp e limpa estados
async function disconnectWhatsApp() {
  isDisconnecting.value = true;
  try {
    await deleteWhatsAppConfig();
    await api.put('/api/whatsapp/api/desconectar', {
      tipo: props.configType
    });

    isConnected.value = false;
    connectedNumber.value = '';

    // Para NFe, PDV, DAV, MDF-e, Financeiro - desmarca checkbox
    if ([2, 3, 4, 5, 6].includes(props.configType) && fields.value?.padraoWhatsApp) {
      fields.value.padraoWhatsApp.value = false;
      whatsappStore.updateUseDefault(props.configType, false);
    }

    defaultCompanyNumber.value = '';
    whatsappStore.clearConnection(props.configType)

    // Se desconectar da empresa, desmarca todos os checkboxes dos outros módulos
    if (props.configType === 1) {
      whatsappStore.updateUseDefault(2, false) // NFe
      whatsappStore.updateUseDefault(3, false) // Financeiro
      whatsappStore.updateUseDefault(4, false) // PDV
      whatsappStore.updateUseDefault(5, false) // DAV
      whatsappStore.updateUseDefault(6, false) // MDF-e
      defaultCompanyNumber.value = ''
    }

    if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }

    qrCodeValue.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }

    $q.notify({
      color: 'positive',
      position: 'top',
      message: 'WhatsApp desconectado com sucesso!'
    });
  } catch (error) {
    $q.notify({
      color: 'negative',
      position: 'top',
      message: 'Erro ao desconectar: ' + (error.response?.data?.message || error.message)
    });
  } finally {
    isDisconnecting.value = false;
  }
}
defineExpose({
  isConnected,
  connectedNumber,
  changeUseDefault
});
</script>
