<template>
  <q-checkbox
    v-if="props.showDefault"
    @update:model-value="changeUseDefault"
    v-model="fields.padraoWhatsApp.value"
    :error="!!fields.padraoWhatsApp.errorMessage"
    :error-message="fields.padraoWhatsApp.errorMessage"
    :valid="fields.padraoWhatsApp.meta.valid"
    :disable="readonlyFields || shouldDisableCheckbox"
    label="Usar padrão da empresa"
    class="tw-col-span-full tw-mb-3 tw-w-fit"
    size="2rem"
    dense
  />
  <div
    v-if="props.showDefault && fields.padraoWhatsApp.value && defaultCompanyNumber.value"
    class="tw-col-span-full tw-mb-3"
  >
    <p class="tw-text-sm tw-text-gray-600">WhatsApp padrão: {{ defaultCompanyNumber.value }}</p>
  </div>
  <div
    v-if="!isConnected"
    class="tw-col-span-full tw-flex tw-justify-center sm:tw-justify-start"
  >
    <q-btn
      @click="showWhatsAppModal"
      :disable="
        props.alwaysEnableConnect
          ? false
          : fields.padraoWhatsApp?.value === true ||
            (isConnected && !fields.padraoWhatsApp)
      "
      :loading="isConnecting"
      color="positive"
      dense
      unelevated
      no-caps
      class="tw-w-[230px]"
    >
      <p class="tw-text-inherit">Conectar com o WhatsApp</p>
    </q-btn>
  </div>

  <div
    v-if="isConnected"
    class="tw-col-span-full tw-flex tw-w-full tw-items-center tw-justify-between"
  >
    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-h-3 tw-w-3 tw-rounded-full tw-bg-green-500"></div>
      <p class="tw-m-0 tw-text-start">{{ connectedNumber }}</p>
    </div>

    <div class="tw-flex tw-items-center tw-gap-2">
      <div class="tw-mt-2 tw-flex tw-w-full tw-flex-wrap tw-gap-2">
        <q-btn
          v-if="props.showSendMessageButton"
          @click="() => (modalHtml = true)"
          color="positive"
          icon="send"
          dense
          round
          size="sm"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-green-500 !tw-shadow-[0_0_0_0]"
        >
          <TooltipCustom text-tooltip="Enviar mensagem" />
        </q-btn>

        <q-btn
          v-if="!props.showOnlySendButton || props.showOnlyDisconnectButton"
          @click="disconnectWhatsApp"
          class="!tw-h-[35px] !tw-max-h-[35px] !tw-min-h-[35px] !tw-min-w-[35px] !tw-bg-red-500 !tw-shadow-[0_0_0_0]"
          round
          size="sm"
          color="primary"
          icon="logout"
          :loading="isDisconnecting"
          :disable="shouldDisableDisconnectButton"
        >
          <TooltipCustom text-tooltip="Desconectar" />
        </q-btn>
      </div>
      <HtmlEditor
        v-if="modalHtml"
        v-model="modalHtml"
        :html-editor-model="userTemplate"
        @on-save="handleSaveAndSend"
        />
    </div>
  </div>
</template>
<script setup>
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { ref, toRefs, defineModel, watch, onMounted, computed } from 'vue';
import WhatsAppModal from 'src/core/components/SG/WhatsApp/WhatsAppModal.vue';
import { useQuasar } from 'quasar';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { api } from 'src/boot/axios';
import HtmlEditor from 'components/ConfigEmpresa/Email/HtmlEditor.vue';
import { userTemplate } from 'components/ConfigEmpresa/Email/HtmlTemplates/userTemplate.js';
import { defineExpose } from 'vue';

// Adicionar no início do script
import { useWhatsAppStore } from 'src/stores/whatsappStore'
import { storeToRefs } from 'pinia'

// Adicionar após as outras declarações
const whatsappStore = useWhatsAppStore()
const { useDefault } = storeToRefs(whatsappStore)
//Props
const props = defineProps({
  showDefault: {
    type: Boolean,
    default: false
  },
  errors: {
    type: Object,
    required: true
  },
  readonlyFields: {
    type: Boolean,
    default: false
  },
  showOnlySendButton: {
    type: Boolean,
    default: false
  },
  showSendMessageButton: {
    type: Boolean,
    default: false
  },
  showOnlyDisconnectButton: {
    type: Boolean,
    default: false
  },
  alwaysEnableConnect: {
    type: Boolean,
    default: false
  },
  configType: {
    type: Number, // 1 = padrao, 2 = venda, 3 = financeiro
    required: true
  }
});

const modalHtml = ref(false);

//Global
const $q = useQuasar();

const fields = defineModel('fields', { type: Object, required: true });
const { readonlyFields } = toRefs(props);

// Estados da conexão WhatsApp
const isConnecting = ref(false);
const isConnected = ref(false);
const connectedNumber = ref('');
const qrCodeValue = ref('');
const qrBase64 = ref('');
const isDisconnecting = ref(false);
const showQrCode = ref(false);
const whatsappDialogRef = ref(null);
const isShowWhatsAppModalOpen = ref(false);
const lastConnectionError = ref('');
const qrCheckInterval = ref(null);
const sessionId = ref('');
const isSendingMessage = ref(false);

const defaultCompanyNumber = ref('');

// Computed properties para controlar estados dos botões
const shouldDisableDisconnectButton = computed(() => {
  // Se for config de empresa (tipo 1), nunca desabilita
  if (props.configType === 1) {
    return false
  }

  // Se for NFe (tipo 2) e checkbox estiver marcado, desabilita
  if (props.configType === 2 && fields.value.padraoWhatsApp?.value === true) {
    return true
  }

  return false
})

const shouldDisableCheckbox = computed(() => {
  // Se for config de empresa (tipo 1), nunca desabilita o checkbox
  if (props.configType === 1) {
    return false
  }

  // Se for NFe (tipo 2) e estiver conectado com número próprio, desabilita checkbox
  if (props.configType === 2) {
    const ownConnection = whatsappStore.connections[props.configType]
    return ownConnection.isConnected && !fields.value.padraoWhatsApp?.value
  }

  return false
})
async function deleteWhatsAppConfig() {
  console.log('Iniciando processo de deleção da configuração do WhatsApp...');
  try {
  // Constrói a URL com o tipo diretamente no caminho
  await api.delete(`/api/whatsapp/configuracoes/${props.configType}`);

  // Limpa o controle após deletar a configuração
  if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }
  } catch (error) {
    console.error('Erro ao deletar configuração do WhatsApp:', error);
  }
}
// NOVO: Função para buscar uma configuração de WhatsApp por tipo
async function fetchWhatsAppConfig(tipo) {
  try {

    const response = await api.get(`/api/whatsapp/configuracoes`, { params: { tipo } });
    if (response.data && response.data.length > 0) {
      // Retorna o primeiro número encontrado para aquele tipo
      return response.data[0].celular;
    }
    return '';
  } catch (error) {
    console.error(`Erro ao buscar configuração do WhatsApp (tipo ${tipo}):`, error);
    return '';
  }
}

// Watch para garantir que o checkbox inicie desmarcado
watch(() => fields.value?.padraoWhatsApp?.value, (newValue) => {
  if (newValue === undefined || newValue === null) {
    fields.value.padraoWhatsApp.value = false;
  }
}, { immediate: true });

// NOVO: Função para salvar a configuração do WhatsApp
async function saveWhatsAppConfig(celular) {
  try {
    const payload = {
      tipo: props.configType,
      celular: celular
    };
    await api.post('/api/whatsapp/configuracoes/', payload);
    console.log(`Configuração do WhatsApp (tipo ${props.configType}) salva com sucesso!`);
  } catch (error) {
    console.error('Erro ao salvar configuração do WhatsApp:', error);
    $q.notify({
      type: 'negative',
      position: 'top',
      message: 'Não foi possível salvar a configuração do WhatsApp.',
    });
  }
}


onMounted(async () => {
  console.log('🔵 ONMOUNTED - Iniciando para configType:', props.configType)
  console.log('🔵 ONMOUNTED - Checkbox padrão inicial:', fields.value.padraoWhatsApp?.value)

  // Carregar estado do store
  whatsappStore.loadFromLocalStorage()
  console.log('🔵 ONMOUNTED - Store carregado:', whatsappStore.connections, whatsappStore.useDefault)

  // Verificar conexão existente para TODOS os tipos
  await verificarConexaoExistente()
  console.log('🔵 ONMOUNTED - Após verificarConexaoExistente - isConnected:', isConnected.value, 'connectedNumber:', connectedNumber.value)

  // Se não for empresa (tipo 1), carregar número padrão da empresa
  if (props.configType !== 1) {
    await loadDefaultCompanyNumber()
    console.log('🔵 ONMOUNTED - Após loadDefaultCompanyNumber - defaultCompanyNumber:', defaultCompanyNumber.value)
  }

  console.log('🔵 ONMOUNTED - Estado final - isConnected:', isConnected.value, 'connectedNumber:', connectedNumber.value)
})

async function loadDefaultCompanyNumber() {
  try {
    const companyNumber = await fetchWhatsAppConfig(1) // Busca config da empresa
    defaultCompanyNumber.value = companyNumber

    // REMOVIDO: Não deve espelhar automaticamente aqui
    // O espelhamento só deve acontecer quando o usuário marcar o checkbox
  } catch (error) {
    console.error('Erro ao carregar número padrão da empresa:', error)
  }
}

async function changeUseDefault(useDefault) {
  console.log('🟢 CHANGE_USE_DEFAULT - useDefault:', useDefault, 'configType:', props.configType)

  // Atualiza o store
  whatsappStore.updateUseDefault(props.configType, useDefault)

  if (useDefault) {
    // Checkbox MARCADO: Usar padrão da empresa
    const companyConnection = whatsappStore.connections[1] // Pega diretamente a conexão da empresa
    console.log('🟢 CHANGE_USE_DEFAULT - Conexão da empresa:', companyConnection)
    console.log('🟢 CHANGE_USE_DEFAULT - defaultCompanyNumber:', defaultCompanyNumber.value)

    if (companyConnection.isConnected) {
      console.log('🟢 CHANGE_USE_DEFAULT - Empresa conectada, espelhando:', companyConnection.number)
      isConnected.value = true
      connectedNumber.value = companyConnection.number

      // Se tinha conexão própria, limpa ela (mas não desconecta da API)
      if (whatsappStore.connections[props.configType].isConnected) {
        await deleteWhatsAppConfig() // Remove apenas do banco
        whatsappStore.clearConnection(props.configType) // Limpa do store
      }
    } else if (defaultCompanyNumber.value) {
      // Se não tem no store mas tem o número padrão, usa ele
      console.log('🟢 CHANGE_USE_DEFAULT - Usando defaultCompanyNumber:', defaultCompanyNumber.value)
      isConnected.value = true
      connectedNumber.value = defaultCompanyNumber.value
    } else {
      console.log('🟢 CHANGE_USE_DEFAULT - Empresa não conectada')
      isConnected.value = false
      connectedNumber.value = ''
      $q.notify({
        type: 'negative',
        position: 'top',
        message: 'Nenhum WhatsApp padrão foi configurado para a empresa.',
        icon: 'warning'
      })
    }
  } else {
    // Checkbox DESMARCADO: Usar configuração específica
    console.log('🟢 CHANGE_USE_DEFAULT - Desmarcado, limpando')
    // Limpa a conexão visual (usuário terá que conectar novamente)
    isConnected.value = false
    connectedNumber.value = ''

    // Verifica se existe configuração própria salva
    const specificConnection = whatsappStore.connections[props.configType]
    if (specificConnection.isConnected) {
      console.log('🟢 CHANGE_USE_DEFAULT - Restaurando conexão própria:', specificConnection.number)
      isConnected.value = true
      connectedNumber.value = specificConnection.number
    }
  }
}
async function verificarConexaoExistente() {
  console.log('🟡 VERIFICAR_CONEXAO - Iniciando para configType:', props.configType)
  try {
    // Verifica apenas a configuração específica para este tipo (não usar getEffectiveConnection aqui)
    const response = await api.get('/api/whatsapp/configuracoes', {
      params: { tipo: props.configType }
    })
    console.log('🟡 VERIFICAR_CONEXAO - Resposta da API:', response.data)

    if (response.data && response.data.length > 0) {
      // Filtra apenas configs do tipo correto (a API pode estar retornando tipos errados)
      const correctConfig = response.data.find(config => parseInt(config.tipo) === props.configType)

      if (correctConfig) {
        console.log('🟡 VERIFICAR_CONEXAO - Config correta encontrada:', correctConfig)
        isConnected.value = true
        connectedNumber.value = correctConfig.celular

        // Atualiza o store
        whatsappStore.updateConnection(props.configType, {
          isConnected: true,
          number: correctConfig.celular
        })
        console.log('🟡 VERIFICAR_CONEXAO - Definindo como conectado:', correctConfig.celular)
      } else {
        console.log('🟡 VERIFICAR_CONEXAO - Nenhuma config do tipo correto encontrada')
        isConnected.value = false
        connectedNumber.value = ''
      }
    } else {
      console.log('🟡 VERIFICAR_CONEXAO - Nenhuma config encontrada')
      isConnected.value = false
      connectedNumber.value = ''
    }
  } catch (error) {
    console.error('🟡 VERIFICAR_CONEXAO - Erro:', error)
    isConnected.value = false
    connectedNumber.value = ''
  }
}
/**
 * Função para lidar com o salvamento do editor e o envio da mensagem.
 * @param {string} messageContent - O conteúdo HTML/texto do editor.
 */
async function handleSaveAndSend(messageContent) {
  // 1. Opcional: Salva o conteúdo no campo do formulário, mantendo a funcionalidade original.
  if (fields.value.whatsapp?.mensagemPadrao) {
    fields.value.whatsapp.mensagemPadrao.setValue(messageContent);
  }

  // 2. Chama a função para enviar a mensagem.
  await sendMessage(messageContent);

  // 3. Fecha o modal do editor após o envio.
  modalHtml.value = false;
}

/**
 * Envia uma mensagem de WhatsApp através da API.
 * @param {string} htmlMessage - A mensagem no formato HTML vinda do editor.
 */
async function sendMessage(htmlMessage) {
  // --- INÍCIO DA CORREÇÃO ---

  // 1. NOVO: Converte o HTML para texto puro.
  // Criamos um elemento div temporário na memória.
  const tempDiv = document.createElement('div');
  // Inserimos o HTML dentro dele.
  tempDiv.innerHTML = htmlMessage;
  // Extraímos apenas o conteúdo de texto, o que remove todas as tags.
  const plainTextMessage = (tempDiv.textContent || tempDiv.innerText || "").trim();

  // 2. ALTERADO: Validamos o texto puro.
  if (!plainTextMessage) {
    $q.notify({
      type: 'warning',
      position: 'top',
      message: 'A mensagem não pode estar vazia.',
      icon: 'warning'
    });
    return;
  }

  isSendingMessage.value = true;
  $q.notify({
    type: 'info',
    position: 'top',
    message: 'Enviando mensagem...',
    timeout: 1500 // Notificação curta
  });

  try {
    const payload = {
      celular: connectedNumber.value,
      // 3. ALTERADO: Usamos a variável com o texto puro no payload.
      mensagem: plainTextMessage,
      destinatario: connectedNumber.value
    };

    await api.post('/api/whatsapp/api/mensagem', payload);

    $q.notify({
      type: 'positive',
      position: 'top',
      message: 'Mensagem enviada com sucesso!',
      icon: 'check_circle'
    });
  } catch (error) {
    console.error('Erro ao enviar mensagem:', error);
    const validationError = error.response?.data?.errors?.mensagem?.[0];
    const generalMessage = error.response?.data?.message || 'Falha ao enviar a mensagem.';
    $q.notify({
      type: 'negative',
      position: 'top',
      message: validationError || generalMessage, // Dá prioridade ao erro de validação.
      icon: 'error'
    });
  } finally {
    isSendingMessage.value = false;
  }
}

// --- FIM DA NOVA IMPLEMENTAÇÃO ---

// Funções existentes (connect, disconnect, etc.)
watch(isConnected, (newValue) => {
  if (newValue) {
    // if (fields.value?.padraoWhatsApp) {
    //   fields.value.padraoWhatsApp.value = false;
    // }
    qrBase64.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }
  }
});

watch(lastConnectionError, (newValue) => {
  console.log('Erro de conexão:', newValue);
});

// Adicionar watcher para mudanças no store - APENAS para conexão própria
watch(
  () => whatsappStore.connections[props.configType],
  (newConnection) => {
    console.log('🟠 WATCHER - Mudança detectada para configType:', props.configType)
    console.log('🟠 WATCHER - Nova conexão própria:', newConnection)
    console.log('🟠 WATCHER - useDefault atual:', useDefault.value[props.configType])
    console.log('🟠 WATCHER - Checkbox atual:', fields.value.padraoWhatsApp?.value)

    // Se não estiver usando padrão, atualiza com a conexão própria
    if (!fields.value.padraoWhatsApp?.value) {
      console.log('🟠 WATCHER - Atualizando com conexão própria:', newConnection.isConnected, newConnection.number)
      isConnected.value = newConnection.isConnected
      connectedNumber.value = newConnection.number
    } else {
      console.log('🟠 WATCHER - NÃO atualizando (usando padrão da empresa)')
    }
  },
  { deep: true }
)
async function showWhatsAppModal() {
  if (isShowWhatsAppModalOpen.value) return;
  isShowWhatsAppModalOpen.value = true;

  const receivedQrBase64 = await connectWhatsApp();

  if (receivedQrBase64) {
    whatsappDialogRef.value = $q
      .dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: WhatsAppModal,
          scope: 'WhatsAppModal',
          hasCancel: true,
          hasSave: false,
          dataModal: {
            qrBase64: receivedQrBase64
          },
          title: 'Conecte-se ao WhatsApp',
          classesTopBox: '!tw-mb-2 lg:tw-w-[450px] tw-text-center',
          descriptionClass: 'tw-w-full tw-break-words tw-whitespace-pre-wrap'
        }
      })
      .onDismiss(() => {
        isShowWhatsAppModalOpen.value = false;
        if (qrCheckInterval.value) {
          clearInterval(qrCheckInterval.value);
          qrCheckInterval.value = null;
        }
      });
  } else {
    isShowWhatsAppModalOpen.value = false;
  }
}

function getTestNumberByType() {
  switch (props.configType) {
    case 1: // Empresa
      return '553491003526'; // Número de teste para empresa
    case 2: // NF-e
      return '5534991745020'; // Número de teste para NF-e
    default:
      return '553490000000'; // Número padrão
  }
}

async function connectWhatsApp() {
  isConnecting.value = true;
  try {
    const celular = getTestNumberByType();
    const response = await api.post('/api/whatsapp/api/conectar', {
      celular: celular
    });

    const { sessionId: newSessionId, qrBase64: newQrBase64 } = response.data;

    sessionId.value = newSessionId;
    qrBase64.value = newQrBase64;
    showQrCode.value = true;

    startStatusCheck();

    return newQrBase64;
  } catch (error) {
    console.error('Erro ao conectar:', error);
    const errorMessage = error.response?.data?.message || 'Erro ao conectar com WhatsApp';
    lastConnectionError.value = errorMessage;
    showQrCode.value = false;

    $q.notify({
      type: 'negative',
      position: 'top',
      message: errorMessage,
      icon: 'error'
    });
    return null;
  } finally {
    isConnecting.value = false;
  }
}


// Verificar status da conexão
async function checkWhatsAppStatus() {
  if (!sessionId.value) return;

  try {
    const celular = getTestNumberByType();
    const response = await api.put('/api/whatsapp/api/status', {
      celular:celular,
      sessionId: sessionId.value
    });

    const session = response.data.sessoes.find(
      (s) => s.sessionId === sessionId.value
    );

    if (!session) {
      console.error('Sessão não encontrada:', sessionId.value);
      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);
      return;
    }

    if (session.isConnected) {
      isConnected.value = true;
      const newConnectedNumber = session.nomeCelular;
      connectedNumber.value = newConnectedNumber

      // Atualizar o store
      whatsappStore.updateConnection(props.configType, {
        isConnected: true,
        number: newConnectedNumber,
        sessionId: sessionId.value
      })

      // Se for NFe e conectou diretamente (não usando padrão), desmarcar checkbox
      if (props.configType === 2 && !fields.value.padraoWhatsApp?.value) {
        fields.value.padraoWhatsApp.value = false
        whatsappStore.updateUseDefault(props.configType, false)
      }

      showQrCode.value = false;

      if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);

      if (whatsappDialogRef.value) {
        whatsappDialogRef.value.hide();
      }

      // Salva a configuração após conectar com sucesso, se não estiver usando o padrão
      if (!fields.value.padraoWhatsApp?.value) {
        await saveWhatsAppConfig(newConnectedNumber);
      }

      $q.notify({
        type: 'positive',
        position: 'top',
        message: 'WhatsApp conectado com sucesso!',
        timeout: 5000
      });
    }
  } catch (error) {
    console.error('Erro ao verificar status:', error);
  }
}

// Iniciar verificação periódica
function startStatusCheck() {
  if (qrCheckInterval.value) clearInterval(qrCheckInterval.value);

  qrCheckInterval.value = setInterval(() => {
    checkWhatsAppStatus();
  }, 5000);
}

//Desconectar do Wpp
async function disconnectWhatsApp() {
  isDisconnecting.value = true;
  try {
    console.log('Iniciando desconexão do WhatsApp...');
    const celular = getTestNumberByType();  // Usa o número baseado no tipo

    console.log('Número conectado:', connectedNumber.value);

    // Primeiro deleta a configuração específica do módulo
    await deleteWhatsAppConfig();

    console.log('Configuração deletada, agora desconectando sessão...');
    await api.put('/api/whatsapp/api/desconectar', {
      celular: celular
    });

    console.log('Sessão desconectada com sucesso.');

    // Limpa todos os estados
    isConnected.value = false;
    connectedNumber.value = '';

    // Se for NFe, reabilita o checkbox após desconectar
    if (props.configType === 2) {
      fields.value.padraoWhatsApp.value = false;
      whatsappStore.updateUseDefault(props.configType, false);
    }

    defaultCompanyNumber.value = ''; // Limpa o número padrão da empresa

    // Limpa o store
    whatsappStore.clearConnection(props.configType)

    // Se for empresa, limpa também o "usar padrão" dos outros
    if (props.configType === 1) {
      whatsappStore.updateUseDefault(2, false)
      whatsappStore.updateUseDefault(3, false)
    }
    // defaultCompanyNumber.value = ''; // Limpa o número padrão da empresa
    // fields.value.whatsapp.controle = null; // Limpa o controle da configuração

     // Limpa o controle apenas se o objeto whatsapp existir
     if (fields.value?.whatsapp) {
      fields.value.whatsapp.controle = null;
    }


    qrCodeValue.value = '';
    if (qrCheckInterval.value) {
      clearInterval(qrCheckInterval.value);
      qrCheckInterval.value = null;
    }

    console.log('Atualizando UI...');

    $q.notify({
      type: 'positive',
      position: 'top',
      message: 'WhatsApp desconectado com sucesso!',
      timeout: 5000
    });
  } catch (error) {
    console.error('Erro ao desconectar:', error);
    $q.notify({
      type: 'negative',
      message: 'Erro ao desconectar: ' + error.message,
      timeout: 5000
    });
  } finally {
    isDisconnecting.value = false;
    console.log('Processo de desconexão finalizado');
  }
}
defineExpose({
  isConnected,
  connectedNumber,
  changeUseDefault
});
</script>
