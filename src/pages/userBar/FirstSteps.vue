<template>
  <SGPage id="first-steps" title="Bem vindo!">
    <template #buttons>
      <q-checkbox
        v-model="checkboxPage"
        label="Ignorar passos"
        @update:model-value="ignoreCheckbox"
      />
    </template>
    <MiniModal
      v-if="pageModal"
      v-model="pageModal"
      @on-save="() => dontShowPage()"
      title="Ignorar seus passos?"
      save-custom-class="tw-bg-SGBRRed"
      class="tw-text-textsSGBR-gray"
      scope="ignorar-passos"
      width="330px"
      :has-close-icon="false"
    >
      <p><PERSON><PERSON><PERSON><PERSON> el<PERSON>, não iremos mais lembrar você dos seus passos.</p>
    </MiniModal>
    <template #description>
      <p class="tw-ml-2 tw-text-lg tw-font-normal">
        Primeiros passos para o uso do SG Master Web
      </p>
    </template>
    <SGCard
      v-for="(step, index) in stepsFilter"
      :key="step.id"
      :id="step.buttonLabel"
      class="fixedHeightBtnQuasar"
    >
      <div
        class="tw-flex tw-items-center"
        :class="{
          'tw-flex-col ': $q.screen.lt.md
        }"
      >
        <div
          class="tw-flex tw-items-center"
          :class="{
            'tw-w-full ': $q.screen.lt.md
          }"
        >
          <q-icon
            :class="{
              'tw-text-SGBRBlueLighten': step.isCompleted,
              'tw-text-SGBRGray': !step.isCompleted
            }"
            size="1.4rem"
            name="check_circle"
          />
          <p
            class="tw-ml-2 tw-mr-1.5 tw-whitespace-nowrap tw-font-bold"
            :class="{
              'tw-text-SGBRBlueLighten': step.isCompleted,
              'tw-text-textsSGBR-gray': !step.isCompleted
            }"
          >
            Passo {{ index + 1 }}:
          </p>
        </div>
        <div
          class="tw-flex tw-flex-1 tw-items-center tw-gap-2"
          :class="{
            'tw-w-full ': $q.screen.lt.md
          }"
        >
          <p class="tw-font-medium">{{ step.description }}</p>
          <q-btn
            :to="step.route"
            v-if="!step.isCompleted"
            class="tw-ml-auto tw-font-bold tw-text-SGBRBlueLighten"
            style="border: 1px solid #8dbbff; min-height: 15px"
            flat
            >{{ step.buttonLabel }}
          </q-btn>
        </div>
      </div>
    </SGCard>

    <p class="tw-text-xl">
      Sinta-se à vontade para explorar o sistema como desejar! :)
    </p>
  </SGPage>
</template>

<script setup>
import SGPage from 'components/generic/SGPage.vue';
import SGCard from 'components/generic/SGCard.vue';
import { useSteps } from 'stores/api/firstStep';
import { useGlobal } from 'stores/global.js';
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import MiniModal from 'src/components/modal/MiniModal.vue';
import { useRouter } from 'vue-router';

const global = useGlobal();
const router = useRouter();

const { company } = storeToRefs(global);
const plan = computed(() =>
  company.value?.empresaWithPlano?.descricao?.toLowerCase()
);

const passos = useSteps();
passos.table.rowsPerPage = null;
passos.get(company.value.controle);

const checkboxPage = ref(
  localStorage.getItem(global.localStepsName) ? true : false
);
const pageModal = ref(false);

function ignoreCheckbox(value) {
  value
    ? (pageModal.value = true)
    : localStorage.removeItem(global.localStepsName);
}

function dontShowPage() {
  localStorage.setItem(global.localStepsName, true);
  if (!global.roles?.includes('DASHBOARD:MENU')) {
    router.push('/bem-vindo');
  } else router.push('/dashboard');
}

watch(
  () => pageModal.value,
  () => {
    if (!localStorage.getItem(global.localStepsName)) {
      checkboxPage.value = pageModal.value;
    }
  }
);

const { data: passosData } = storeToRefs(passos);

const steps = ref([
  {
    id: 0,
    description: 'Complete o cadastro dos dados de sua empresa!',
    route: '/dados-empresa',
    buttonLabel: 'DADOS DA EMPRESA',
    isCompleted: computed(() => passosData.value?.empresa)
  },
  {
    id: 1,
    description:
      'Cadastre produtos ou serviços para fazer suas vendas rapidamente.',
    route: '/produto',
    buttonLabel: 'CADASTROS DE ESTOQUE',
    isCompleted: computed(() => passosData.value?.produto)
  },
  {
    id: 3,
    description:
      'Insira seu certificado para credenciar suas emissões fiscais.',
    route: '/configuracoes-emissao',
    buttonLabel: 'CONFIGURAÇÕES GERAIS',
    isCompleted: computed(() => passosData.value?.certificado)
  },
  {
    id: 4,
    description: 'Confira as informações de notas fiscais para emiti-las.',
    route: '/configuracoes-emissao',
    buttonLabel: 'CONFIGURAÇÕES DE EMISSÃO',
    isCompleted: computed(() =>
      company.value.codPlano != '2'
        ? passosData.value?.configNfe && passosData.value?.configNfce
        : passosData.value?.configNfe || passosData.value?.configNfce
    )
  }
]);

const stepsFilter = computed(() =>
  steps.value.filter(
    (step) => step?.plan?.includes(plan.value) || step?.plan === undefined
  )
);
</script>
