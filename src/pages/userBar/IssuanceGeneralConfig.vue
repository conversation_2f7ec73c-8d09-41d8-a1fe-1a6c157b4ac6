<template>
  <div>
    <RegisterFormSkeleton v-if="isLoadingPage" :has-nav-menu="showNavMenu" />

    <SGRegisterPage
      v-else
      id="general-issuance"
      :action-bar="false"
      title="Configurações de emissão"
    >
      <GeneralSettings
        @changed-values="() => fetchDataNfce()"
        :certificate-data="certificates"
        v-if="hasCertificate"
      />

      <!------ DADOS NFC-e ----->
      <SGCard
        title="Dados para emissão de NFC-e"
        id="general-issuance-nfc"
        remove-gap
        :cols="9"
        :has-circle="false"
        v-if="hasNfce"
      >
        <div
          class="tw-col-span-full -tw-mt-4 tw-flex tw-flex-wrap tw-items-center tw-gap-4"
        >
          <q-tabs
            v-model="tabsNFC"
            active-color="primary"
            class="tw-col-span-full tw-w-fit tw-text-[13px] tw-text-textsSGBR-gray"
            content-class="tw-flex tw-flex-wrap"
          >
            <q-tab
              name="prod"
              label="Ambiente de produção"
              alert-icon="check"
              :alert="formValuesNFC?.producao?.padrao"
            />
            <q-tab
              name="hom"
              label="Ambiente de homologação"
              :alert="formValuesNFC?.homologacao?.padrao"
              alert-icon="check"
            />
          </q-tabs>
        </div>
        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tabsNFC"
          animated
          transition-prev="slide-right"
          transition-next="slide-left"
          transition-duration="800"
          class="tw-col-span-full"
        >
          <q-tab-panel
            name="prod"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.csc"
              type="text"
              class="quasar-input-text-none tw-col-span-2"
              required
              label-slot
              maxlength="255"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Cód. de Seg. do Cont. (CSC)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Código de segurança do contribuinte - token"
              />
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.codCsc"
              class="tw-col-span-2"
              type="text"
              mask="######"
              input-class="tw-text-right"
              required
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                ID Token (CSC) <span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.serie"
              type="number"
              class="tw-col-span-2"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNFC.producao.serie <= 999 || 'Máximo de 999 série'
              ]"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Série da NFC-e<span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.sequencia"
              type="number"
              class="tw-col-span-3"
              required
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                Seq. da próx. NFC-e em prod.
                <span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Sequência da próxima NFC-e em prod."
              />
            </q-input>

            <SelectDefault
              required
              label="Tipo de emissão"
              v-bind="formsNFC.producao.tipoEmissao"
              :options="tiposEmissao"
              class="tw-col-span-4"
              outlined
              dense
              :clearable="false"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tipo de emissão<span class="require">*</span>
              </template>
            </SelectDefault>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.tempoEspera"
              type="number"
              class="tw-col-span-2"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNFC.producao.tempoEspera <= 120 ||
                  'Máximo de 120 segundos'
              ]"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tempo de espera (segundos)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.tentativasEnvio"
              @update:model-value="
                (val) => {
                  setFieldValueNFC(
                    'producao.tentativasEnvio',
                    val ? Number(val) : ''
                  );
                }
              "
              type="number"
              :rules="[
                () =>
                  formValuesNFC.producao.tentativasEnvio <= 6 ||
                  'Máximo de 6 tentativas de envio'
              ]"
              class="tw-col-span-3"
              required
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tentativas envio<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.descricao"
              type="textarea"
              class="tw-col-span-full !tw-max-h-[126px]"
              input-style="min-height: 90px; max-height: 90px"
              autogrow
              label-slot
              bottom-slots
              maxlength="255"
              label="Informações adicionais padrão"
              :readonly="onlyViewNfce"
            />

            <q-checkbox
              dense
              outlined
              stack-label
              v-bind="formsNFC.producao.padrao"
              class="tw-col-span-3 tw-my-1 !tw-w-fit"
              required
              @update:model-value="
                (value) => {
                  setFieldValueNFC('homologacao.padrao', false);
                  if (!value) {
                    setFieldValueNFC('producao.padrao', true);
                  }
                }
              "
              :disable="onlyViewNfce"
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
          <q-tab-panel
            name="hom"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.csc"
              type="text"
              class="quasar-input-text-none tw-col-span-2"
              required
              label-slot
              maxlength="255"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Cód. de Seg. do Cont. (CSC)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Código de segurança do contribuinte - token"
              />
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.codCsc"
              class="tw-col-span-2"
              type="text"
              mask="######"
              input-class="tw-text-right"
              required
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                ID Token (CSC) <span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.serie"
              type="number"
              class="tw-col-span-2"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNFC.homologacao.serie <= 999 ||
                  'Máximo de 999 série'
              ]"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Série da NFC-e<span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.sequencia"
              type="number"
              class="tw-col-span-3"
              required
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                Seq. da próx. NFC-e em hom.
                <span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Sequência da próxima NFC-e em hom."
              />
            </q-input>

            <SelectDefault
              required
              label="Tipo de emissão"
              v-bind="formsNFC.homologacao.tipoEmissao"
              :options="TIPOS_EMISSAO_NFCE_HOM"
              class="tw-col-span-4"
              outlined
              dense
              :clearable="false"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tipo de emissão<span class="require">*</span>
              </template>
            </SelectDefault>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.tempoEspera"
              @update:model-value="
                (val) => {
                  setFieldValueNFC('homologacao.tempoEspera', val ?? '');
                }
              "
              type="number"
              class="tw-col-span-2"
              required
              :rules="[
                () =>
                  formValuesNFC.homologacao.tempoEspera <= 120 ||
                  'Máximo de 120 segundos'
              ]"
              label-slot
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tempo de espera (segundos)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.tentativasEnvio"
              @update:model-value="
                (val) => {
                  setFieldValueNFC(
                    'homologacao.tentativasEnvio',
                    val ? Number(val) : ''
                  );
                }
              "
              type="number"
              class="tw-col-span-3"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNFC.homologacao.tentativasEnvio <= 6 ||
                  'Máximo de 6 tentativas de envio'
              ]"
              :readonly="onlyViewNfce"
            >
              <template #label>
                Tentativas envio<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.descricao"
              type="textarea"
              class="tw-col-span-full !tw-max-h-[126px]"
              input-style="min-height: 90px; max-height: 90px"
              autogrow
              label-slot
              bottom-slots
              maxlength="255"
              label="Informações adicionais padrão"
              :readonly="onlyViewNfce"
            />

            <q-checkbox
              dense
              outlined
              stack-label
              v-bind="formsNFC.homologacao.padrao"
              class="tw-col-span-3 tw-my-1 !tw-w-fit"
              required
              @update:model-value="
                (value) => {
                  setFieldValueNFC('producao.padrao', false);
                  if (!value) {
                    setFieldValueNFC('homologacao.padrao', true);
                  }
                }
              "
              :disable="onlyViewNfce"
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
        </q-tab-panels>

        <div class="tw-col-span-8 tw-pt-4">
          <q-btn
            flat
            class="tw-w-fit tw-bg-SGBRBlueLighten tw-text-white"
            @click="() => saveData('nfc')"
            :disable="onlyViewNfce"
            >Salvar alterações</q-btn
          >
        </div>
      </SGCard>

      <!------ DADOS NFE ----->
      <SGCard
        title="Dados para emissão de NF-e"
        id="general-issuance-nf"
        remove-gap
        :cols="9"
        :has-circle="false"
        v-if="hasNfe"
      >
        <div
          class="tw-col-span-full -tw-mt-4 tw-flex tw-flex-wrap tw-items-center tw-gap-4"
        >
          <q-tabs
            v-model="tabsNF"
            active-color="primary"
            class="tw-col-span-full tw-w-fit tw-text-[13px] tw-text-textsSGBR-gray"
            content-class="tw-flex tw-flex-wrap"
          >
            <q-tab
              name="prod"
              label="Ambiente de produção"
              alert-icon="check"
              :alert="formValuesNF?.producao?.padrao"
            />
            <q-tab
              name="hom"
              label="Ambiente de homologação"
              :alert="formValuesNF?.homologacao?.padrao"
              alert-icon="check"
            />
          </q-tabs>
        </div>
        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tabsNF"
          animated
          transition-prev="slide-right"
          transition-next="slide-left"
          transition-duration="800"
          class="tw-col-span-full"
        >
          <q-tab-panel
            name="prod"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              dense
              outlined
              stack-label
              type="number"
              v-bind="formsNF.producao.serie"
              input-class="tw-text-right"
              class="tw-col-span-4"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNF.producao.serie <= 999 || 'Máximo de 999 série'
              ]"
              :readonly="onlyViewNfe"
            >
              <template #label>
                Série da NF-e<span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.producao.sequencia"
              type="number"
              class="tw-col-span-5"
              required
              maxlength="20"
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Seq. da próx. NF-e
                <span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                :text-tooltip="`Sequência da próxima NF-e`"
              />
            </q-input>
            <SelectDefault
              required
              label="Tipo de emissão"
              v-bind="formsNF.producao.tipoEmissao"
              :options="tiposEmissao"
              class="tw-col-span-4"
              outlined
              dense
              :clearable="false"
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tipo de emissão<span class="require">*</span>
              </template>
            </SelectDefault>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.producao.tempoEspera"
              @update:model-value="
                (val) =>
                  setFieldValueNF(
                    'producao.tempoEspera',
                    val ? Number(val) : ''
                  )
              "
              type="number"
              class="tw-col-span-2"
              required
              :rules="[
                () =>
                  formValuesNF.producao.tempoEspera <= 120 ||
                  'Máximo de 120 segundos'
              ]"
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tempo de espera (segundos)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.producao.tentativasEnvio"
              @update:model-value="
                (val) =>
                  setFieldValueNF(
                    'producao.tentativasEnvio',
                    val ? Number(val) : ''
                  )
              "
              type="number"
              class="tw-col-span-3"
              :rules="[
                () =>
                  formValuesNF.producao.tentativasEnvio <= 6 ||
                  'Máximo de 6 tentativas de envio'
              ]"
              required
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tentativas envio<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.producao.descricao"
              type="textarea"
              class="tw-col-span-full tw-mb-2 !tw-max-h-[120px]"
              input-style="min-height: 90px; max-height: 90px"
              autogrow
              label-slot
              bottom-slots
              maxlength="255"
              label="Informações adicionais padrão"
              :readonly="onlyViewNfe"
            />
            <q-checkbox
              dense
              outlined
              stack-label
              v-bind="formsNF.producao.padrao"
              class="tw-col-span-3 tw-mb-2 tw-mt-0 !tw-w-fit"
              required
              @update:model-value="
                (value) => {
                  setFieldValueNF('homologacao.padrao', false);
                  if (!value) {
                    setFieldValueNF('producao.padrao', true);
                  }
                }
              "
              :disable="onlyViewNfe"
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
          <q-tab-panel
            name="hom"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              dense
              outlined
              stack-label
              type="number"
              v-bind="formsNF.homologacao.serie"
              input-class="tw-text-right"
              class="tw-col-span-4"
              required
              label-slot
              :rules="[
                () =>
                  formValuesNF.homologacao.serie <= 999 || 'Máximo de 999 série'
              ]"
              :readonly="onlyViewNfe"
            >
              <template #label>
                Série da NF-e<span class="require">*</span>
              </template>
            </q-input>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.homologacao.sequencia"
              type="number"
              class="tw-col-span-5"
              required
              maxlength="20"
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Seq. da próx. NF-e
                <span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                :text-tooltip="`Sequência da próxima NF-e`"
              />
            </q-input>
            <SelectDefault
              required
              label="Tipo de emissão"
              v-bind="formsNF.homologacao.tipoEmissao"
              :options="tiposEmissao"
              class="tw-col-span-4"
              outlined
              dense
              :clearable="false"
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tipo de emissão<span class="require">*</span>
              </template>
            </SelectDefault>
            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.homologacao.tempoEspera"
              @update:model-value="
                (val) =>
                  setFieldValueNF(
                    'homologacao.tempoEspera',
                    val ? Number(val) : ''
                  )
              "
              type="number"
              class="tw-col-span-2"
              required
              :rules="[
                () =>
                  formValuesNF.homologacao.tempoEspera <= 120 ||
                  'Máximo de 120 segundos'
              ]"
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tempo de espera (segundos)<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.homologacao.tentativasEnvio"
              @update:model-value="
                (val) =>
                  setFieldValueNF(
                    'homologacao.tentativasEnvio',
                    val ? Number(val) : ''
                  )
              "
              type="number"
              class="tw-col-span-3"
              :rules="[
                () =>
                  formValuesNF.homologacao.tentativasEnvio <= 6 ||
                  'Máximo de 6 tentativas de envio'
              ]"
              required
              label-slot
              :readonly="onlyViewNfe"
            >
              <template #label>
                Tentativas envio<span class="require">*</span>
              </template>
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </q-input>

            <q-input
              dense
              outlined
              stack-label
              v-bind="formsNF.homologacao.descricao"
              type="textarea"
              class="tw-col-span-full tw-mb-2 !tw-max-h-[120px]"
              input-style="min-height: 90px; max-height: 90px"
              autogrow
              label-slot
              bottom-slots
              maxlength="255"
              label="Informações adicionais padrão"
              :readonly="onlyViewNfe"
            />

            <q-checkbox
              dense
              outlined
              stack-label
              v-bind="formsNF.homologacao.padrao"
              class="tw-col-span-3 tw-mb-2 tw-mt-0 !tw-w-fit"
              required
              @update:model-value="
                (value) => {
                  setFieldValueNF('producao.padrao', false);
                  if (!value) {
                    setFieldValueNFC('homologacao.padrao', true);
                  }
                }
              "
              :disable="onlyViewNfe"
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
        </q-tab-panels>

        <div class="tw-col-span-8 tw-pt-4">
          <q-btn
            flat
            class="tw-w-fit tw-bg-SGBRBlueLighten tw-text-white"
            @click="() => saveData('nf')"
            :disable="onlyViewNfe"
            >Salvar alterações</q-btn
          >
        </div>
      </SGCard>

      <SGCardNovo
        v-if="hasMdfe"
        title="Dados para emissão de MDF-e"
        id="general-issuance-mdfe"
        remove-gap
        :cols="9"
        has-title-separator
        :has-circle="false"
      >
        <div
          class="tw-col-span-full -tw-mt-4 tw-flex tw-flex-wrap tw-items-center tw-gap-4"
        >
          <q-tabs
            v-model="tabsMdfe"
            active-color="primary"
            class="tw-col-span-full tw-w-fit tw-text-[13px] tw-text-textsSGBR-gray"
            content-class="tw-flex tw-flex-wrap"
          >
            <q-tab
              name="prod"
              label="Ambiente de produção"
              alert-icon="check"
              :alert="formValuesMDF?.producao?.padrao"
            />
            <q-tab
              name="hom"
              label="Ambiente de homologação"
              :alert="formValuesMDF?.homologacao?.padrao"
              alert-icon="check"
            />
          </q-tabs>
        </div>
        <q-separator class="tw-col-span-full tw-mb-4" />

        <q-tab-panels
          v-model="tabsMdfe"
          animated
          transition-prev="slide-right"
          transition-next="slide-left"
          transition-duration="800"
          class="tw-col-span-full"
        >
          <q-tab-panel
            name="prod"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              v-model="fieldsMDF.producao.serie.value"
              :error="!!fieldsMDF.producao.serie.errorMessage"
              :error-message="fieldsMDF.producao.serie.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputNumber"
              label="Série da MDF-e"
              type="number"
              input-class="tw-text-right"
              class="tw-col-span-4"
              required
            />

            <q-input
              v-model="fieldsMDF.producao.sequencia.value"
              :error="!!fieldsMDF.producao.sequencia.errorMessage"
              :error-message="fieldsMDF.producao.sequencia.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputNumber"
              label="Seq. da próx. MDF-e"
              type="number"
              input-class="tw-text-right"
              class="tw-col-span-5"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Sequência da próxima MDF-e"
              />
            </q-input>

            <InputSelect
              v-model="fieldsMDF.producao.tipoEmissao.value"
              :error="!!fieldsMDF.producao.tipoEmissao.errorMessage"
              :error-message="fieldsMDF.producao.tipoEmissao.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="{
                ...ModelInputSelectSearch,
                params: {
                  optionsStatic: tiposEmissaoMdfe
                }
              }"
              :clearable="false"
              option-value="value"
              option-label="label"
              label="Tipo de emissão"
              class="tw-col-span-4"
              required
            />

            <InputMoney
              v-model="fieldsMDF.producao.tempoEspera.value"
              :error="!!fieldsMDF.producao.tempoEspera.errorMessage"
              :error-message="fieldsMDF.producao.tempoEspera.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              :decimals-quantity="0"
              label="Tempo de espera (segundos)"
              input-class="!tw-text-right"
              class="tw-col-span-2"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </InputMoney>

            <InputMoney
              v-model="fieldsMDF.producao.tentativasEnvio.value"
              :error="!!fieldsMDF.producao.tentativasEnvio.errorMessage"
              :error-message="fieldsMDF.producao.tentativasEnvio.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              :decimals-quantity="0"
              label="Tentativas envio"
              input-class="!tw-text-right"
              class="tw-col-span-3"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </InputMoney>

            <q-input
              v-model="fieldsMDF.producao.descricao.value"
              :error="!!fieldsMDF.producao.descricao.errorMessage"
              :error-message="fieldsMDF.producao.descricao.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              label="Informações adicionais padrão"
              type="textarea"
              class="tw-col-span-full tw-mb-2 !tw-max-h-[120px]"
              input-style="min-height: 90px; max-height: 90px"
              maxlength="255"
              autogrow
            />

            <q-checkbox
              v-model="fieldsMDF.producao.padrao.value"
              :disable="onlyViewMdfe"
              @update:model-value="
                (value) => {
                  setFieldValueMDF('homologacao.padrao', false);
                  if (!value) {
                    setFieldValueMDF('producao.padrao', true);
                  }
                }
              "
              v-bind="ModelInputText"
              class="tw-col-span-3 tw-mb-2 tw-mt-0 !tw-w-fit"
              required
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
          <q-tab-panel
            name="hom"
            class="tw-gap-x-customGridGapCards"
            :class="
              $q.screen.lt.md ? 'tw-flex tw-flex-col' : `tw-grid tw-grid-cols-9`
            "
          >
            <q-input
              v-model="fieldsMDF.homologacao.serie.value"
              :error="!!fieldsMDF.homologacao.serie.errorMessage"
              :error-message="fieldsMDF.homologacao.serie.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputNumber"
              label="Série da MDF-e"
              type="number"
              input-class="tw-text-right"
              class="tw-col-span-4"
              required
            />

            <q-input
              v-model="fieldsMDF.homologacao.sequencia.value"
              :error="!!fieldsMDF.homologacao.sequencia.errorMessage"
              :error-message="fieldsMDF.homologacao.sequencia.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputNumber"
              label="Seq da próx. MDF-e"
              type="number"
              input-class="tw-text-right"
              class="tw-col-span-5"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Sequência da próxima MDF-e"
              />
            </q-input>

            <InputSelect
              v-model="fieldsMDF.homologacao.tipoEmissao.value"
              :error="!!fieldsMDF.homologacao.tipoEmissao.errorMessage"
              :error-message="fieldsMDF.homologacao.tipoEmissao.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="{
                ...ModelInputSelectSearch,
                params: {
                  optionsStatic: tiposEmissaoMdfe
                }
              }"
              :clearable="null"
              option-value="value"
              option-label="label"
              label="Tipo de emissão"
              class="tw-col-span-4"
              required
            />

            <InputMoney
              v-model="fieldsMDF.homologacao.tempoEspera.value"
              :error="!!fieldsMDF.homologacao.tempoEspera.errorMessage"
              :error-message="fieldsMDF.homologacao.tempoEspera.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              :decimals-quantity="0"
              label="Tempo de espera (segundos)"
              input-class="!tw-text-right"
              class="tw-col-span-2"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Tempo de espera entre cada tentativa"
              />
            </InputMoney>

            <InputMoney
              v-model="fieldsMDF.homologacao.tentativasEnvio.value"
              :error="!!fieldsMDF.homologacao.tentativasEnvio.errorMessage"
              :error-message="
                fieldsMDF.homologacao.tentativasEnvio.errorMessage
              "
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              :decimals-quantity="0"
              label="Tentativas envio"
              input-class="!tw-text-right"
              class="tw-col-span-3"
              required
            >
              <TooltipCustom
                :custom-offset="[0, 40]"
                text-tooltip="Número de tentativas de envio em caso de falhas no servidor"
              />
            </InputMoney>

            <q-input
              v-model="fieldsMDF.homologacao.descricao.value"
              :error="!!fieldsMDF.homologacao.descricao.errorMessage"
              :error-message="fieldsMDF.homologacao.descricao.errorMessage"
              :readonly="onlyViewMdfe"
              v-bind="ModelInputText"
              label="Informações adicionais padrão"
              type="textarea"
              class="tw-col-span-full tw-mb-2 !tw-max-h-[120px]"
              input-style="min-height: 90px; max-height: 90px"
              maxlength="255"
              autogrow
              required
            />

            <q-checkbox
              v-model="fieldsMDF.homologacao.padrao.value"
              :disable="onlyViewMdfe"
              @update:model-value="
                (value) => {
                  setFieldValueMDF('producao.padrao', false);
                  if (!value) {
                    setFieldValueMDF('homologacao.padrao', true);
                  }
                }
              "
              v-bind="ModelInputText"
              class="tw-col-span-3 tw-mb-2 tw-mt-0 !tw-w-fit"
              required
            >
              Padrão
            </q-checkbox>
          </q-tab-panel>
        </q-tab-panels>

        <div class="tw-col-span-8 tw-pt-4">
          <q-btn
            :disable="onlyViewMdfe"
            flat
            class="tw-w-fit tw-bg-SGBRBlueLighten tw-text-white"
            @click="() => saveData('mdfe')"
            >Salvar alterações</q-btn
          >
        </div>
      </SGCardNovo>
    </SGRegisterPage>
  </div>
</template>

<script setup>
import SGCard from 'components/generic/SGCard.vue';
import SGCardNovo from 'src/core/components/SG/Card/NewSGCard.vue';
import SGRegisterPage from 'components/generic/SGRegister.vue';
import SelectDefault from 'components/generic/input/Select/default.vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import GeneralSettings from 'pages/userBar/GeneralSettings.vue';
import { useFormSchemaNF } from 'pages/userBar/useFormSchemaNF.js';
import { useFormSchemaNFC } from 'pages/userBar/useFormSchemaNFC.js';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import RegisterFormSkeleton from 'src/components/generic/skeletons/RegisterFormSkeleton.vue';
import InputMoney from 'src/core/components/Inputs/Money/InputMoney.vue';
import InputSelect from 'src/core/components/Inputs/Select/InputSelect.vue';
import ModelInputNumber from 'src/core/models/inputs/Number';
import ModelInputSelectSearch from 'src/core/models/inputs/SelectSearch';
import ModelInputText from 'src/core/models/inputs/Text';
import { useMdfeEmissaoConfigStore } from 'src/pages/userBar/store/useMdfeEmissaoConfigStore';
import { useFormSchemaMDF } from 'src/pages/userBar/useFormSchemaMDF';
import { useGlobal } from 'src/stores/global';
import { useNfce } from 'stores/api/nfce';
import { useNfe } from 'stores/api/nfe';
import { computed, ref, toRaw, toRefs } from 'vue';
import { useRouter } from 'vue-router';

const $q = useQuasar();

const global = useGlobal();

const hasNfe = computed(() =>
  global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-NFE')
);

const onlyViewNfe = computed(
  () =>
    global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-NFE') &&
    !global.roles?.includes('EMPRESA.EMISSAO:EDITAR-CONFIG-NFE')
);

const hasNfce = computed(() =>
  global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-NFCE')
);

const onlyViewNfce = computed(
  () =>
    global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-NFCE') &&
    !global.roles?.includes('EMPRESA.EMISSAO:EDITAR-CONFIG-NFCE')
);

const hasCertificate = computed(() =>
  global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR')
);

const hasMdfe = computed(() =>
  global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-NFE')
);

const onlyViewMdfe = computed(
  () =>
    global.roles?.includes('EMPRESA.EMISSAO:VISUALIZAR-CONFIG-MDFE') &&
    !global.roles?.includes('EMPRESA.EMISSAO:EDITAR-CONFIG-MDFE')
);

const showNavMenu = computed(
  () =>
    [hasNfe.value, hasNfce.value, hasCertificate.value].filter(
      (val) => val == true
    ).length > 1
);

const tabsNFC = ref('prod');
const tabsNF = ref('prod');
const tabsMdfe = ref('prod');

const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const certificates = ref(null);

async function fetchDataNfce() {
  try {
    const response = await api.get(`api/certificado/digital`);

    certificates.value = response.data;
  } catch (error) {
    $q.notify({
      position: 'top',
      color: 'warning',
      message: error.response.data,
      icon: 'warning'
    });
  }
}
fetchDataNfce();

const emit = defineEmits(['ok']);
const router = useRouter();
const { modal } = toRefs(props);

const nfce = useNfce();
const nfe = useNfe();
const mdfe = useMdfeEmissaoConfigStore();

const { data: nfceData } = storeToRefs(nfce);
const { data: nfeData } = storeToRefs(nfe);

nfce.resetData();

const searchedNfce = ref(false);
const searchedNfe = ref(false);
const searchedMdfe = ref(false);
const initialMdfe = ref();

const isLoadingPage = computed(() => {
  return (
    (!searchedNfce.value || !searchedNfe.value || certificates.value == null) &&
    !props.modal
  );
});

nfce.get(null, false, false).then(() => {
  setEditData();
  searchedNfce.value = true;
});

nfe.get(null, false, false).then(() => {
  setEditData();
  searchedNfe.value = true;
});

const {
  forms: formsNFC,
  resetForm: resetFormNFC,
  setFieldValue: setFieldValueNFC,
  formValues: formValuesNFC
} = useFormSchemaNFC();
const {
  forms: formsNF,
  resetForm: resetFormNF,
  setFieldValue: setFieldValueNF,
  formValues: formValuesNF
} = useFormSchemaNF();
const {
  values: formValuesMDF,
  resetForm: resetFormMDF,
  setFieldValue: setFieldValueMDF,
  fields: fieldsMDF
} = useFormSchemaMDF();

mdfe.get({ paginate: false, deletedAt: false }).then((response) => {
  if (response.success) {
    initialMdfe.value = response?.data?.rowsData ?? false;
    setMdfeEditData();
    searchedMdfe.value = true;
  }
});

/**
 * Atualiza com valores do back.
 */
async function setMdfeEditData() {
  if (initialMdfe.value?.producao) {
    resetFormMDF({
      values: {
        producao: {
          ...initialMdfe.value.producao,
          tipoEmissao: String(initialMdfe.value.producao?.tipoEmissao)
        }
      }
    });
  }
  if (initialMdfe.value?.homologacao) {
    resetFormMDF({
      values: {
        homologacao: {
          ...initialMdfe.value.homologacao,
          tipoEmissao: String(initialMdfe.value.homologacao?.tipoEmissao)
        }
      }
    });
  }

  if (initialMdfe.value?.producao?.padrao) tabsMdfe.value = 'prod';
  if (initialMdfe.value?.homologacao?.padrao) tabsMdfe.value = 'hom';
}

/**
 * Popula o formulário com dados salvos.
 */
async function setEditData() {
  const rawNFC = toRaw(nfceData.value);
  const rawNF = toRaw(nfeData.value);

  if (rawNFC) {
    resetFormNFC({
      values: {
        ...rawNFC,
        producao: {
          ...rawNFC?.producao,
          padrao: rawNFC?.producao?.padrao ?? true,
          destino: '1',
          tipoEmissao: rawNFC?.producao?.tipoEmissao?.toString() ?? '1'
        },
        homologacao: {
          ...rawNFC?.homologacao,
          padrao: rawNFC?.homologacao?.padrao ?? false,
          destino: '2',
          tipoEmissao: rawNFC?.homologacao?.tipoEmissao?.toString() ?? '1'
        }
      }
    });

    if (rawNFC?.producao?.padrao) tabsNFC.value = 'prod';
    if (rawNFC?.homologacao?.padrao) tabsNFC.value = 'hom';
  }

  if (rawNF) {
    resetFormNF({
      values: {
        ...rawNF,
        producao: {
          ...rawNF?.producao,
          padrao: rawNF?.producao?.padrao ?? true,
          destino: rawNF?.producao?.destino ?? '1',
          tipoEmissao: rawNF?.producao?.tipoEmissao?.toString() ?? '1'
        },
        homologacao: {
          ...rawNF?.homologacao,
          padrao: rawNF?.homologacao?.padrao ?? false,
          destino: rawNF?.homologacao?.destino ?? '2',
          tipoEmissao: rawNF?.homologacao?.tipoEmissao?.toString() ?? '1'
        }
      }
    });

    if (rawNF?.producao?.padrao) tabsNF.value = 'prod';
    if (rawNF?.homologacao?.padrao) tabsNF.value = 'hom';
  }
}

const tiposEmissao = [
  {
    label: '1 - Normal',
    value: '1'
  },
  {
    label: '3 - Contingência com SCAN',
    value: '3'
  },
  {
    label: '5 - Contingência FS-DA',
    value: '5'
  }
];

const TIPOS_EMISSAO_NFCE_HOM = [
  {
    label: '1 - Normal',
    value: '1'
  },
  {
    label: '3 - Contingência com SCAN',
    value: '3'
  },
  {
    label: '5 - Contingência FS-DA',
    value: '5'
  },
  {
    label: '9 - Emissão off-line',
    value: '9'
  }
];

const tiposEmissaoMdfe = [
  {
    label: '1 - Normal',
    value: '1'
  },
  {
    label: '2 - Contingência off-line',
    value: '2'
  }
];

async function saveData(type = 'nfc') {
  let response, payload, url;

  if (type === 'nf') {
    payload = toRaw(formValuesNF);
    response = await nfe.put(payload);
    url = '/vendas/nf';
  } else if (type === 'nfc') {
    payload = toRaw(formValuesNFC);
    response = await nfce.put(payload);
    url = '/vendas/pdv';
  } else {
    payload = toRaw(formValuesMDF);
    response = await mdfe.put({ payload });
    url = '/vendas/mdfe';
  }

  // Se a operação foi bem-sucedida, desabilita a animação de aviso e redireciona conforme necessário
  if (response?.success) {
    if (modal.value) {
      emit('ok');
    }
    router.push(url);
  }
}
</script>
