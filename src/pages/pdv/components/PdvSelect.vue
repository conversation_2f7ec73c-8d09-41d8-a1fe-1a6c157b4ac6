<template>
  <q-select
    ref="selectRef"
    outlined
    dense
    stack-label
    use-input
    hide-selected
    fill-input
    hide-bottom-space
    label-slot
    input-debounce="200"
    input-class="tw-text-[20px] lg:tw-text-[32px] tw-text-right tw-pt-1"
    :loading="loading?.table"
    :placeholder="props.placeholder"
    v-model="model"
    :readonly="disabled"
    :options="filtered_options"
    :option-label="props.storeKey"
    :popup-content-style="[
      filtered_options.length > 9 ? 'height: 300px; width: 50px' : ''
    ]"
    option-value="controle"
    :required="!disabled && props.required"
    virtual-scroll-sticky-size-end="40"
    hide-dropdown-icon
    @input-value="filterFn"
    @popup-show="handleShowPopup"
    @keydown.enter="
      (ev) => {
        ev.preventDefault();
        if (!isRequesting) {
          isRequesting = true;
          handleInputEnter(ev.target.value);
        }
      }
    "
    @update:model-value="handleNewOptionSelected"
    @virtual-scroll="(data) => updateScroll(data, 0)"
  >
    <TooltipCustom
      text-tooltip="Digite '/' para pesquisar"
      custom-anchor="top middle"
    />

    <template #label>
      {{ props.labelSelect }}
    </template>

    <template #after-options>
      <div class="tw-sticky tw-bottom-0 tw-bg-SGBRGrayBG tw-p-2">
        <div class="tw-flex tw-items-center tw-gap-4">
          <div class="tw-flex tw-items-center tw-gap-1">
            <q-icon
              name="keyboard_arrow_up"
              size="1.2rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten"
            />
            <q-icon
              name="keyboard_arrow_down"
              size="1.2rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten"
            />
            <span class="tw-text-[10px] tw-font-bold"> Navegar </span>
          </div>

          <div class="tw-flex tw-items-center tw-gap-1">
            <q-icon
              name="keyboard_return"
              size="0.9rem"
              class="tw-rounded-md tw-bg-SGBRGrayLighten tw-p-1"
            />
            <span class="tw-text-[10px] tw-font-bold"> Selecionar </span>
          </div>

          <div class="tw-flex tw-items-center tw-gap-1">
            <span
              class="tw-rounded-md tw-bg-SGBRGrayLighten tw-p-1 tw-text-[10px] tw-font-bold"
            >
              Esc
            </span>
            <span class="tw-text-[10px] tw-font-bold"> Fechar </span>
          </div>
        </div>
      </div>
    </template>

    <template #no-option>
      <span
        class="tw-flex tw-flex-row tw-items-center tw-justify-center tw-p-4 tw-font-medium"
        >Nenhum resultado encontrado para "{{
          searchString.toUpperCase()
        }}"</span
      >
    </template>
  </q-select>
</template>

<script setup>
import { useApiOptions } from 'components/generic/input/Select/composables/useApiOptions';
import { useQuasar } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import { useGlobal } from 'src/stores/global';
import {
  computed,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch
} from 'vue';

// Props do componente
const props = defineProps({
  labelSelect: {
    type: String,
    required: true
  },
  required: {
    type: Boolean,
    default: false
  },
  buttonAdd: {
    type: Boolean,
    default: false
  },
  store: {
    type: Object,
    required: true
  },
  scope: {
    type: String,
    required: true
  },
  storeKey: {
    type: String,
    required: true
  },
  value: {
    type: [Object, String],
    required: false,
    default: ''
  },
  filters: {
    type: [Object, String],
    required: false,
    default: ''
  },
  orderBy: {
    type: [Object, String],
    required: false,
    default: () => {}
  },
  filterOr: {
    type: Array,
    default: null
  },
  multiple: {
    type: Boolean,
    required: false,
    default: false
  },
  deletedAt: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: null,
    required: false
  },
  configPdv: {
    type: [Object, String],
    required: false,
    default: () => {}
  }
});

// Emits do componente.
const emit = defineEmits([
  'selected',
  'fetchWithMoney',
  'fetchWithQuantity',
  'fetchEanControle',
  'importDav'
]);

const { filters, filterOr, deletedAt, disabled, orderBy, configPdv } =
  toRefs(props);
const $q = useQuasar();
const model = ref(null);
const selectRef = ref();
const selectInput = reactive([
  {
    filterText: '',
    filteredSelectOptions: []
  }
]);
const searchString = ref('');
let scrollEvent = null;
const global = useGlobal();
// Ao inicializar, ativa o listener do scroll para reposicionar o menu
onMounted(() => {
  if (props.value) {
    model.value = props.value;
  }

  const dialog = document.querySelector('.sgbrDialogs');

  if (dialog) {
    scrollEvent = dialog?.addEventListener('scroll', () => {
      if (selectRef.value) selectRef.value.updateMenuPosition();
    });
  }
});

// Para qualquer mudança externa no value, altera a model interna deste componente.
watch(
  () => props.value,
  (newValue) => {
    model.value = newValue;
  }
);

// Guarda o valor original da pesquisa utilizando "/" para manipulações futuras
const originalSearchValue = ref('');

/**
 * Ao selecionar uma opção do menu, emite a opção selecionada e fecha o menu.
 * @param {object} option
 */
const handleNewOptionSelected = (option) => {
  let quantity = 1;

  if (originalSearchValue.value?.includes('*')) {
    const quantityString = originalSearchValue.value.split('*')[0];
    quantity = Number(quantityString.replace(',', '.')) || 1;
  }

  option.qtdeEstoque = option.produtoWithEstoque.qtde;
  option.produtoWithEstoque.qtde = quantity;

  emit('selected', option);
  selectRef.value.updateInputValue('');
  originalSearchValue.value = '';
};

const isRequesting = ref(false);

/**
 * Faz validações referentes ao que foi digitado.
 * Armazena as seguintes funcionalidades de digitação:
 *
 * R25*10 - 25 reais do item de controle 10.
 *
 * 15*10 - 15 unidades do item de controle 10.
 *
 * 123 - Busca controle ou EAN igual a 123.
 *
 * DP2 / DC2 / DO2 - Busca a respectiva DAV de controle 2.
 * @param {string} string
 */
const filterFn = async (value) => {
  const multiplyQuantityUsingNameRegex = /^(?!0$)(?!0\d)\d+(?:,\d+)?\*\/\S+$/;

  if (!value) selectRef.value.hidePopup();

  if (
    value.replace('/', '').length === 0 ||
    (!value.startsWith('/') && !multiplyQuantityUsingNameRegex.test(value))
  ) {
    return;
  }

  originalSearchValue.value = value;

  const string = value.split('/')[1];
  selectInput[0].filterText = string;

  await handleFilter(string, 0, filterOr.value);

  searchString.value = string;

  selectRef.value.showPopup();
};
const handleInputEnter = async (string) => {
  const rNumberRegex = /^[Rr](?!0$)(?!0\d)\d+(?:,\d+)?\*(?!0$)\d+$/;
  const numberNumberRegex = /^(?!0$)(?!0\d)\d+(?:,\d+)?\*\S+$/;
  const onlyNumbersRegex = /^\d+$/; // Test if only numbers (codigo ou EAN)
  const numberStringAndSpace = /^[a-zA-Z0-9 ]+$/; // Test if only number
  const davRegex = /\b(?:DP|DO|DC|OS)\d+\b/i; // Test if string starts with DP, DC or DO (dav)

  if (rNumberRegex.test(string)) {
    let [precoTotal, controle] = string.split('*');
    precoTotal = precoTotal.replace(/r/gi, '');

    emit('fetchWithMoney', { string, precoTotal, controle });
    return;
  }

  if (numberNumberRegex.test(string)) {
    const [quantity, controle] = string.split('*');

    if (controle[0] == '/') {
      selectRef.value.updateInputValue(controle);

      await filterFn(controle);

      return;
    }

    emit('fetchWithQuantity', { string, quantity, controle });
    return;
  }

  if (davRegex.test(string)) {
    const controleDav = string.replace(/\D/g, '');
    const davType = string.replace(/\d/g, '');
    const davs = {
      do: 1,
      dc: 2,
      dp: 3,
      os: 5
    };
    if (davs[davType] && global.roles?.includes('VENDA.PDV:IMPORTAR')) {
      const isOs = davs[davType] === 5;
      emit('importDav', controleDav, davs[davType], isOs);
      return;
    }
  }

  if (
    onlyNumbersRegex.test(string) ||
    (numberStringAndSpace.test(string) && configPdv.value?.buscaPadrao == '5')
  ) {
    emit('fetchEanControle', string);
    return;
  }

  if (string && model.value && typeof model.value == 'string') {
    $q.notify({
      position: 'top',
      color: 'blue',
      message: 'Entrada inválida'
    });
    selectText();
  }
  isRequesting.value = false;
};

// Opções do select.
const filtered_options = computed(() =>
  selectInput[0].filterText
    ? selectInput[0].filteredSelectOptions
    : selectOptions.value
);

/**
 * Filtra as opções de acordo com o digitado após '/' e abre o menu.
 * @param {string} value O valor inserido no campo de busca.
 * @returns {Promise<void>} Uma promessa que é resolvida quando o filtro é aplicado.
 */
/**
 * Função que impede o display do menu caso não exista '/' no input.
 */
const handleShowPopup = () => {
  const selectLabel = selectRef.value.$el;
  const input = selectLabel.querySelector('input');

  if (input) {
    const shouldShowPopup = input.value?.split('/')?.[1]?.length > 0;

    if (!shouldShowPopup) {
      selectRef.value.hidePopup();
    }
  }
};
// Instancia do composable de selects que lida com ordernação, filtros e também infiniteScroll
const { updateScroll, handleFilter, loading, selectOptions } = useApiOptions(
  selectInput,
  props.store,
  props.storeKey,
  filters,
  orderBy.value ? orderBy : {},
  deletedAt.value,
  false
);

/**
 * Mantém o foco no campo.
 */
const focus = () => {
  nextTick(() => {
    const input = selectRef.value?.$el.querySelector('input');
    if (input) input.focus();
  });
  isRequesting.value = false;
};

/**
 * Seleciona o texto do input.
 */
const selectText = () => {
  selectRef?.value.$el?.querySelector('input')?.select();
  isRequesting.value = false;
};

/**
 * Limpa o texto do input.
 */
const clearText = () => {
  model.value = '';
  selectRef?.value.updateInputValue('');
  isRequesting.value = false;
};

/**
 * Atribui string para texto do input.
 * @param {string} text
 */
const setText = (text) => {
  selectRef?.value.updateInputValue(text);
  isRequesting.value = false;
};

defineExpose({ focus, selectText, clearText, setText });

onUnmounted(() => {
  if (scrollEvent) document.removeEventListener('scroll', scrollEvent);
});
</script>
<style scoped>
div.q-field__inner.relative-position.col.self-stretch {
  height: 65px !important;
}
</style>
