<template>
  <!----- INÍCIO PÁGINA DE CADASTRO  ----->
  <SGPage id="pdv-page">
    <!----- INÍCIO SKELETON DE PDV  ----->
    <PdvSkeleton
      v-if="
        loadingNfce.table || loadingPdvConfig.table || loadingFirstSteps.table
      "
    />
    <!----- FIM SKELETON DE PDV  ----->

    <div v-else>
      <!----- INÍCIO WARNING DE CERTIFICADO E CONFIGURACOES ----->
      <SGCard
        v-if="showNfceWarning"
        :is-sticky-scroll="false"
        id="general-issuance-warning"
        class="tw-my-4 tw-border-2 tw-border-SGBRYellow"
      >
        <div class="tw-flex tw-gap-4">
          <q-icon
            class="tw-mx-2 tw-text-SGBRYellow"
            size="4rem"
            name="error"
            :class="{
              '-tw-mx-2 ': $q.screen.lt.md
            }"
          />
          <div>
            <h2>
              Adicione as configurações e certificado para emitir NFC-e (nota
              fiscal de consumidor eletrônica)
            </h2>
            <p class="tw-mb-2">
              *É essencial finalizar as configurações da NFC-e para emitir nota
              fiscal, pois ele garante que seu sistema não será bloqueado para
              emissão de notas proporcionando segurança tanto para o emissor
              quanto para o destinatário.
            </p>
            <p class="tw-mb-2">
              *Um certificado é essencial para emitir nota fiscal, pois ele
              garante a autenticidade e a integridade das informações presentes
              na nota, proporcionando segurança tanto para o emissor quanto para
              o destinatário.
            </p>
            <q-btn
              class="tw-text-SGBRBlueLighten"
              style="border: 1px solid #8dbbff; min-height: 15px"
              flat
              label="Configurar agora"
              to="/configuracoes-emissao"
            />
            <span
              v-if="!pdvConfigData?.[0]?.permitirFinalizarVendaSemEmissaoNfce"
            >
              ou
              <q-btn
                class="tw-text-SGBRBlueLighten"
                style="border: 1px solid #8dbbff; min-height: 15px"
                flat
                label="Permitir finalizar venda sem emissão de NFC-e"
                @click="handleAllowPdvWithoutNfce"
              />
            </span>
          </div>
        </div>
      </SGCard>
      <!----- FIM WARNING DE CERTIFICADO E CONFIGURACOES ----->

      <!----- INÍCIO CARD DE WARNING DE PRODUTO ----->
      <SGCard
        v-if="!isFullscreen && !firstStepData.produto"
        :is-sticky-scroll="false"
        id="general-issuance-warning"
        class="tw-mb-4 tw-border-2 tw-border-SGBRYellow"
      >
        <div class="tw-flex tw-gap-4">
          <q-icon
            class="tw-mx-2 tw-text-SGBRYellow"
            size="4rem"
            name="error"
            :class="{
              '-tw-mx-2 ': $q.screen.lt.md
            }"
          />
          <div>
            <h2>Adicione um produto para emitir notas fiscais</h2>
            <p class="tw-mb-2">
              *Um produto é nescessário para emitir nota fiscal, pois ele
              garante que você possui um estoque e estará presente na nota,
              proporcionando segurança tanto para o emissor quanto para o
              destinatário.
            </p>
            <q-btn
              class="tw-ml-auto tw-text-SGBRBlueLighten"
              style="border: 1px solid #8dbbff; min-height: 15px"
              flat
              label="Adicionar agora"
              to="/produto"
            />
          </div>
        </div>
      </SGCard>
      <!----- FIM CARD DE WARNING DE PRODUTO ----->

      <!----- INÍCIO CARDS E MODAIS DA TELA ----->
      <div>
        <div>
          <!----- INÍCIO MODAL TIPO IMPORTAÇÃO DE DAVS ----->
          <ImportDavTypeModal
            v-if="isDavImportModalOpened"
            v-model="isDavImportModalOpened"
            @on-import="
              async (data) => {
                isDavImportModalOpened = false;
                const dav = await handleFetchDav(
                  data.controle,
                  false,
                  data?.importarProdutoServicoOuApenasProduto,
                  data?.isOs ?? false
                );
                if (dav) {
                  if (!data?.isOs) {
                    dav.controle = data.controle;
                  }
                  handleDavImport(dav);
                }
              }
            "
            @on-cancel="() => (isDavImportModalOpened = false)"
          />
          <!----- FIM MODAL DE IMPORTAÇÃO DE DAVS ----->

          <!----- MODAL DE ATALHOS ----->
          <MiniModal
            v-if="showPdvShortkeysModal"
            v-model="showPdvShortkeysModal"
            scope="atalhos"
            :has-cancel="false"
            :has-save="false"
            title="Atalhos"
          >
            <PdvShortkeys />
          </MiniModal>

          <!----- INÍCIO DO GRID DA TELA ----->
          <div
            class="tw-grid tw-grid-cols-12 tw-gap-customGridGapCards"
            ref="gridContainerRef"
            :class="[
              {
                'disabled-content': !allowUsePdv
              }
            ]"
          >
            <!----- INÍCIO CARD SUPERIOR DE BOTÕES ----->
            <div
              class="sgcard tw-col-span-12 tw-flex tw-scroll-mt-24 tw-flex-col tw-rounded-md tw-bg-white tw-p-4 tw-shadow-md xl:tw-col-span-9 xl:tw-self-center"
              id="pdv-page-menus"
            >
              <div
                class="tw-flex tw-flex-wrap tw-items-center tw-justify-start tw-gap-customGridGapCards md:tw-justify-between"
              >
                <div
                  class="tw-flex tw-flex-wrap tw-items-center tw-gap-customGridGapCards"
                >
                  <q-btn
                    flat
                    dense
                    unelevated
                    class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
                    @click="handleImportarDocumento"
                    :disabled="!noData"
                    v-authorized="'VENDA.PDV:IMPORTAR'"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase"
                      v-if="showShortKey"
                      >f4
                    </span>
                    <TooltipCustom
                      text-tooltip="Faça a importação e/ou mesclagem de documentos auxiliares de venda (DAV)"
                    />
                    IMPORTAR DOCUMENTOS
                  </q-btn>
                  <q-btn
                    flat
                    dense
                    unelevated
                    :disabled="isFullscreen"
                    class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
                    @click="handleAbrirListagem"
                    v-authorized="'VENDA.LISTAGEM-PDV:MENU'"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase"
                      v-if="showShortKey"
                      >f10
                    </span>
                    <TooltipCustom
                      :text-tooltip="
                        isFullscreen
                          ? 'Desabilite a tela cheia para acessar a listagem'
                          : 'Listagem de todas as vendas confirmadas'
                      "
                    />
                    LISTAGEM
                  </q-btn>

                  <q-btn
                    flat
                    dense
                    unelevated
                    class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
                    @click="openNfcConfig"
                    :disabled="!noData"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase"
                      v-if="showShortKey"
                      >f11
                    </span>
                    <TooltipCustom text-tooltip="Configurações do PDV" />
                    Configurações
                  </q-btn>
                  <q-btn
                    flat
                    dense
                    unelevated
                    class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
                    :disable="!noData"
                    @click="handleFechamentoCaixa"
                    v-authorized="'VENDA.PDV:FECHAMENTO-CAIXA'"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase"
                      v-if="showShortKey"
                      >f3
                    </span>
                    <TooltipCustom text-tooltip="Fechamento de caixa" />
                    Fechamento caixa
                  </q-btn>

                  <q-btn
                    flat
                    dense
                    unelevated
                    class="tw-bg-SGBRBlueLighten tw-px-2 tw-text-white"
                    @click="showPdvShortkeysModal = true"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase"
                      v-if="showShortKey"
                      >ctrl+.
                    </span>
                    <TooltipCustom
                      text-tooltip="Atalhos para facilitar navegação na tela de PDV"
                    />
                    Atalhos
                  </q-btn>
                </div>
                <div class="tw-flex tw-items-center">
                  <span
                    class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                    v-if="showShortKey"
                    >{{ altOrOption }}+f11
                  </span>
                  <FullscreenIcon
                    size="25px"
                    @click="switchToFullscreen"
                    class="tw-hidden md:tw-block"
                  >
                    <TooltipCustom text-tooltip="Entrar em modo tela cheia" />
                  </FullscreenIcon>
                </div>
              </div>
            </div>
            <!----- FIM CARD SUPERIOR DE BOTÕES ----->

            <!----- INÍCIO CARD PRINCIPAL COM TABELA ----->
            <SGCard
              class="tw-col-span-12 tw-col-start-1 tw-row-span-1 tw-row-start-2 xl:tw-col-span-9 xl:tw-row-span-11"
              id="pdv-page-main"
            >
              <div
                class="tw-relative tw-flex tw-h-full tw-flex-col tw-justify-between"
              >
                <div
                  v-if="global?.company?.caminhoLogo"
                  class="tw-absolute tw-left-[50%] tw-top-[60%] tw-z-10 tw-translate-x-[-50%] tw-translate-y-[-50%] tw-opacity-5"
                >
                  <img
                    :src="global?.company?.caminhoLogo ?? ''"
                    style="height: 100%; width: 100%"
                    class="tw-rounded-md tw-object-contain"
                  />
                </div>

                <div
                  class="tw-my-2 tw-flex tw-w-full tw-flex-col tw-items-center tw-gap-customGridGapCards md:tw-flex-row"
                >
                  <q-btn
                    v-if="!apenasLiberarEdicaoDeDadosFiscais"
                    unelevated
                    dense
                    icon="format_list_bulleted"
                    class="tw-h-fit tw-w-full tw-justify-center tw-bg-SGBRWhiten tw-text-SGBRBlueLighten md:tw-h-full md:tw-w-fit md:tw-bg-SGBRWhite"
                    :padding="'0px 7px'"
                    size="17px"
                    @click="openItemSelection"
                    v-authorized="'VENDA.PDV:IMPORTAR'"
                  >
                    <span
                      class="tw-mx-1 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.5em] tw-uppercase tw-text-white"
                      v-if="showShortKey"
                      >ctrl+p
                    </span>
                    <TooltipCustom
                      text-tooltip="Visualizar e selecionar produtos cadastrados"
                    />
                  </q-btn>

                  <PdvSelect
                    ref="itemSearchRef"
                    v-authorized="'VENDA.PDV:CADASTRAR'"
                    class="pdvSelect tw-w-full"
                    :store="stock"
                    :placeholder="'INSIRA O CÓDIGO'"
                    :filters="stockFilters"
                    :scope="hotkeyScope"
                    :config-pdv="configPdv"
                    store-key="descricaoCodigo"
                    :filter-or="['controle', 'nome']"
                    :label-select="labelBuscaProduto"
                    :value="stockEditValue"
                    @selected="
                      (item) => {
                        if (item && verifyStock(item)) {
                          handleAddItem(item);
                        }
                      }
                    "
                    @fetch-with-money="handleFetchWithMoney"
                    @fetch-with-quantity="handleFetchWithQuantity"
                    @fetch-ean-controle="handleFetchEanControle"
                    @import-dav="
                      async (controleDav, codTipoDav, isOS) => {
                        if (!davAlreadyImported) {
                          const dav = await handleFetchDav(
                            controleDav,
                            codTipoDav,
                            'produtosServicos',
                            isOS
                          );
                          if (dav) handleDavImport(dav);
                        } else {
                          isTableLoading = false;
                          itemSearchRef.selectText();
                          notify('Já existe um documento aberto');
                        }
                      }
                    "
                    :loading="isTableLoading"
                    :readonly="
                      isTableLoading || apenasLiberarEdicaoDeDadosFiscais
                    "
                  />
                </div>
                <PdvItemsTable
                  ref="pdvItemsTableRef"
                  :apenas-dados-fiscais="apenasLiberarEdicaoDeDadosFiscais"
                  :disable-delete-item="disableDeleteItem"
                  :rows="items"
                  :loading="isTableLoading"
                  :reserva-estoque="produtoReservaEstoque"
                  :config-pdv="configPdv"
                  @open-serial="
                    (product) => {
                      verifySerialGradeLote(product);
                    }
                  "
                  @open-grade="
                    (product) => {
                      verifySerialGradeLote(product);
                    }
                  "
                  @open-lote="
                    (product) => {
                      verifySerialGradeLote(product);
                    }
                  "
                  @delete-item="deleteItemPermission"
                  @open-discount-modal="(row) => openDiscountModal(row)"
                  @open-informacoes-adicionais="
                    (row) => openInformacoesAdicionaisModal(row)
                  "
                  @open-beneficio-fiscal="
                    (row) => openBeneficioFiscalModal(row)
                  "
                  @update-valor-bruto="
                    (item) => {
                      items[item.index].valorBruto = item.valorBruto;
                    }
                  "
                  @updated-selected="
                    (selected) => {
                      itemsSelected = selected;
                    }
                  "
                />
              </div>

              <div
                v-if="recentlyAddedProduct"
                class="tw-border-t-1 tw-mt-auto tw-flex tw-items-center tw-justify-start tw-rounded tw-bg-SGBRGrayBG tw-py-2 tw-pl-4 tw-uppercase"
              >
                <span class="tw-text-[16px] tw-font-bold">Último item: </span>
                <span class="tw-mx-2 tw-text-[16px]">
                  {{ recentlyAddedProduct.nome }}
                </span>
              </div>
            </SGCard>
            <!----- FIM CARD PRINCIPAL COM TABELA ----->

            <!----- INÍCIO CARD LATERAL COM TOTALIZADORES ----->
            <SGCard
              title=" "
              :has-circle="false"
              :has-title-separator="false"
              class="tw-col-span-12 tw-col-start-1 tw-row-span-1 !tw-pt-2 xl:tw-col-span-3 xl:tw-col-start-10 xl:tw-row-span-12 xl:tw-row-start-1"
              id="pdv-page-buy-info"
            >
              <div
                class="tw-flex tw-h-full tw-flex-col tw-justify-between tw-gap-customGridGapCards"
              >
                <div>
                  <SelectInput
                    ref="employeeRef"
                    :store="selectEmployeeStore"
                    :filters="selectEmployeeFiltersV2"
                    :filter-or="['controle', 'nome']"
                    :order-by="{ controle: 'asc' }"
                    :value="employeeValue"
                    store-key="descricaoCodigo"
                    label-select="Vendedor"
                    button-add
                    required
                    :disabled="apenasLiberarEdicaoDeDadosFiscais"
                    :class="
                      !global.roles?.includes('PESSOA:CADASTRAR')
                        ? 'tw-mb-2'
                        : ''
                    "
                    hide-bottom-space
                    @on-click-add="() => openEmployeeRegister()"
                    role-prefix="FUNCIONARIO"
                    @value="
                      (value) => {
                        setFieldValue(
                          'venda.codFuncionarioResponsavel',
                          value?.controle
                        );
                        employeeValue = value;
                      }
                    "
                  />

                  <q-checkbox
                    v-if="!apenasLiberarEdicaoDeDadosFiscais"
                    v-authorized="'PESSOA:CADASTRAR'"
                    v-model="isClientRegistered"
                    @click="
                      () => {
                        setFieldValue('venda.codCliente', '');
                        cnpjCpf = '';
                        clientValue = '';
                        isClientLoading = false;
                        clientName = '';
                        newDocValue = '';
                      }
                    "
                    label="Cliente cadastrado"
                    class="tw-mt-2 tw-w-fit"
                    dense
                  />

                  <CPFCNPJ
                    v-if="
                      !isClientRegistered &&
                      global.roles?.includes('PESSOA:CADASTRAR')
                    "
                    label="CPF/CNPJ"
                    dense
                    hide-bottom-space
                    outlined
                    v-model="cnpjCpf"
                    class="docCnpjCpf tw-relative tw-col-span-2 tw-mb-1"
                    :loading="isClientLoading"
                    :disable="
                      isClientLoading || apenasLiberarEdicaoDeDadosFiscais
                    "
                    @update:model-value="
                      (value) => {
                        if (newDocValue != value) {
                          newDocValue = value;
                          handleDocRegister(value);
                        }
                      }
                    "
                  />

                  <SelectInput
                    v-if="
                      !global.roles?.includes('PESSOA:CADASTRAR') ||
                      isClientRegistered
                    "
                    ref="clientRef"
                    :store="client"
                    :filters="clientFilters"
                    :relationships="[
                      'pessoaWithEndereco',
                      'pessoaWithPessoaTipoCadastro.tipoCadastro'
                    ]"
                    :filter-or="['controle', 'razaosocial', 'cnpjcpf']"
                    :value="clientValue"
                    store-key="descricaoCodigo"
                    label-select="Cliente"
                    :disabled="apenasLiberarEdicaoDeDadosFiscais"
                    editable
                    class="tw-mb-1"
                    :block-editable="['1']"
                    @editable="(option) => openRegisterPeople(option?.controle)"
                    button-add
                    @on-click-add="() => openRegisterPeople()"
                    role-prefix="PESSOA"
                    @value="
                      (value) => {
                        setFieldValue(
                          'venda.codCliente',
                          value?.controle || null
                        );
                        clientValue = value;
                      }
                    "
                  />

                  <InputPriceOrPercent
                    ref="priceOrPercentAcrescimoRef"
                    label="Acréscimo"
                    :readonly="apenasLiberarEdicaoDeDadosFiscais"
                    v-bind="forms.venda.acrescimo"
                    :toggle-type="tipoValorAcrescimo"
                    outlined
                    hide-bottom-space
                    class="tw-mb-1"
                    stack-label
                    dense
                    :max="9999999.99"
                    @update:model-value="
                      (value) => {
                        setFieldValue('venda.acrescimo', value);
                        valorAcrescimo = value;
                      }
                    "
                    @toggle-click="
                      (toggleType) => {
                        valorAcrescimo = 0;
                        setFieldValue('venda.acrescimo', valorAcrescimo);
                        tipoValorAcrescimo = toggleType.value;
                        setFieldValue(
                          'venda.tipoValorAcrescimo',
                          toggleType.value
                        );
                      }
                    "
                  />

                  <InputPriceOrPercent
                    ref="priceOrPercentDescontoRef"
                    label="Desconto"
                    :readonly="
                      apenasLiberarEdicaoDeDadosFiscais || disableDeleteItem
                    "
                    v-bind="forms.venda.desconto"
                    :toggle-type="tipoValorDesconto"
                    class="tw-mb-1"
                    hide-bottom-space
                    outlined
                    :key="items.length"
                    stack-label
                    dense
                    :max="9999999.99"
                    :disable-percent="disableDeleteItem"
                    @update:model-value="
                      (value) => {
                        setFieldValue('venda.desconto', value);
                        valorDesconto = value;
                      }
                    "
                    @toggle-click="
                      (toggleType) => {
                        valorDesconto = 0;
                        setFieldValue('venda.desconto', valorDesconto);
                        tipoValorDesconto = toggleType.value;
                        setFieldValue(
                          'venda.tipoValorDesconto',
                          toggleType.value
                        );
                      }
                    "
                  />

                  <InputPriceOrPercent
                    ref="priceOrPercentFreteRef"
                    label="Frete"
                    :readonly="apenasLiberarEdicaoDeDadosFiscais"
                    v-bind="forms.venda.frete"
                    :toggle-type="tipoValorFrete"
                    hide-bottom-space
                    class="tw-mb-1"
                    outlined
                    stack-label
                    dense
                    :max="9999999.99"
                    @update:model-value="
                      (value) => {
                        setFieldValue('venda.frete', value);
                        valorFrete = value;
                      }
                    "
                    @toggle-click="
                      (toggleType) => {
                        valorFrete = 0;
                        setFieldValue('venda.frete', valorFrete);
                        tipoValorFrete = toggleType.value;
                        setFieldValue('venda.tipoValorFrete', toggleType.value);
                      }
                    "
                  />
                </div>

                <div
                  v-if="global?.company?.caminhoLogo"
                  class="tw-rounded-md max-md:tw-hidden"
                >
                  <img
                    :src="global?.company?.caminhoLogo ?? ''"
                    style="height: 100%; width: 100%"
                    class="tw-rounded-md tw-object-contain"
                  />
                </div>

                <div class="tw-flex tw-flex-col tw-text-textsSGBR-gray">
                  <div class="tw-mb-2 md:tw-my-2">
                    <div>
                      Total itens:
                      {{ items.filter((row) => !row?.deletar)?.length }}
                    </div>
                    <div>
                      Total lançados: {{ currencyFormat(round(totalItems, 2)) }}
                    </div>
                  </div>
                  <div
                    class="tw-col-span-full tw-flex tw-min-h-[160px] tw-flex-col tw-rounded tw-border-2 tw-border-solid tw-border-SGBRWhiten tw-p-3"
                  >
                    <div
                      class="tw-my-1 tw-flex tw-flex-wrap tw-justify-between"
                    >
                      <p class="tw-text-inherit">Subtotal</p>
                      <p class="tw-text-inherit">
                        R$ {{ currencyFormat(productsTotal) }}
                      </p>
                    </div>
                    <div
                      class="tw-my-1 tw-flex tw-flex-wrap tw-justify-between"
                    >
                      <p class="tw-text-inherit">Desconto</p>
                      <p class="tw-text-inherit">
                        R$ {{ currencyFormat(descontoNota) }}
                      </p>
                    </div>
                    <div
                      class="tw-my-1 tw-flex tw-flex-wrap tw-justify-between"
                    >
                      <p class="tw-text-inherit">Acréscimo</p>
                      <p class="tw-text-inherit">
                        R$ {{ currencyFormat(acrescimoNota) }}
                      </p>
                    </div>
                    <div
                      class="tw-my-1 tw-flex tw-flex-wrap tw-justify-between"
                    >
                      <p class="tw-text-inherit">Frete</p>
                      <p class="tw-text-inherit">
                        R$ {{ currencyFormat(freteNota) }}
                      </p>
                    </div>

                    <q-separator spaced class="tw-bg-SGBRWhiten" size="1px" />

                    <div class="tw-flex tw-flex-wrap tw-justify-between">
                      <p class="tw-text-[20px] tw-font-bold tw-text-inherit">
                        Total
                      </p>
                      <p class="tw-text-[20px] tw-font-bold tw-text-inherit">
                        R$ {{ currencyFormat(valorTotalNota) }}
                      </p>
                    </div>
                  </div>
                  <div
                    class="tw-col-span-full tw-flex tw-w-full tw-items-center tw-justify-end tw-gap-2 tw-py-2 max-[700px]:tw-flex-col min-[700px]:tw-flex-wrap"
                  >
                    <div
                      class="tw-flex tw-w-full tw-items-end tw-justify-end tw-gap-2 min-[700px]:tw-w-fit"
                    >
                      <q-btn
                        flat
                        unelevated
                        dense
                        class="tw-bg-tablesSGBR-lightRed tw-px-2"
                        :disabled="noData"
                        @click="handleDeletePdv"
                        v-authorized="'VENDA.PDV:EXCLUIR'"
                      >
                        <span
                          class="tw-mx-1 tw-mr-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                          v-if="showShortKey"
                          >f7
                        </span>
                        <svg
                          width="14"
                          height="18"
                          viewBox="0 0 16 21"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M16 1.14286H12L10.8571 0H5.14286L4 1.14286H0V3.42857H16M1.14286 18.2857C1.14286 18.8919 1.38367 19.4733 1.81233 19.902C2.24098 20.3306 2.82236 20.5714 3.42857 20.5714H12.5714C13.1776 20.5714 13.759 20.3306 14.1877 19.902C14.6163 19.4733 14.8571 18.8919 14.8571 18.2857V4.57143H1.14286V18.2857Z"
                            fill="#ffffff"
                          />
                        </svg>
                        <TooltipCustom
                          :text-tooltip="
                            allowSave
                              ? 'Cancelar venda aberta'
                              : 'Não há uma venda aberta para cancelar'
                          "
                        />
                      </q-btn>
                      <q-btn
                        v-if="!importOS"
                        flat
                        unelevated
                        dense
                        class="tw-cursor-pointer tw-bg-SGBRWhiten tw-px-1 tw-text-SGBRGray"
                        icon-right="save"
                        :disabled="!allowSave"
                        @click="save()"
                        v-authorized="'VENDA.PDV:CADASTRAR'"
                      >
                        <TooltipCustom
                          :text-tooltip="
                            allowSave
                              ? 'Salvar venda para continuar posteriormente'
                              : 'Preencha os campos obrigatórios e verifique itens adicionados para salvar'
                          "
                        />
                        <span
                          class="tw-mx-1 tw-mr-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                          v-if="showShortKey"
                          >{{ altOrOption }}+g
                        </span>
                      </q-btn>
                    </div>
                    <div
                      v-if="!reemissao"
                      class="tw-flex tw-w-full tw-justify-end tw-gap-2 min-[700px]:tw-w-fit"
                    >
                      <q-btn
                        v-if="
                          pdvConfigData?.[0]
                            ?.permitirFinalizarVendaSemEmissaoNfce
                        "
                        flat
                        unelevated
                        dense
                        :loading="isSaving"
                        class="!tw-w-full tw-cursor-pointer tw-bg-SGBRBlueLighten tw-px-2 tw-text-white min-[700px]:!tw-w-fit"
                        @click="save(true)"
                        :disabled="!allowSave"
                        v-authorized="'VENDA.PDV:CADASTRAR'"
                      >
                        <span
                          class="tw-mx-1 tw-mr-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                          v-if="showShortKey"
                          >f5
                        </span>
                        Finalizar
                        <TooltipCustom
                          :text-tooltip="
                            allowSave
                              ? 'Finalizar venda'
                              : 'Preencha os campos obrigatórios e verifique itens adicionados para salvar'
                          "
                        />
                      </q-btn>
                      <q-btn
                        flat
                        unelevated
                        dense
                        :loading="isSaving"
                        class="tw-flex !tw-min-h-fit !tw-w-full tw-cursor-pointer tw-flex-wrap tw-bg-SGBRBlueMedium tw-px-2 tw-text-white min-[700px]:!tw-w-fit xl:tw-min-h-[48px] 2xl:!tw-min-h-fit"
                        @click="save(true, true)"
                        :disabled="!allowIssueNfce"
                        v-authorized="'VENDA.PDV:CADASTRAR'"
                      >
                        <span
                          class="tw-mx-1 tw-mr-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                          v-if="showShortKey"
                          >f9
                        </span>
                        Finalizar e emitir NFC-e
                        <TooltipCustom
                          :text-tooltip="
                            allowIssueNfce
                              ? 'Finalizar venda e emitir NFC-e'
                              : 'Adicione as configurações e certificado para emitir NFC-e. Verifique se itens foram adicionados.'
                          "
                        />
                      </q-btn>
                    </div>
                    <div v-else>
                      <q-btn
                        flat
                        unelevated
                        dense
                        :loading="isSaving"
                        class="w-full tw-flex tw-min-w-fit tw-cursor-pointer tw-flex-wrap tw-bg-SGBRBlueLighten tw-px-2 tw-text-white xl:tw-min-h-[48px] 2xl:!tw-min-h-fit"
                        @click="handleRejectedSale"
                        :disabled="!allowIssueNfce"
                        v-authorized="'VENDA.PDV:CADASTRAR'"
                      >
                        Finalizar e reemitir NFC-e
                        <TooltipCustom
                          :text-tooltip="
                            allowIssueNfce
                              ? 'Finalizar venda e emitir NFC-e'
                              : 'Adicione as configurações e certificado para emitir NFC-e. Verifique se itens foram adicionados.'
                          "
                        />
                        <span
                          class="tw-mx-1 tw-mr-2 tw-inline-block tw-rounded-md tw-bg-SGBRBlueDarken tw-px-2 tw-text-[0.7em] tw-uppercase tw-text-white"
                          v-if="showShortKey"
                          >f9
                        </span>
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </SGCard>
            <!----- FIM CARD LATERAL COM TOTALIZADORES ----->

            <!----- INICIO MODAL LOADING  ----->
            <MiniModal
              v-if="loadingModal"
              v-model="loadingModal"
              scope="loading"
              :has-cancel="false"
              :has-close-icon="false"
              :has-save="false"
            >
              <div
                class="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center"
              >
                <q-spinner
                  color="primary"
                  size="6em"
                  class="tw-mb-18 tw-m-24"
                  :thickness="5"
                />
                <p class="tw-pb-4 tw-text-sm tw-text-inherit">
                  Aguarde, reemitindo nota fiscal...
                </p>
              </div>
            </MiniModal>
            <!----- FIM MODAL LOADING  ----->

            <!----- INICIO MODAL CONTRATO PIX  ----->
            <MiniModal
              v-if="isPixContractModalOpened"
              v-model="isPixContractModalOpened"
              scope="contrato-pix"
              :has-cancel="false"
              :has-close-icon="false"
              :has-save="false"
            >
              <PixContract
                dont-show-again-button
                @on-save="
                  () => {
                    isPixContractModalOpened = false;
                    focusOnSelect();
                  }
                "
                @on-cancel="
                  () => {
                    isPixContractModalOpened = false;
                    focusOnSelect();
                  }
                "
                scope="contrato-pix"
              />
            </MiniModal>
          </div>
          <!----- FIM DO GRID DA TELA ----->
        </div>
      </div>
      <!----- FIM CARDS E MODAIS DA TELA ----->
    </div>
  </SGPage>
</template>

<script setup>
import SGCard from 'components/generic/SGCard.vue';
import SGPage from 'components/generic/SGPage.vue';
import CPFCNPJ from 'components/generic/input/CPFCNPJ.vue';
import InputPriceOrPercent from 'components/generic/input/PriceOrPercent.vue';
import SelectInput from 'components/generic/input/Select/index.vue';
import MiniModal from 'components/modal/MiniModal.vue';
import FechamentoCaixa from 'components/modal/pdv/FechamentoCaixa.vue';
import { currencyFormat } from 'components/utils/currencyFormat';
import { cnpj, cpf } from 'cpf-cnpj-validator';
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import MiniModalLayout from 'layouts/MiniModalLayout.vue';
import _, { debounce } from 'lodash';
import PdvSelect from 'pages/pdv/components/PdvSelect.vue';
import { storeToRefs } from 'pinia';
import { useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import PdvSkeleton from 'src/components/generic/skeletons/PdvSkeleton.vue';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import FullscreenIcon from 'src/components/icons/FullscreenIcon.vue';
import GradeVenda from 'src/components/modal/dav/GradeVenda.vue';
import ProductsList from 'src/components/modal/global/ProductsList.vue';
import AdditionDiscountModal from 'src/components/modal/pdv/AdditionDiscountModal.vue';
import BeforeRouteLeaveModal from 'src/components/modal/pdv/BeforeRouteLeaveModal.vue';
import FastClientRegister from 'src/components/modal/pdv/FastClientRegister.vue';
import ImportDavTypeModal from 'src/components/modal/pdv/ImportDavTypeModal.vue';
import IrregularCfop from 'src/components/modal/pdv/IrregularCfop.vue';
import NoStockItems from 'src/components/modal/pdv/NoStockItems.vue';
import PdvShortkeys from 'src/components/modal/pdv/PdvShortkeys.vue';
import ProdutoOuServicoImportDav from 'src/components/modal/pdv/ProdutoOuServicoImportDav.vue';
import SupervisorPassword from 'src/components/modal/pdv/SupervisorPassword.vue';
import PixContract from 'src/components/modal/pdv/pixContract/PixContract.vue';
import PaymentMethod from 'src/components/modal/sales/PaymentMethod.vue';
import notify from 'src/components/utils/notify.js';
import round from 'src/components/utils/round';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import { default as RegisterEmployee } from 'src/modules/funcionarios/views/register.vue';
import { default as RegisterPeople } from 'src/modules/pessoa/views/register.vue';
import PdvConfigPage from 'src/modules/vendas/pdv/views/config.vue';
import { returnObjOrNull, toFloat } from 'src/services/utils';
import { useNfce } from 'src/stores/api/nfce';
import { usePdvConfig } from 'src/stores/api/pdv/pdvConfig';
import { useSales } from 'src/stores/api/sales';
import { issuePrintNfce } from 'src/stores/api/sales/emitirNfce';
import { useSteps } from 'stores/api/firstStep';
import { usePeople } from 'stores/api/people';
import { useCalcularImpostoProdutos } from 'stores/api/sales/calcularImpostoProduto';
import { useGradeVenda } from 'stores/api/sales/gradeVenda';
import { useStock } from 'stores/api/stock/index';
import { useGlobal } from 'stores/global';
import { computed, nextTick, onMounted, ref, toRaw, watch } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import PdvItemsTable from './components/PdvItemsTable.vue';
import { useFormSchema } from './useFormSchema';
import audio from '/src/assets/audios/audio-pdv.wav';
import BeneficioFiscalModal from 'src/components/modal/pdv/BeneficioFiscalModal.vue';
import { useSelectEmployeeStore } from 'src/stores/api/selectEmployee';
import { isCstCsosn } from 'src/components/utils/checkCstCsosn';
import CstCsosnInvalidoNfceModal from 'src/components/modal/pdv/CstCsosnInvalidoNfceModal.vue';

// ------------------ Stores --------------------
const global = useGlobal();
const selectEmployeeStore = useSelectEmployeeStore();
const router = useRouter();
const route = useRoute();
const firstSteps = useSteps();
const stock = useStock();
const client = usePeople();
const sales = useSales();
const people = usePeople();
const $q = useQuasar();
const pdvConfig = usePdvConfig();
const configEmissaoStore = useNfce();
const gradeVendaStore = useGradeVenda();
const { forms, formValues, errors, setFieldValue, setValues, resetForm } =
  useFormSchema();

selectEmployeeStore.resetData();

let initialValues;

// ------------------ Filtros --------------------
const stockFilters = {
  deleted_at: {
    filterType: 'NULL'
  },
  relationships: [
    'produtoWithGrade',
    'produtoWithEstoque',
    'produtoWithFiscal',
    'produtoWithFiscal.produtoFiscalWithCsosn',
    'produtoWithFiscal.produtoFiscalWithCst',
    'produtoWithFiscal.produtoFiscalWithCfop',
    'produtoWithCaracteristica',
    'produtoWithCaracteristica.caracteristicaWithUnidadeMedida'
  ]
};

const clientFilters = {
  'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao': {
    filterType: 'ILIKE',
    filterValue: 'Cliente'
  }
};

// ------------------ Refs ------------------

const priceOrPercentAcrescimoRef = ref();
const priceOrPercentFreteRef = ref();
const priceOrPercentDescontoRef = ref();

// Shortkeys
const { showShortKey, altOrOption } = storeToRefs(global);
const showPdvShortkeysModal = ref(false);

// Áudio de inserção e remoção de item no PDV
const productAddedAudio = new Audio(audio);

// Usado no pdv select.
const stockEditValue = ref();

// Funcionário
const employeeRef = ref();
const employeeValue = ref();

const selectEmployeeFiltersV2 = {
  filtersV2: [
    {
      not_deleted: true,
      field: 'codUsuario',
      filterType: 'FIXED',
      filterValue: global?.user?.controle
    },
    {
      not_deleted: true,
      field: 'controle',
      filterType: 'FIXED',
      filterValue: 1
    }
  ]
};

// FIXED MANUAL (O SELECT ANTIGO NAO TEM TRATAMENTO)
// (NA PROXIMA REFATORACAO (NESSE CASO DEVE MUDAR O SCHEMA PRO MAIS NOVO, TROCAR O SELECT PRA INPUTSELECT, NAO UTILIZAR EMPLOYEEVALUE E TALS), POIS O FIXED IRÁ FUNCIONAR)
// MAS O FILTRO DIFERENTE DEVE SER USADO NA PROXIMA REFATORACAO
watch(
  () => selectEmployeeStore?.fixed,
  () => {
    employeeValue.value = selectEmployeeStore?.fixed;

    if (selectEmployeeStore?.fixed) {
      resetForm({
        values: {
          ...formValues,
          venda: {
            ...formValues.venda,
            codFuncionarioResponsavel: selectEmployeeStore?.fixed?.controle
          }
        }
      });
    }
  },
  { immediate: true }
);

// Contrato PIX
const isPixContractModalOpened = ref(false);
const loadingModal = ref(false);

// Cliente
const clientRef = ref();
const clientValue = ref();
const cnpjCpf = ref();
const newDocValue = ref('');
const isClientLoading = ref(false);
const isDocValid = ref(true);
const clientName = ref('');
const isClientRegistered = ref(false);

// Acréscimo, desconto e frete da nota.
const valorAcrescimo = ref(0);
const tipoValorAcrescimo = ref(0);
const valorFrete = ref(0);
const tipoValorFrete = ref(0);
const valorDesconto = ref(0);
const tipoValorDesconto = ref(0);

// Cálculo de impostos
const calcularImposto = useCalcularImpostoProdutos();

// Tabela PDV e campo select
const isTableLoading = ref(false);
const pdvItemsTableRef = ref();
const itemSearchRef = ref();
const itemsSelected = ref([]);
const apenasLiberarEdicaoDeDadosFiscais = ref(false);
const importOS = ref(false);
const disableDeleteItem = ref(false);

// Items e último adicionado
const items = ref([]);
const recentlyAddedProduct = ref();

// Controle de tela e responsividad
const isFullscreen = ref(false);
const gridContainerRef = ref();

const isSaving = ref(false);

// Pagamento
const valorPago = ref(0);
const mudancaValorFinal = ref(false);

// Validação dos dados para salvar / editar / importar DAV.
const davAlreadyImported = ref();
const isDavImportModalOpened = ref(false);
const isDavImportada = ref(false);
// ------------------ Requisições inicias ------------------
// Fazer verificação dos passos iniciais para cadastro de certificado de NFC-e e produtos cadastrados.

const { hasPixContract } = storeToRefs(global);
const { data: peopleData } = storeToRefs(people);
const { data: pdvConfigData, loading: loadingPdvConfig } =
  storeToRefs(pdvConfig);
const { data: firstStepData, loading: loadingFirstSteps } =
  storeToRefs(firstSteps);
const { data: configuracoesEmissao, loading: loadingNfce } =
  storeToRefs(configEmissaoStore);

initialValues = _.cloneDeep(formValues);

/**
 * Verifica a situação do contrato pix e abre modal se necessário. Foca no campo do PDV.
 * @returns {void} - Esta função não retorna nada.
 */
onMounted(async () => {
  const showPixContractWarning = localStorage.getItem('showPixContractWarning');
  if (
    !hasPixContract.value &&
    (showPixContractWarning === 'true' || !showPixContractWarning)
  ) {
    isPixContractModalOpened.value = true;
  }

  // Create an array of initial promises
  const initialPromises = [
    firstSteps.get(global.company.controle),
    pdvConfig.get(null, false, false),
    configEmissaoStore.get(null, false, false)
  ];

  if (route.query?.controle) {
    initialPromises.push(handleFetchDav(route.query?.controle));
  }

  const results = await Promise.all(initialPromises);

  if (route.query?.controle) {
    const dav = results[initialPromises.length - 1];
    if (dav) {
      dav.controle = route.query?.controle;
      allowImportPdv(dav);
    }
  }
});

// ------------------- Computed ------------------

const reemissao = computed(
  () => formValues.dfe?.status || formValues.venda?.vendaPaga
);

const configPdv = computed(() => pdvConfigData.value?.[0] ?? false);

const configEmissaoPadrao = computed(() => {
  return configuracoesEmissao.value?.producao?.padrao
    ? configuracoesEmissao.value?.producao
    : configuracoesEmissao.value?.homologacao;
});

// Permite usar o pdv
const allowUsePdv = computed(
  () =>
    (firstStepData.value.certificado &&
      firstStepData.value.produto &&
      configEmissaoPadrao.value) ||
    (firstStepData.value.produto &&
      configPdv.value.permitirFinalizarVendaSemEmissaoNfce)
);

// Retorna true caso nenhum dado seja preenchido
const noData = computed(
  () =>
    items.value.length === 0 &&
    !formValues.venda.codCliente &&
    !cnpjCpf.value &&
    !formValues.venda.acrescimo &&
    !formValues.venda.frete &&
    !formValues.venda.desconto
);

// Retorna true caso a venda esteja bloqueada para estoque negativo ou zerado.
const bloquearVendaEstoqueNegativoOuZerado = computed(() => {
  return configPdv.value.bloquearVendaEstoqueNegativoOuZerado;
});

// Verifica se as configurações de produto reservam estoque ou não.
const produtoReservaEstoque = computed(() => {
  return configPdv.value.reservaEstoque;
});

/**
 * Calcula o valor total do item reduzindo seu desconto, seja em porcentagem ou em dinheiro.
 * @param {Number} valorUnitario O preço unitário de venda do item.
 * @param {Number} qtde Quantidade do item.
 * @param {Number} tipoValorDesconto Tipo de desconto, 0 para R$ e 1 para %.
 * @param {Number} valorDesconto Valor do desconto.
 */
const calcValorLiquido = ({
  valorUnitario,
  qtde,
  valorDesconto,
  valorAcrescimo,
  tipoValorDesconto,
  tipoValorAcrescimo
}) => {
  const total = valorUnitario * (qtde ?? 0);
  let desconto = 0;
  let acrescimo = 0;
  valorDesconto = valorDesconto ?? 0;
  valorAcrescimo = valorAcrescimo ?? 0;

  // Calcula o valor total com base no tipo de desconto
  if (tipoValorDesconto === 0) {
    // Desconto em valor fixo
    desconto = round(valorDesconto ?? 0, 2);
  } else {
    // Desconto percentual
    desconto = round((total * valorDesconto) / 100, 2);
  }

  if (tipoValorAcrescimo === 0) {
    acrescimo = round(valorAcrescimo, 2);
  } else {
    acrescimo = round((total * valorAcrescimo) / 100, 2);
  }

  return round(total + acrescimo - desconto, 2);
};

const calcValorIcmsDeson = (produto) => {
  const aliqIcmsDeson = round(produto.fiscal.aliqIcmsDeson, 2);

  const valorIcmsDeson = round(produto.valorLiquido * (aliqIcmsDeson / 100), 2);

  return valorIcmsDeson;
};

// Calcula o valor total liquido e bruto dos itens e também o valor líquido de cada item.
const productsTotal = computed(() => {
  const totals = items.value
    .filter((item) => !item?.deletar)
    .reduce(
      (acc, product) => {
        const valorLiquido = calcValorLiquido(product);
        const valorBruto = round(product.qtde * product.valorUnitario, 2);

        acc.totalLiquido += valorLiquido;
        acc.totalBruto += valorBruto;

        return acc;
      },
      { totalLiquido: 0, totalBruto: 0 }
    );

  const totalLiquido = round(totals.totalLiquido, 2);
  const totalBruto = round(totals.totalBruto, 2);

  setFieldValue('venda.valorBruto', totalBruto);

  return totalLiquido;
});

// Desconto calculado da NOTA.
const descontoNota = computed(() => {
  if (!valorDesconto.value || !productsTotal.value) {
    return 0;
  }

  if (tipoValorDesconto.value === 0) {
    return round(valorDesconto.value, 2);
  } else {
    return round((productsTotal.value * valorDesconto.value) / 100, 2);
  }
});

// Acréscimo calculado da NOTA.
const acrescimoNota = computed(() => {
  if (!valorAcrescimo.value || !productsTotal.value) {
    return 0;
  }

  if (tipoValorAcrescimo.value === 0) {
    return valorAcrescimo.value;
  } else {
    return (productsTotal.value * valorAcrescimo.value) / 100;
  }
});

const freteNota = computed(() => {
  if (!valorFrete.value || !productsTotal.value) {
    return 0;
  }

  if (tipoValorFrete.value === 0) {
    return valorFrete.value;
  } else {
    return (productsTotal.value * valorFrete.value) / 100;
  }
});
// Valor líquido total da NOTA.
const valorTotalNota = computed(() => {
  const total = round(
    Number(productsTotal.value) +
      Number(freteNota.value ?? 0) +
      Number(acrescimoNota.value ?? 0) -
      Number(descontoNota.value ?? 0),
    2
  );

  const result = total > 0 ? total : 0;
  setFieldValue('venda.valorLiquido', result);
  return result;
});

// Totalizador da quantidade de items adicionados
const totalItems = computed(() => {
  if (items.value.length === 0) {
    return 0;
  }

  return items.value
    .filter((row) => !row?.deletar)
    .reduce((acc, item) => {
      return (acc += round(parseFloat(item.qtde), 2) ?? 0);
    }, 0);
});

// Verifica se há items, a quantidade e valor final pra permitir finalização
const allowSave = computed(() => {
  return (
    formValues.item.length > 0 &&
    formValues.venda.codFuncionarioResponsavel &&
    !isTableLoading.value &&
    !isClientLoading.value &&
    totalItems.value > 0 &&
    Object.keys(errors.value).length === 0 &&
    isDocValid.value &&
    valorTotalNota.value > 0
  );
});

const valorTotalProdutoServico = computed(() => {
  let productTotal = [];
  let serviceTotal = [];

  items.value?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorLiquido(item);
    }

    // Verificar se é um serviço, cujo controle é igual a 10
    if (item?.itemWithProduto.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);
    } else {
      productTotal.push(calc);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  return { productTotal: productTotalSum, serviceTotal: serviceTotalSum };
});
// Verifica se é possível finalizar e emitir NFC-e
const allowIssueNfce = computed(() => {
  return (
    allowSave.value &&
    firstStepData.value?.certificado &&
    configEmissaoPadrao.value
  );
});

// Verifica se é necessário mostrar o warning de configuração de NFC-e
const showNfceWarning = computed(
  () =>
    (!firstStepData.value.certificado || !configEmissaoPadrao.value) &&
    !configPdv.value.permitirFinalizarVendaSemEmissaoNfce &&
    !isFullscreen.value
);

// ------------------ Handlers and Functions ------------------
/**
 * Altera as configurações do PDV para permitir salvar sem emitir NFC-e.
 */
const handleAllowPdvWithoutNfce = async () => {
  const payload = pdvConfigData.value?.[0];
  if (payload.controle) {
    payload.permitirFinalizarVendaSemEmissaoNfce = true;
    await pdvConfig.put(payload, payload.controle);
    await pdvConfig.get(null, false, false);
  }
};

// Foca no input de select.
const focusOnSelect = () => {
  itemSearchRef.value?.focus();
};
const labelBuscaProduto = computed(() => {
  const buscaPadrao = configPdv.value.buscaPadrao;

  const nomeDaConst = {
    1: 'Busque o produto pelo código ou GTIN',
    2: 'Busque o produto pelo código do produto',
    3: 'Busque o produto pelo código de barras (GTIN)',
    4: 'Busque o produto pelo código de barras interno',
    5: 'Busque o produto pela referência',
    6: 'Busque o produto pelo código, GTIN ou código de barras interno',
    7: 'Busque o produto pelo código ou código de barras interno'
  };
  return (
    nomeDaConst[buscaPadrao] || 'Busque o produto pelo nome, código ou EAN'
  );
});
/**
 * Abre modal de configurações de
 */
const openNfcConfig = () => {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: PdvConfigPage,
      scope: 'config-nfc-dialog'
    }
  })
    .onOk(async () => {
      await pdvConfig.get(null, false, false);
      focusOnSelect();
    })
    .onCancel(() => {
      focusOnSelect();
    });
};

/**
 * Lida com o fluxo de fechamento de caixa
 */
const handleFechamentoCaixa = () => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: FechamentoCaixa,
      scope: 'fechamento-caixa',
      title: 'Fechamento de caixa',
      classesTopBox: '!tw-mr-6',
      hasCancel: false,
      hasSave: false,
      dataModal: {
        caixaCego: configPdv.value.caixaCego,
        funcionario: employeeValue.value
      }
    }
  })
    .onOk(() => focusOnSelect())
    .onCancel(() => focusOnSelect());

  focusOnSelect();
};

const handleImportarDocumento = () => {
  if (!davAlreadyImported.value && noData.value) {
    isDavImportModalOpened.value = true;
    itemSearchRef.value.selectText();
    return;
  }
  itemSearchRef.value.selectText();
  notify('Já existe um documento aberto');
};

const handleAbrirListagem = async () => {
  await sales.resetData();
  router.push('/vendas/pdv-listagem');
};

/**
 * Manipula o processo de registro para um determinado número de documento com debounce.
 * @param {string} docNumber - O número do documento para o qual o registro será manipulado.
 * @returns {Promise<void>} Uma Promessa que resolve quando o processo de registro for concluído.
 * @async
 */
const handleDocRegister = debounce(async (docNumber) => {
  // Verifica se o cnpj ou cpf é válido.
  if (
    (docNumber.length > 11 && !cnpj.isValid(docNumber)) ||
    (docNumber.length === 11 && !cpf.isValid(docNumber))
  ) {
    isDocValid.value = false;
    return;
  }

  isDocValid.value = true;
  isClientLoading.value = true;
  if (docNumber.length === 11) {
    await handleFetchOrRegisterClient(docNumber, 1);
  }

  if (docNumber.length === 14) {
    await handleFetchOrRegisterClient(docNumber, 2);
  }

  isClientLoading.value = false;
}, 400);

/**
 * Procura pelo registro de pessoas pelo número do documento. Se nenhum resultado for encontrado, abre um modal para registrá-lo.
 * @param {string} docNumber O número do CNPJ ou CPF.
 * @param {number} type O tipo de cadastro: 1 para cliente, 2 para fornecedor.
 * @returns {Promise<void>} Uma Promessa que resolve quando o processo de busca e registro for concluído.
 * @async
 */
const handleFetchOrRegisterClient = async (docNumber, type) => {
  // Define os filtros para buscar cliente
  people.table.filters = {
    deleted_at: {
      filterType: 'NULL'
    },
    cnpjCpf: {
      filterType: 'ILIKE',
      filterValue: docNumber
    },
    relationships: [
      'pessoaWithEndereco',
      'pessoaWithPessoaTipoCadastro.tipoCadastro'
    ]
  };

  await people.get();

  // Verifica se foi encontrado exatamente um registro de pesso, se sim, renderiza o select e popula o campo.
  if (peopleData.value?.length === 1) {
    isClientRegistered.value = true;
    nextTick(() => {
      clientValue.value = toRaw(peopleData.value?.[0]);
      setFieldValue('venda.codCliente', clientValue.value.controle ?? null);
      clientName.value = '';
    });
    notify('Cliente já possui cadastro', 'positive');
    return;
  }

  // Se nenhum registro de pessoa foi encontrado, abre modal para cadastrar.
  if (peopleData.value?.length === 0) {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: FastClientRegister,
        hasCancel: false,
        hasSave: false,
        classesTopBox: '!tw-justify-start',
        title: 'Cadastrar cliente',
        scope: 'cadastrar-cliente',
        dataModal: {
          clientName: clientName
        }
      }
    })
      .onOk(async () => {
        // Monta o payload para o registro do cliente
        const payload = {
          pessoaTipoCadastro: [{ codTipoCadastro: 1 }],
          pessoa: {
            razaoSocial: clientName.value,
            codPessoaTipoEntidade: type,
            cnpjCpf: docNumber
          }
        };
        // Realiza o registro do cliente
        await handleRegisterClient(payload);
        isClientLoading.value = false;
        clientName.value = '';
        focusOnSelect();
      })
      // Ao cancelar o cadastro
      .onCancel(() => {
        // Define que o cliente não está mais carregando e limpa o campo de nome do cliente
        isClientLoading.value = false;
        clientName.value = '';
        focusOnSelect();
      });
    return;
  }
};

/**
 * Registra um cliente e o define no campo de seleção de cliente.
 * @param {Object} payload Dados referentes ao cliente.
 */
const handleRegisterClient = async (payload) => {
  try {
    const response = await people.post(payload);
    if (response.data) {
      // Troca o campo cliente pro select e atualiza com a response.
      isClientRegistered.value = true;
      nextTick(() => {
        clientValue.value = {
          ...response.data.pessoa,
          ...response.data.pessoaTipoCadastro[0]
        };
        setFieldValue(
          'venda.codCliente',
          response.data.pessoa?.controle ?? null
        );
      });
      isClientLoading.value = false;
    }
  } catch (error) {
    console.error(error);
    notify('Cliente já possui cadastro');
  }
};

/**
 * Abre o registro de funcionário.
 */
const openEmployeeRegister = () => {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterEmployee,
      scope: 'register-employee'
    }
  })
    // Ao confirmar o registro de funcionário atualiza as opções do select
    .onOk(async () => {
      await updatePeopleOptions();
    });
  focusOnSelect();
};

/**
 * Abre o modal de registro de pessoas.
 */
const openRegisterPeople = (controle = null) => {
  let props = {};

  if (controle) {
    props = {
      data: {
        isEditing: true,
        controle
      },
      modal: true
    };
  }

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'register-people',
      ...props
    }
  }).onOk(async () => {
    await updatePeopleOptions();
  });
  focusOnSelect();
};

/**
 * Atualiza as opções de pessoas uma vez que uma é registrada.
 */
const updatePeopleOptions = async () => {
  employeeRef.value.mountSelectOptions();
  focusOnSelect();
};

/**
 * Define as variáveis de desconto a serem usadas no modal de desconto.
 * @param {Object} row Linha da tabela à qual o desconto será adicionado.
 */
const openDiscountModal = (row) => {
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: AdditionDiscountModal,
      scope: 'Desconto-acrescimo',
      title: 'Desconto e acréscimo do item',
      classesTopBox: '!tw-justify-start !tw-mb-2 !tw-mt-4',
      hasSave: false,
      hasCancel: false,
      dataModal: {
        item: items.value[row.index]
      }
    }
  }).onOk(({ index, controle, data }) => {
    applyItemAdditionDiscount(index, controle, data);
    focusOnSelect();
  });
};

/**
 * Aplica o desconto do modal de desconto ao item.
 */
const applyItemAdditionDiscount = (index, controle, data) => {
  // Itera sobre os itens para encontrar o item correspondente ao desconto
  items.value.forEach((item, i) => {
    if (i === index && item.controle === controle) {
      item.tipoValorDesconto = data.tipoValorDesconto;
      item.valorDesconto = data.desconto;
      item.tipoValorAcrescimo = data.tipoValorAcrescimo;
      item.valorAcrescimo = data.acrescimo;

      item.valorLiquido = calcValorLiquido(item);
      item.fiscal.valorIcmsDeson = calcValorIcmsDeson(item);
    }
  });
};

const openBeneficioFiscalModal = (row) => {
  const produto = items.value[row.index];

  const item = {
    index: produto?.index,
    controle: produto?.controle,
    cst: produto?.cstCsosnEditValue?.codigo || null,
    produto: produto?.itemWithProduto,
    item: produto,
    fiscal: {
      codBeneficio: produto?.fiscal?.codBeneficio,
      valorIcmsDeson: produto?.fiscal?.valorIcmsDeson,
      motivoIcmsDeson: produto?.fiscal?.motivoIcmsDeson,
      infosAdicionais: produto?.fiscal?.infosAdicionais
    }
  };

  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: BeneficioFiscalModal,
      scope: 'beneficio-fiscal',
      title: 'Dados do produto',
      classesTopBox: '!tw-justify-start !tw-mb-2 ',
      hasSave: false,
      hasCancel: false,
      dataModal: {
        item
      }
    }
  }).onOk(({ index, controle, data }) => {
    applyItemBeneficioFiscal(index, controle, data);
    focusOnSelect();
  });
};

/**
 * Altera informacoes adicionais do item
 */
const applyItemBeneficioFiscal = (index, controle, data) => {
  // Itera sobre os itens para encontrar o item correspondente ao controle
  items.value.forEach((item, i) => {
    if (i === index && item.controle === controle) {
      item.fiscal.codBeneficio = data.codBeneficio;
      item.fiscal.valorIcmsDeson = data.valorIcmsDeson;
      item.fiscal.motivoIcmsDeson = data.motivoIcmsDeson;
      item.fiscal.infosAdicionais = data.infosAdicionais;
    }
  });
};

/**
 * Verifica os dados de configuração de emissao e da venda importada para autorizar ou não.
 * Isso evita que uma venda rejeitada seja importada para o pdv sem que haja um certificado e permissão para reemissão.
 * @param {object} davf
 */
const allowImportPdv = (dav, noSuccessNotify = false) => {
  if (!dav.vendaWithDfe) {
    handleDavImport(dav, true, noSuccessNotify);
    return;
  }

  if (firstStepData.value?.certificado && configEmissaoPadrao.value) {
    handleDavImport(dav, true, noSuccessNotify);
    return;
  }

  notify(
    'Certifique-se que as configurações de emissão estejam preenchidas para abrir uma venda rejeitada'
  );
};

/**
 * Define as informações do DAV nos campos e adiciona seus itens.
 * @param {Object} dav Objeto DAV importado contendo todas as informações.
 */
const handleDavImport = async (dav, isPdv = false, noSuccessNotify = false) => {
  reset();

  isTableLoading.value = true;

  // Não foi possível importar a DAV.
  if (!dav) {
    notify('Não foi possível importar DAV');
    isTableLoading.value = false;
    itemSearchRef?.value.selectText();
    return;
  }

  // Já existe dav sendo importada.
  if (davAlreadyImported.value) {
    notify('Já existe uma nota em aberto');
    isTableLoading.value = false;
    itemSearchRef?.value.selectText();
    return;
  }

  // Status devolvido
  if (dav?.vendaWithDav?.codSituacao == '6') {
    notify('Este condicional já foi devolvido, impossível prosseguir');
    isTableLoading.value = false;
    itemSearchRef?.value.selectText();
    return;
  }

  // Itens inativos da DAV.
  if (
    dav.vendaWithItem?.filter((item) => item.vendaItemWithProduto?.deletedAt)
      .length > 0
  ) {
    notify('Documento possui um ou mais itens inativos');
    isTableLoading.value = false;
    itemSearchRef?.value.selectText();
    return;
  }

  // Venda bloqueada caso estoque negativo ou zerado.
  if (bloquearVendaEstoqueNegativoOuZerado.value && !isPdv) {
    const hasStock = dav.vendaWithItem?.every((item) => {
      if (produtoReservaEstoque.value) {
        return (
          Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtdeReal) > 0
        );
      }
      return Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtde) > 0;
    });
    if (!hasStock) {
      isTableLoading.value = false;
      notify('Venda bloqueada para estoque negativo ou zerado');
      itemSearchRef?.value.selectText();
      return;
    }
  }

  // Mapeia os itens do DAV e calcula o valor liquido de cada um com base no tipo de desconto
  const davItems = dav.vendaWithItem?.reduce((acc, item, index) => {
    if (Number(item.qtde) <= 0) return acc;

    // Objeto do item.
    const itemStructure = {
      controle: item.controle,
      codVenda: item.codVenda,
      codProduto: item.codProduto,
      qtde: Number(item.qtde), // Manipulável pelo usuário.
      qtdeEstoque: Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtde), // Em estoque no cadastro de produtos.
      qtdeReal: Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtdeReal), // Considerando reservas em vendas abertas.
      qtdeInicial: Number(item.qtde), // Inicial não manipulável.
      valorUnitario: Number(item.valorUnitario),
      valorDesconto: Number(item.valorDesconto) ?? 0,
      descontoVenda: 0,
      valorAcrescimo: Number(item.valorAcrescimo) ?? 0,
      tipoValorAcrescimo: Number(item.tipoValorAcrescimo) ?? 0,
      tipoValorDesconto: Number(item.tipoValorDesconto) ?? 0,
      unidade:
        item.vendaItemWithProduto?.produtoWithCaracteristica
          ?.caracteristicaWithUnidadeMedida?.descricao ?? null,
      nome: item.vendaItemWithProduto.nome,
      index,
      valorLiquido: calcValorLiquido({
        valorUnitario: Number(item.valorUnitario),
        qtde: Number(item.qtde),
        valorDesconto: Number(item.valorDesconto),
        valorAcrescimo: Number(item.valorAcrescimo),
        tipoValorDesconto: Number(item.tipoValorDesconto),
        tipoValorAcrescimo: Number(item.tipoValorAcrescimo)
      }),
      valorBruto: round(Number(item.qtde) * Number(item.valorUnitario), 2),

      // Impostos
      cfopEditValue:
        item.vendaItemWithFiscal?.vendaItemFiscalWithCfop ??
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCfop ??
        null,
      cstCsosnEditValue:
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCsosn ??
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCst ??
        null,
      originalCfop:
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCfop ??
        null,
      originalCstCsosn:
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCsosn ??
        item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCst ??
        null,
      fiscal: {
        codCfop: item?.vendaItemWithFiscal?.codCfop ?? null,
        codCstCsosn:
          item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCsosn
            ?.codigo ??
          item.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCst
            ?.codigo ??
          null,
        codBeneficio: item?.vendaItemWithFiscal?.codBeneficio,
        valorIcmsDeson: item?.vendaItemWithFiscal?.valorIcmsDeson,
        aliqIcmsDeson:
          item?.vendaItemWithProduto?.produtoWithFiscal?.aliqIcmsDeson || 0,
        motivoIcmsDeson: item?.vendaItemWithFiscal?.motivoIcmsDeson,
        infosAdicionais: item?.vendaItemWithFiscal?.infosAdicionais
      },
      itemWithProduto: item.vendaItemWithProduto ?? null,
      usaSerial: false,
      usaGrade:
        item.vendaItemWithProduto?.produtoWithCaracteristica?.usaGrade ?? false,
      usaLote: false
    };

    if (!validateCstCsosnProduct(itemStructure?.cstCsosnEditValue?.codigo)) {
      itemStructure.fiscal.codCstCsosn = null;
      itemStructure.cstCsosnEditValue = null;
      itemStructure.originalCstCsosn = null;
    }

    const configCfop = configPdv.value.cfop;
    if (!itemStructure.cfopEditValue && configCfop) {
      itemStructure.cfopEditValue = configCfop;
      itemStructure.fiscal.codCfop = configCfop?.controle;
      if (!itemStructure.originalCfop) itemStructure.originalCfop = configCfop;
    }

    acc.push(itemStructure);
    return acc;
  }, []);

  // Define os valores da venda e dos itens
  setValues({
    venda: {
      controle: dav.controle,
      davImportada: dav.controle,
      codCliente: dav.codCliente,
      codFuncionarioResponsavel: dav.codFuncionarioResponsavel,
      numOrcamento: dav.numOrcamento,
      numPedido: dav.numPedido,
      descricao: dav.descricao,
      observacoes: dav.observacoes,
      frete: Number(dav.frete),
      acrescimo: Number(dav.acrescimo),
      desconto: Number(dav.desconto),
      tipoValorFrete: Number(dav.tipoValorFrete),

      tipoValorAcrescimo: Number(dav.tipoValorAcrescimo),
      tipoValorDesconto: Number(dav.tipoValorDesconto),
      valorBruto: Number(dav.valorBruto),
      valorLiquido: Number(dav.valorLiquido),
      modulo: dav.modulo ?? null,
      vendaPaga: dav.vendaPaga,
      valorProdutos: Number(dav.valorProdutos),
      valorServicos: Number(dav.valorServicos)
    },
    item: [...davItems],
    dav: { ...dav.vendaWithDav }
  });

  if (dav.vendaWithDfe) {
    setFieldValue('dfe', { ...dav.vendaWithDfe });
  }

  if (isPdv || importOS.value) {
    setFieldValue('venda.davImportada', null);
    isDavImportada.value = false;
  } else {
    isDavImportada.value = true;
  }

  // Atribui valores inicias e adiciona os items da DAV.
  initialValues = _.cloneDeep(formValues);
  // Usa esse for para ser assíncrono e alterar o valor do item em davItems
  for (let i = 0; i < davItems.length; i++) {
    // await setEditSerial(davItems[i], isPdv);
    davItems[i] = await setEditGrade(davItems[i], isPdv);
    // davItems[i] = await setEditLote(davItems[i]);

    // As funções podem retornar null
    if (davItems[i]) {
      items.value.push(davItems[i]);
    } else {
      notify(
        'Venda possui iten(s) com grades irregulares. Estes itens não foram adicionados.'
      );
      noSuccessNotify = true;
    }
  }

  // Desconto da venda
  valorDesconto.value = Number(dav.desconto);
  tipoValorDesconto.value = Number(dav.tipoValorDesconto);
  valorAcrescimo.value = Number(dav.acrescimo);
  tipoValorAcrescimo.value = Number(dav.tipoValorAcrescimo);
  valorFrete.value = Number(dav.frete);
  tipoValorFrete.value = Number(dav.tipoValorFrete);

  // Adiciona os itens do DAV e define o funcionário responsável e o cliente
  employeeValue.value = dav.vendaWithFuncionarioResponsavel ?? '';
  // Troca o campo de cliente para select
  isClientRegistered.value = !!dav.pessoa;
  clientValue.value = dav.pessoa ?? '';
  davAlreadyImported.value = true;

  isTableLoading.value = false;

  if (isPdv) {
    router.replace('/vendas/pdv');
  }

  if (!noSuccessNotify) notify('Documento importado com sucesso', 'positive');
  focusOnSelect();
};

// /**
//  * Busca os seriais do item
//  * @param item
//  * @returns {Promise<void>}
//  */
// const setEditSerial = async (item, isPdv) => {
//   if (item.itemWithProduto?.produtoWithCaracteristica?.usaSerial) {
//     serialVendaStore.table.filters = {
//       codVendaItem: {
//         filterType: 'EQUALS',
//         filterValue: item.controle
//       }
//     };
//     const response = await serialVendaStore.get(null, false, false);
//     if (response.success && response.data?.rowsData) {
//       const seriais = response.data?.rowsData;

//       item.serial = seriais.map((serial) => {
//         if (isPdv) {
//           return serial;
//         }

//         if (serial.serialProduto?.disponivel) {
//           return serial;
//         } else {
//           return null;
//         }
//       });
//       item.serial = item.serial.filter((serial) => serial);

//       item.qtde = item.serial.length;
//       return item.qtde ? item : null;
//     }
//   }
//   return item;
// };

/**
 * Ajusta as quantidades e validações das grades.
 * @param item
 * @returns {Promise<void>}
 */
const setEditGrade = async (item, isPdv = false) => {
  if (item.itemWithProduto?.produtoWithCaracteristica?.usaGrade) {
    gradeVendaStore.table.filters = {
      codVendaItem: {
        filterType: 'EQUALS',
        filterValue: item.controle
      }
    };
    const response = await gradeVendaStore.get(null, false, false);
    if (response.success && response.data?.rowsData) {
      const grades = _.cloneDeep(response.data?.rowsData);
      item.grade = grades.map((grade) => {
        if (
          isPdv &&
          produtoReservaEstoque.value &&
          bloquearVendaEstoqueNegativoOuZerado.value
        ) {
          grade.quantidadeMaxima =
            Number(grade.quantidade) +
            Number(grade.produtoGrade?.quantidadeReal);
          return grade;
        } else {
          return ajustaQuantidadeGrade(grade, Number(grade.quantidade));
        }
      });
      item.grade = item.grade.filter((grade) => grade);
    }

    if (item.grade.length) {
      item.qtde = item.grade.reduce(
        (acc, grade) => (acc += Number(grade.quantidade)),
        0
      );
    }

    return item.grade.length ? item : null;
  }
  return item;
};

const handleImportarProdutosServicos = async (dav) => {
  const davPossuiServicos = dav.vendaWithItem.some(
    (item) =>
      item?.vendaItemWithProduto?.produtoWithCaracteristica?.codTipoUso == '10'
  );

  if (dav?.vendaWithDav?.codTipoDav == '1' && davPossuiServicos) {
    return await new Promise((resolve) => {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: ProdutoOuServicoImportDav,
          scope: 'produto-servico',
          title: 'Importar DAV',
          hasCancel: false,
          hasSave: false,
          hasCloseIcon: true
        }
      })
        .onOk((payload) => resolve(payload))
        .onCancel(() => {
          itemSearchRef?.value.selectText();
          isTableLoading.value = false;
          sales.clearFilters();
          return resolve(false);
        });
    });
  }

  return false;
};

/**
 * Busca o DAV com o controle fornecido.
 * @param {Number} controleDav O 'controle' do DAV a ser buscado.
 * @param {Object} filters Os filtros que serão utilizados.
 * @returns {Object|boolean} O objeto DAV encontrado ou false se não encontrado.
 */
const handleFetchDav = async (
  controleDav,
  codTipoDav = false,
  importarProdutoServicoOuApenasProduto,
  isOS = false
) => {
  isTableLoading.value = true;

  if (!noData.value) {
    isTableLoading.value = false;
    itemSearchRef.value.selectText();
    notify('Já existe um documento aberto');

    return;
  }

  const exportarItens =
    importarProdutoServicoOuApenasProduto === 'produtos' ? 2 : 1;
  let vendaWithOs;
  let response;

  if (isOS) {
    importOS.value = true;
    vendaWithOs = await api.post(`api/ordem-servico/${controleDav}/exportar`, {
      modulo: 1,
      itensParaExportacao: exportarItens
    });
    if (vendaWithOs) {
      response = await sales.get(vendaWithOs.data.controle, false, false, true);
    }
    if (response?.data?.rowsData?.valorPago !== '0.00') {
      disableDeleteItem.value = true;
    }
  } else {
    if (codTipoDav) {
      sales.table.filters = {
        'vendaWithDav.codTipoDav': {
          filterType: 'EQUALS',
          filterValue: String(codTipoDav)
        }
      };
    }

    response = await sales.get(controleDav, false, false, true);
  }

  if (response.success) {
    isTableLoading.value = false;

    const data = response.data?.rowsData;

    if (!data.osImportada) {
      if (
        codTipoDav &&
        (!data.vendaWithDav || data.vendaWithDav?.codTipoDav != codTipoDav)
      ) {
        notify('Não foi possível identificar a DAV');
        itemSearchRef?.value.selectText();
        reset();
        return false;
      }

      if (codTipoDav && (data?.vendaWithDav?.mesclada || data?.confirmada)) {
        notify('Não é possível importar DAV mesclada ou finalizada');
        if (codTipoDav) itemSearchRef?.value.selectText();
        reset();
        return false;
      }
    }
    if (!importarProdutoServicoOuApenasProduto) {
      importarProdutoServicoOuApenasProduto =
        await handleImportarProdutosServicos(data);
    }

    if (importarProdutoServicoOuApenasProduto === 'produtos') {
      data.vendaWithItem = data.vendaWithItem.filter(
        (item) =>
          item.vendaItemWithProduto?.produtoWithCaracteristica?.codTipoUso !=
          '10'
      );
    }

    const statusRejeitado = '3';

    apenasLiberarEdicaoDeDadosFiscais.value =
      data?.vendaWithDfe?.contingencia &&
      data?.vendaWithDfe?.status === statusRejeitado;

    return data;
  }

  notify('Não foi possível identificar a DAV');
  itemSearchRef?.value.selectText();
  isTableLoading.value = false;
  sales.clearFilters();
  return false;
};

const deleteItemPermission = (item) => {
  if (disableDeleteItem.value) return;
  if (
    pdvConfigData.value?.[0] &&
    pdvConfigData.value?.[0].exigirSupervisorExcluirItem
  ) {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: SupervisorPassword,
        scope: 'aprovacao-necessaria',
        title: 'Aprovação necessária',
        classesTopBox: 'tw-pr-12',
        hasCancel: false,
        hasSave: false
      }
    }).onOk(() => handleDeleteItem(item));
  } else {
    handleDeleteItem(item);
  }
};

/**
 * Remove o item da lista.
 * @param {Object} item Objeto do item.
 */
const handleDeleteItem = (item) => {
  const index = items.value.findIndex((el) => item === el);

  if (!item?.controle) items.value.splice(index, 1);
  else items.value[index].deletar = true;

  // Se a lista ficar vazia, redefine o produto recentemente adicionado como nulo
  if (items.value.length === 0) {
    recentlyAddedProduct.value = null;
  }

  // Limpa a seleção na tabela de produtos do PDV
  pdvItemsTableRef.value.clearSelection();
  focusOnSelect();
  notify('Item removido com sucesso', 'positive');
};

/**
 * Manipula a obtenção de informações do produto e adiciona-o à venda atual com base no preço total e informações de controle.
 * @param {Object} options - O objeto de opções.
 * @param {number} options.precoTotal - O preço total do produto.
 * @param {string} options.controle - As informações de controle.
 */
const handleFetchWithMoney = async ({ precoTotal, controle }) => {
  const product = await handleFetchItem(controle);

  // Se o produto for encontrado, calcula a quantidade com base no preço total e adiciona o produto
  if (product) {
    if (verifyStock(product)) {
      precoTotal = toFloat(precoTotal);
      product.produtoWithEstoque.qtde = round(
        Number(precoTotal) / Number(product.produtoWithEstoque?.precoVenda),
        4
      );
      handleAddItem(product);
    }
  } else {
    itemSearchRef?.value.selectText();
  }
};

/**
 * Manipula a obtenção de informações do produto e adiciona-o à venda atual com base na quantidade fornecida e nas informações de controle.
 * @param {Object} options - O objeto de opções.
 * @param {string} options.quantity - A quantidade do produto.
 * @param {string} options.controle - As informações de controle.
 * @returns {Promise<void>} - Uma Promessa que resolve assim que o produto for obtido e adicionado.
 */
const handleFetchWithQuantity = async ({ quantity, controle }) => {
  const item = await handleFetchItem(controle);

  // Se o produto for encontrado, define a quantidade (convertendo vírgula para ponto, se necessário) e adiciona o produto à venda
  if (item) {
    if (verifyStock(item)) {
      const qtde = Number(quantity.replace(',', '.'));
      item.qtdeEstoque = item.produtoWithEstoque.qtde;
      item.produtoWithEstoque.qtde = qtde;
      handleAddItem(item);
    }
  } else {
    itemSearchRef?.value.selectText();
  }
};

/**
 * Manipula a obtenção de informações do produto com base nas informações de controle (controle ou código de barras) e adiciona-o à venda atual.
 * @param {string} controle - As informações de controle ou código de barras do produto.
 * @returns {Promise<void>} - Uma Promessa que resolve assim que o produto for obtido e adicionado.
 */
const handleFetchEanControle = async (controle) => {
  // Obtém as informações do produto com base no controle (ou código de barras)
  const item = await handleFetchItem(controle);

  // Se o produto for encontrado, define a quantidade como 1 e adiciona o produto à venda
  if (item) {
    if (verifyStock(item)) {
      item.qtdeEstoque = item.produtoWithEstoque?.qtde ?? 1;
      item.produtoWithEstoque.qtde = 1;
      handleAddItem(item);
    }
  } else {
    itemSearchRef?.value.selectText();
  }
};

/**
 * Obtém informações do produto da API com base nas informações de controle fornecidas (controle ou código de barras) e filtros opcionais.
 * @param {string} controle - As informações de controle ou código de barras do produto.
 * @param {Object} [filters={}] - Filtros opcionais para especificar critérios de pesquisa.
 * @returns {Promise<Object|boolean>} - Uma Promessa que resolve com os dados do produto se encontrado, ou false se não encontrado ou ocorrer um erro.
 */
const handleFetchItem = async (searchString) => {
  const buscaPadrao = configPdv.value.buscaPadrao;

  const BUSCA_GTIN_OU_CONTROLE = buscaPadrao == '1';
  const BUSCA_COD_PRODUTO = buscaPadrao == '2';
  const BUSCA_COD_BARRAS_GTIN = buscaPadrao == '3';
  const BUSCA_COD_BARRAS_INTERNO = buscaPadrao == '4';
  const BUSCA_REFERENCIA = buscaPadrao == '5';
  const BUSCA_GTIN_OU_CONTROLE_OU_CODBARRASINTERNO = buscaPadrao == '6';
  const BUSCA_COD_PRODUTO_OU_CODBARRASINTERNO = buscaPadrao == '7';

  isTableLoading.value = true;
  let searchGrade = true;
  let filtersV2 = [
    {
      operator: 'OR',
      field: 'controle',
      filterType: 'EQUALS',
      filterValue: searchString
    }
  ];

  const setFilters = (field, value) => {
    filtersV2 = [
      ...filtersV2,
      {
        operator: 'OR',
        field: field,
        filterType: 'EQUALS',
        filterValue: value
      }
    ];
  };

  if (BUSCA_GTIN_OU_CONTROLE_OU_CODBARRASINTERNO) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
    setFilters('codBarras', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_PRODUTO_OU_CODBARRASINTERNO) {
    filtersV2 = [];
    setFilters('codBarras', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_GTIN_OU_CONTROLE) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_PRODUTO) {
    filtersV2 = [];
    searchGrade = false;
    setFilters('controle', searchString);
  }

  if (BUSCA_COD_BARRAS_GTIN) {
    filtersV2 = [];
    setFilters('codBarrasGtin', searchString);
  }

  if (BUSCA_COD_BARRAS_INTERNO) {
    filtersV2 = [];
    setFilters('codBarras', searchString);
  }

  if (BUSCA_REFERENCIA) {
    filtersV2 = [];
    searchGrade = false;
    const referenciaFormatada = searchString.trim().toUpperCase();
    setFilters('referencia', referenciaFormatada);
  }

  try {
    const route = '/api/produto';
    const response = await api.get(route, {
      params: { filtersV2 }
    });

    if (!response.data.length) {
      if (searchString.length >= 6 && searchGrade) {
        const gradeResponse = await api.get('/api/produto/grade', {
          params: {
            filters: {
              codBarras: {
                filterType: 'EQUALS',
                filterValue: searchString
              }
            }
          }
        });

        if (gradeResponse.data && gradeResponse.data.length === 1) {
          const grade = gradeResponse.data[0];
          const item = {
            ...grade?.produto,
            grade: [grade]
          };
          delete item.grade?.[0].produto;
          return item;
        } else {
          notify('Não foi possível identificar o produto');
          return false;
        }
      } else {
        notify('Não foi possível identificar o produto');
        return false;
      }
    }

    const product = response.data[0];

    // Verifica se o produto está inativo e notifica
    if (product && product.deletedAt) {
      notify('Produto inativo');
      return false;
    }

    if (!product) {
      notify('Não foi possível identificar o produto');
      return false;
    }

    return product;
  } catch (error) {
    notify('Não foi possível identificar o produto');
    return false;
  } finally {
    isTableLoading.value = false;
  }
};

/**
 * Adiciona um produto à venda atual estruturando as informações do produto e adicionando-o à lista de itens da venda.
 * @param {Object} item - O objeto do produto a ser adicionado à venda.
 * @returns {void} - Esta função não retorna nada.
 */
const handleAddItem = async (item) => {
  // Verifica se há grade (adicionado pelo codBarras da grade)
  if (item.grade?.length === 1) {
    item.grade[0] = ajustaQuantidadeGrade(
      item.grade[0],
      item.produtoWithEstoque?.qtde
    );
    item.grade[0].codProdutoGrade = item.grade[0]?.controle;
    delete item.grade[0]?.controle;
  }

  // Estrutura as informações do produto
  let itemStructure = {
    controle: null,
    index: items.value.length,
    nome: item.nome,
    codVenda: formValues.venda.controle ?? null,
    codProduto: item.controle,

    // Estoque e valores
    unidade:
      item.produtoWithCaracteristica?.caracteristicaWithUnidadeMedida
        ?.descricao ?? null,
    qtde: item.produtoWithEstoque?.qtde,
    qtdeReal: item.produtoWithEstoque?.qtdeReal,
    qtdeEstoque: item.qtdeEstoque,
    valorUnitario: item.produtoWithEstoque?.precoVenda,
    valorDesconto: item.produtoWithEstoque?.desconto ?? 0,
    descontoVenda: 0,
    valorAcrescimo: item.produtoWithEstoque?.acrescimo ?? 0,
    tipoValorAcrescimo: item.produtoWithEstoque?.tipoValorAcrescimo ?? 0,
    tipoValorDesconto: item.produtoWithEstoque?.tipoValorDesconto ?? 0,
    valorBruto: round(
      Number(item.produtoWithEstoque?.qtde) *
        Number(item.produtoWithEstoque?.precoVenda),
      2
    ),
    // Impostos
    cfopEditValue: item.produtoWithFiscal?.produtoFiscalWithCfop ?? null,
    cstCsosnEditValue:
      item.produtoWithFiscal?.produtoFiscalWithCst ??
      item.produtoWithFiscal?.produtoFiscalWithCsosn ??
      null,
    originalCfop: item.produtoWithFiscal?.produtoFiscalWithCfop ?? null,
    originalCstCsosn:
      item.produtoWithFiscal?.produtoFiscalWithCst ??
      item.produtoWithFiscal?.produtoFiscalWithCsosn ??
      null,
    usaSerial: false,
    usaGrade: item.produtoWithCaracteristica?.usaGrade ?? false,
    usaLote: false,
    grade: item.grade ?? [],
    produtoWithGrade: item.produtoWithGrade,
    serial: [],
    lote: [],
    itemWithProduto: item,
    fiscal: {
      codBeneficio: item?.produtoWithFiscal?.codBeneficio || null,
      aliqIcmsDeson: Number(item?.produtoWithFiscal?.aliqIcmsDeson) || 0,
      valorIcmsDeson: null,
      motivoIcmsDeson: item?.produtoWithFiscal?.motivoIcmsDeson || '',
      infosAdicionais: item?.produtoWithFiscal?.infosAdicionais || ''
    }
  };

  if (!validateCstCsosnProduct(itemStructure?.cstCsosnEditValue?.codigo)) {
    itemStructure.cstCsosnEditValue = null;
    itemStructure.originalCstCsosn = null;
  }

  itemStructure.valorLiquido = calcValorLiquido(itemStructure);
  itemStructure.fiscal.valorIcmsDeson = calcValorIcmsDeson(itemStructure);

  // Seta o CFOP de configuração, caso não exista.
  const configCfop = configPdv.value.cfop;
  if (!itemStructure.cfopEditValue && configCfop) {
    itemStructure.cfopEditValue = configCfop;
    itemStructure.fiscal.codCfop = configCfop?.controle;
    if (!itemStructure.originalCfop) itemStructure.originalCfop = configCfop;
  }

  // Se já adiciona item com grade,
  // significa que buscou pelo codBarras da grade
  // então verifica se já existe a grade adicionada
  // para popular a tabela
  let shouldAddItem = true;
  if (itemStructure.grade.length > 0) {
    itemStructure = procuraGradesAdicionadas(itemStructure, true);
    addingItem.value = _.cloneDeep(itemStructure);
    handleConfirm('grade');
    shouldAddItem = false;
    itemSearchRef?.value.clearText();
  } else if (
    !itemStructure?.produtoWithGrade?.length &&
    itemStructure.usaGrade
  ) {
    // Verifica se produto usa grade e não possui grades disponiveis
    notify('Não há grades em estoque para este item');
    shouldAddItem = false;
  } else {
    shouldAddItem = await verifySerialGradeLote(itemStructure);
  }

  if (shouldAddItem) {
    // Define o produto recentemente adicionado e adiciona na venda.
    recentlyAddedProduct.value = itemStructure;
    productAddedAudio.pause();
    productAddedAudio.currentTime = 0;
    productAddedAudio.play();
    items.value.push(itemStructure);
  }
  itemSearchRef?.value.clearText();
};

const ajustaQuantidadeGrade = (grade, quantidadeEstoque) => {
  const estoqueBloqueado = bloquearVendaEstoqueNegativoOuZerado.value;
  const reservaEstoque = produtoReservaEstoque.value;
  const quantidadeGrade = Number(grade.quantidade || 0);
  const quantidadeRealGrade = Number(
    Number(grade.quantidadeReal) ||
      Number(grade.produtoGrade?.quantidadeReal) ||
      0
  );

  if (!quantidadeGrade && estoqueBloqueado) {
    notify('Venda bloqueada para estoque negativo ou zerado');
    itemSearchRef?.value.clearText();
    return;
  }

  if (
    estoqueBloqueado &&
    reservaEstoque &&
    (!quantidadeRealGrade || quantidadeRealGrade < 0)
  ) {
    notify('Venda bloqueada para estoque negativo ou zerado');
    itemSearchRef?.value.clearText();
    return;
  }

  if (estoqueBloqueado) {
    grade.quantidadeMaxima = reservaEstoque
      ? quantidadeRealGrade
      : quantidadeGrade;

    if (quantidadeEstoque > grade.quantidadeMaxima) {
      notify(
        `Não há quantidade suficiente disponível em estoque, quantidade ajustada para ${grade.quantidadeMaxima}`
      );
      grade.quantidade = grade.quantidadeMaxima;
    } else {
      grade.quantidade = quantidadeEstoque;
    }
  } else {
    grade.quantidade = quantidadeEstoque;
  }

  return grade;
};

const addingItem = ref();
const seriaisRemovidos = ref([]);
const gradesRemovidas = ref([]);
// const serialStore = useSerial();

/**
 * Verifica a existência de grade serial e lote para o produto adicionado
 * Chama função que abre os modais de serial e grade.
 * @param product
 * @returns {Promise<boolean>}
 */
const verifySerialGradeLote = async (product) => {
  let shouldAddItem = true;

  // if (product && product.usaSerial) {
  //   procuraSeriaisAdicionados(product);
  //   addingItem.value = _.cloneDeep(product);
  //   shouldAddItem = await verifyAvailableSerials(product);
  //   if (!shouldAddItem) return shouldAddItem;

  //   // Modal de serial
  //   shouldAddItem = false;
  //   await openSerialModal();
  // }

  // Verifica se já existe esse item com esta grade adicionado
  if (product && product.usaGrade) {
    procuraGradesAdicionadas(product);
    addingItem.value = _.cloneDeep(product);
    shouldAddItem = false;

    // Modal de grade
    await openGradeModal();
    itemSearchRef?.value.clearText();
  }

  itemSearchRef?.value?.clearText();
  return shouldAddItem;
};

// const procuraSeriaisAdicionados = (product) => {
//   const alreadyAddedSerials = items.value.filter(
//     (item) => item.codProduto == (product.controle ?? product.codProduto)
//   );
//   if (alreadyAddedSerials && alreadyAddedSerials.length > 0) {
//     product.serial =
//       alreadyAddedSerials.reduce((acc, item) => {
//         if (item.codProduto == (product.controle ?? product.codProduto)) {
//           return acc.concat(item.serial);
//         }
//       }, []) ?? [];
//   }
//   return product;
// };

const procuraGradesAdicionadas = (product, adding = false) => {
  const alreadyAddedGrades = items.value.filter(
    (item) => item.codProduto == (product.controle ?? product.codProduto)
  );
  if (alreadyAddedGrades.length) {
    // Itera sobre produtos já adicionados
    const mergedGrades = alreadyAddedGrades.reduce((acc, item) => {
      if (item.codProduto == (product.controle ?? product.codProduto)) {
        // Caso o produto já possua uma grade (buscaram por codigo de barras da grade)
        if (product.grade.length && item.grade.length && adding) {
          // Procura o index dessa grade nas já adicionadas
          const indexGradeExistente = item.grade.findIndex(
            (grade) =>
              grade.cor == product.grade[0]?.cor &&
              grade.tamanho == product.grade[0]?.tamanho
          );

          // Se não encontra, concatena as grades ou então ajusta as quantidades
          if (indexGradeExistente == -1) {
            return acc.concat(product.grade, item.grade);
          } else if (adding) {
            /*
            Se houver quantidade máxima (bloqueia estoque negativo true)
            verifica se a soma das quantidades ultrapassa o limite e ajusta,
            caso contrário, soma normalmente
            */
            const qtde = item.grade[indexGradeExistente].quantidade;
            if (
              product.grade[0]?.quantidadeMaxima &&
              qtde + product.grade[0]?.quantidade >
                product.grade[0]?.quantidadeMaxima
            ) {
              // A soma da adição + existente é superior à máxima
              item.grade[indexGradeExistente].quantidade +=
                product.grade[0]?.quantidadeMaxima - qtde;
            } else {
              item.grade[indexGradeExistente].quantidade += Number(
                product.grade[0]?.quantidade
              );
            }
          }
        }
        return acc.concat(item.grade);
      }
    }, []);
    product.grade = mergedGrades;
  }

  return product;
};

const handleConfirm = (type) => {
  const existingIndex = items.value.findIndex((item) => {
    return item.codProduto == addingItem.value.codProduto;
  });

  let qtde = 1;
  if (type === 'serial') {
    qtde = addingItem.value[type].length;
  }

  if (type === 'grade') {
    qtde = addingItem.value[type].reduce((acc, grade) => {
      acc += Number(grade.quantidade);
      return acc;
    }, 0);
  }

  if (existingIndex === -1) {
    addingItem.value.qtde = qtde;
    items.value.push({ ...addingItem.value });
    recentlyAddedProduct.value = { ...addingItem.value };
    itemSearchRef.value.clearText();
  } else {
    items.value[existingIndex][type] = [...addingItem.value[type]];
    items.value[existingIndex].qtde = qtde;
    itemSearchRef.value.clearText();
  }

  productAddedAudio.pause();
  productAddedAudio.currentTime = 0;
  productAddedAudio.play();
  addingItem.value = {};
};

// const verifyAvailableSerials = async (product) => {
//   serialStore.table.filters = {
//     codProduto: {
//       filterType: 'EQUALS',
//       filterValue: product.codProduto
//     },
//     disponivel: {
//       filterType: 'EQUALS',
//       filterValue: true
//     }
//   };
//   const response = await serialStore.get(null, false, false);
//   if (response.success && response.data?.rowsData.length === 0) {
//     notify(
//       `O produto "${product.itemWithProduto?.descricaoCodigo}" não possui seriais disponíveis para venda`
//     );
//     return false;
//   }
//   return true;
// };

// const openSerialModal = async () => {
//   // Modal de serial
//   await new Promise((resolve) => {
//     $q.dialog({
//       component: MiniModalLayout,
//       componentProps: {
//         componentRef: SerialVenda,
//         hasSave: false,
//         hasCancel: false,
//         scope: 'serialVenda',
//         dataModal: {
//           isPdv: true,
//           serialStore,
//           vendaItem: addingItem.value,
//           addCallback: (res) => {
//             addingItem.value.serial = [...addingItem.value.serial, res];
//           },
//           removeCallback: (index) => {
//             if (addingItem.value.serial[index].controle) {
//               addingItem.value.serial[index].deletar = true;
//               seriaisRemovidos.value.push(addingItem.value.serial[index]);
//             }
//             addingItem.value.serial.splice(index, 1);
//           }
//         }
//       }
//     })
//       .onOk(() => {
//         handleConfirm('serial');
//         resolve();
//       })
//       .onCancel(() => resolve());
//   });
// };

const openGradeModal = async () => {
  // Modal de grade
  await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: GradeVenda,
        hasSave: false,
        hasCancel: false,
        scope: 'gradeVenda',
        dataModal: {
          isPdv: true,
          vendaItem: addingItem.value,
          bloquearEstoqueNegativo: bloquearVendaEstoqueNegativoOuZerado.value,
          reservaEstoque: produtoReservaEstoque.value,
          addCallback: (res) => {
            addingItem.value.grade = [...addingItem.value.grade, res];
          },
          updateCallback: ({ payload, index }) => {
            addingItem.value.grade.splice(index, 1, payload);
          },
          removeCallback: (index) => {
            if (addingItem.value.grade[index].controle) {
              addingItem.value.grade[index].deletar = true;
              gradesRemovidas.value.push(addingItem.value.grade[index]);
            }
            addingItem.value.grade.splice(index, 1);
          }
        }
      }
    })
      .onOk(() => {
        handleConfirm('grade');
        resolve();
      })
      .onCancel(() => resolve());
  });
};

/**
 * Abre um diálogo de confirmação para deletar a venda atual (PDV). Se confirmado, redefine os dados da venda.
 * @returns {void} - Esta função não retorna nada.
 */
const handleDeletePdv = () => {
  // Abre um diálogo de confirmação
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      scope: 'cancelar-venda',
      title: 'Cancelar venda?',
      description: 'Essa ação não pode ser desfeita.',
      hasCloseIcon: false,
      classesTopBox: '!tw-mb-0',
      cancelClass: 'tw-bg-SGBRWhiten tw-text-white',
      cancelLabel: 'Não',
      confirmLabel: 'Sim',
      saveCustomClass: '!tw-bg-tablesSGBR-lightRed !tw-text-tablesSGBR-darkRed'
    }
  }).onOk(() => {
    reset();
  });
};

/**
 * Verifica se há estoque para um determinado produto.
 * @param {string | number} qtde
 */
const verifyStock = (product) => {
  if (bloquearVendaEstoqueNegativoOuZerado.value) {
    let hasStock = Number(product.produtoWithEstoque?.qtde) > 0;
    if (produtoReservaEstoque.value) {
      hasStock = Number(product.produtoWithEstoque?.qtdeReal) > 0;
    }
    if (!hasStock) {
      notify('Venda bloqueada para estoque negativo ou zerado');
      itemSearchRef.value.selectText();
      return false;
    }
  }
  return true;
};

const newSale = ref();

/**
 * Salva o PDV como uma venda ou edita um DAV existente, confirmado ou não.
 * @param {Boolean} confirmed Sinalizador para salvar a venda como confirmada ou não.
 */
async function save(confirmed = false, emitirNfce = false) {
  let payload = toRaw(formValues);

  const { venda } = formValues;

  // Verifica se há alterações na importação, seja PDV ou DAV.
  const isEditing =
    Boolean(venda?.controle) && !_.isEqual(initialValues, payload);

  payload.venda.tipoEmissao = emitirNfce ? 1 : 2;

  // Realiza todas as validações antes de salvar ou editar a venda.
  const isValid = await validatePdv(payload, emitirNfce);
  if (!isValid) {
    return false;
  }

  // Se não há cliente informado, seta o padrão
  payload.venda.codCliente = payload.venda.codCliente || '1';
  payload.venda.valorProdutos = round(
    valorTotalProdutoServico.value.productTotal,
    2
  );
  payload.venda.valorServicos = round(
    valorTotalProdutoServico.value.serviceTotal,
    2
  );

  // Se não há natureza de operação informada, seta o padrão ('2 - Venda consumidor final')
  payload.venda.codNaturezaOperacao =
    configPdv.value.codNaturezaOperacao || '2';

  // Verifica se um PDV foi importado
  const isPdvImported = Boolean(venda?.controle) && venda?.modulo == 1;

  // Importou venda, não foi editado e nem está finalizando, não faz nada.
  if (venda?.controle && !isEditing && !confirmed) {
    const message =
      isDavImportada.value && produtoReservaEstoque.value
        ? ', itens não reservarão estoque'
        : '';
    notify(`Sem alterações${message}`, 'positive');
    reset();
    return true;
  }

  // Loader
  isSaving.value = true;
  newSale.value = null;

  // Se um PDV foi importado, edita os registros
  if (isPdvImported && !isDavImportada.value) {
    payload.item.forEach((item) => {
      if (item.grade) item.grade = [...gradesRemovidas.value, ...item.grade];
      if (item.serial)
        item.serial = [...seriaisRemovidos.value, ...item.serial];
      item.grade = returnObjOrNull(item.grade);
      item.serial = returnObjOrNull(item.serial);
      item.lote = returnObjOrNull(item.lote);
      delete item.itemWithProduto;
      delete item.originalCfop;
      delete item.originalCstCsosn;
      delete item.cfopEditValue;
      delete item.cstCsosnEditValue;
    });

    delete payload.dav;

    newSale.value = await editPdv(payload);
  } else {
    payload.venda.modulo = 1;
    payload.item.forEach((item) => {
      if (item.grade) item.grade = [...gradesRemovidas.value, ...item.grade];
      if (item.serial)
        item.serial = [...seriaisRemovidos.value, ...item.serial];
      item.grade = returnObjOrNull(item.grade);
      item.serial = returnObjOrNull(item.serial);
      item.lote = returnObjOrNull(item.lote);
      delete item.itemWithProduto;
      delete item.originalCfop;
      delete item.originalCstCsosn;
      delete item.cfopEditValue;
      delete item.cstCsosnEditValue;
    });
    newSale.value = await savePdv(payload);
  }
  isSaving.value = false;

  // Executa o fluxo de pagamento, caso confirmada.
  if (confirmed) {
    if (emitirNfce && newSale.value?.venda?.controle) {
      await calcularImposto.get(
        newSale.value?.venda?.controle,
        false,
        false,
        false,
        {
          modelo: '65'
        }
      );
    }
    if (newSale.value) {
      await handlePaymentFlow(newSale.value, emitirNfce);
    }
  } else {
    if (newSale.value) {
      reset();
    }
  }

  return true;
}

const validateCstCsosnProduct = (codCstCsosn) => {
  const crt = global?.company?.empresaWithEmpresaFiscal?.crt;

  if (crt == 1 || crt == 5) {
    if (!codCstCsosn || !isCstCsosn(codCstCsosn, 'csosn')) {
      return false;
    }
  } else {
    if (!codCstCsosn || !isCstCsosn(codCstCsosn, 'cst')) {
      return false;
    }
  }

  return true;
};

/**
 * Valida se o PDV pode ser salvo nas seguintes condições:
 *
 * Items não zerados ou negativos em estoque.
 * Exige vendedor e não foi preenchido.
 * @param {object} venda
 */
const validatePdv = async (payload, emitirNfce) => {
  if (emitirNfce) {
    const itemsMissingTaxes = payload.item.filter(
      (item) => !item.cfopEditValue || !item.cstCsosnEditValue
    );

    if (itemsMissingTaxes.length > 0) {
      if (itemsMissingTaxes.length > 12) {
        notify(
          'Existem muitos produtos com informações de CFOP ou CST/CSOSN ausentes. Verifique antes de finalizar a venda.'
        );
      } else {
        for (let i = 0; i < itemsMissingTaxes.length; i++) {
          notify(
            `Antes de finalizar a venda, verifique os impostos do produto: ${
              itemsMissingTaxes[i]?.itemWithProduto?.descricaoCodigo ||
              itemsMissingTaxes[i]?.codProduto
            }`
          );
        }
      }

      return false;
    }
  }

  const allValuesFilled = payload.item.every(
    (item) => item.valorLiquido > 0 && item.valorUnitario > 0
  );
  if (!allValuesFilled) {
    notify(
      'Item com valor líquido e/ou unitário zerado, ajuste antes de finalizar'
    );
    return false;
  }

  // Verifica CST / CSOSN
  if (emitirNfce) {
    const cstCsosnValidos = [
      '00',
      '20',
      '40',
      '41',
      '60',
      '102',
      '103',
      '300',
      '400',
      '500',
      '900',
      '02',
      '15',
      '53',
      '61'
    ];

    const itensIrregulares = [];
    const itensCstCsosnInvalido = [];

    payload.item.forEach((item) => {
      const itemCfop = item.cfopEditValue?.codCfop;
      const itemCstCsosn = item.cstCsosnEditValue?.codigo;

      const itemIrregular =
        (itemCstCsosn == '500' || itemCstCsosn == '60') && itemCfop != '5405';
      const cstCsosnInvalidoParaNfce = !cstCsosnValidos.includes(itemCstCsosn);

      if (itemIrregular) {
        itensIrregulares.push(item);
      }

      if (cstCsosnInvalidoParaNfce) {
        itensCstCsosnInvalido.push(item);
      }
    });

    if (itensCstCsosnInvalido.length > 0) {
      const result = await new Promise((resolve) => {
        $q.dialog({
          component: MiniModalLayout,
          componentProps: {
            scope: 'cstcsosn-invalido',
            componentRef: CstCsosnInvalidoNfceModal,
            title: 'CST/CSOSN Inválido',
            hasSave: false,
            hasCancel: false,
            dataModal: {
              items: itensCstCsosnInvalido
            }
          }
        }).onDismiss(() => {
          resolve(false);
        });
      });

      if (!result) {
        return false;
      }
    }

    if (itensIrregulares.length > 0) {
      const result = await new Promise((resolve) => {
        $q.dialog({
          component: MiniModalLayout,
          componentProps: {
            scope: 'imposto-irregular',
            componentRef: IrregularCfop,
            title: 'Impostos irregulares',
            cancelLabel: 'Editar',
            dataModal: {
              items: itensIrregulares
            }
          }
        })
          .onOk(() => {
            resolve(true);
          })
          .onCancel(() => {
            resolve(false);
          });
      });

      if (!result) {
        return false;
      }
    }
  }

  // Verificar estoque dos itens antes de enviar para o back.
  if (bloquearVendaEstoqueNegativoOuZerado.value) {
    // Realiza o somatório de cada produto da tabela e verifica se tem estoque suficiente.
    const groupedItems = payload.item.reduce((acc, item) => {
      const codProduto = item.codProduto;
      const qtdeEstoque = produtoReservaEstoque.value
        ? Number(item.qtdeReal)
        : Number(item.qtdeEstoque);

      if (item.deletar) return {};

      if (!acc[codProduto]) {
        acc[codProduto] = { qtde: 0 };
      }

      const quantidadeItem = item.controle
        ? Number(item.qtde) - Number(item.qtdeInicial ?? 0)
        : Number(item.qtde);

      acc[codProduto].qtde += quantidadeItem;
      acc[codProduto].item = item;
      acc[codProduto].estoqueMsg =
        qtdeEstoque > 0
          ? ` possui apenas ${qtdeEstoque} ${item.unidade} em estoque`
          : 'não possui estoque';
      acc[codProduto].estoqueSuficiente = acc[codProduto].qtde <= qtdeEstoque;
      return acc;
    }, {});

    // Filtra somente os items que não tiverem estoque negativo.
    let hasStock = Object.values(groupedItems).filter(
      (item) => !item.estoqueSuficiente
    );

    // Notifica os itens com estoque negativo ou zerado
    if (hasStock.length > 0) {
      hasStock = hasStock.map((item) => item.item);
      const result = await new Promise((resolve) => {
        $q.dialog({
          component: MiniModalLayout,
          componentProps: {
            componentRef: NoStockItems,
            scope: 'sem-estoque',
            title: 'Sem estoque!',
            hasCancel: false,
            confirmLabel: 'Ok',
            dataModal: {
              items: hasStock,
              reservaEstoque: produtoReservaEstoque.value
            }
          }
        })
          .onOk(() => {
            resolve(false);
          })
          .onCancel(() => {
            resolve(false);
          });
      });

      if (!result) {
        return false;
      }
    }
  }

  // Verifica se o valor final da venda JÁ paga foi alterado.
  // e mostra modal para confirmação
  if (payload?.venda?.vendaPaga && mudancaValorFinal.value) {
    const openModal = new Promise(function (resolve) {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          scope: 'pagamento-ja-realizado',
          classesTopBox: '!tw-justify-start !tw-mb-2 md:!tw-w-[400px]',
          title: 'Pagamento já realizado',
          descriptionClass: 'tw-mt-2',
          description: `Alterações no valor da venda. Ao confirmar, o pagamento vigente será cancelado e um novo pagamento deverá ser realizado.`
        }
      })
        .onOk(() => {
          resolve(true);
        })
        .onCancel(() => {
          resolve(false);
        });
    });
    return await openModal;
  }

  return true;
};

/**
 * Executa o fluxo de salvamento confirmado.
 * @param {Object} newSale Novo objeto da venda ou DAV não alterada.
 * @param emitir
 */
const handlePaymentFlow = async (newSale, emitir) => {
  const { venda } = newSale;
  // Abre um modal para selecionar o método de pagamento
  $q.dialog({
    component: MiniModalLayout,
    componentProps: {
      componentRef: PaymentMethod,
      scope: 'payment-method',
      hasCancel: false,
      hasSave: false,
      dataModal: {
        totalProp: valorTotalNota.value,
        vendaProp: venda,
        emitir: emitir
      }
    }
  })
    .onOk(() => {
      importOS.value = false;
      disableDeleteItem.value = false;
      reset();
    })
    .onCancel(async () => {
      // Reimporta a venda caso o modal de pagamento seja cancelado
      if (newSale?.venda?.controle) {
        reset();
        const dav = await handleFetchDav(newSale?.venda?.controle);
        if (dav) await allowImportPdv(dav, true);
      }
    });
};

/**
 * Função que envia uma requisição PUT para editar o PDV atual.
 * @param {Object} payload Objeto de payload da requisição.
 */
const editPdv = async (payload) => {
  try {
    // Envia a requisição PUT para editar o PDV e os itens já existentes e notifica.
    const response = await sales.put(payload, payload.controle);
    if (response.success) {
      return response.data;
    }
    return false;
  } catch (error) {
    // Em caso de erro, imprime o erro no console e notifica o usuário
    console.error(error);
    notify('Não foi possível editar a venda.', 'negative');
  }
};

/**
 * Função que envia uma requisição POST para salvar o PDV atual.
 * @param {Object} payload Objeto de payload da requisição.
 */
const savePdv = async (payload) => {
  delete payload.dav;
  try {
    // Envia a requisição POST para salvar o PDV e notifica
    const response = await sales.post(payload, null);
    if (response.success) {
      return response.data;
    }
  } catch (error) {
    // Em caso de erro, notifica o usuário
    console.error(error);
    notify('Não foi possível salvar a venda', 'negative');
  }
};

const resetFormValues = () => {
  resetForm({
    values: {
      venda: {
        controle: null,
        confirmada: false,
        codCliente: '',
        codFuncionarioResponsavel: '',
        numOrcamento: '',
        numPedido: '',
        descricao: '',
        observacoes: '',
        frete: 0,
        seguro: 0,
        acrescimo: 0,
        desconto: 0,
        tipoValorFrete: 0,
        tipoValorSeguro: 0,
        tipoValorAcrescimo: 0,
        tipoValorDesconto: 0,
        valorLiquido: 0,
        valorBruto: 0,
        davImportada: null,
        modulo: null,
        vendaPaga: false,
        valorProdutos: 0,
        valorServicos: 0
      },
      item: [],
      dav: null
    }
  });
};

/**
 * Reseta o estado da venda atual, limpando os dados do formulário, redefinindo as flags e esvaziando as listas de itens.
 * @returns {void} - Esta função não retorna nada.
 */
const reset = () => {
  resetFormValues();

  isClientRegistered.value = false;
  items.value.splice(0, items.value.length);
  setFieldValue(
    'venda.codFuncionarioResponsavel',
    employeeValue.value?.controle
  );
  clientValue.value = '';
  isClientLoading.value = false;
  cnpjCpf.value = '';
  recentlyAddedProduct.value = '';
  davAlreadyImported.value = false;
  valorPago.value = 0;
  isDavImportada.value = false;

  valorDesconto.value = 0;
  tipoValorDesconto.value = 0;
  valorAcrescimo.value = 0;
  tipoValorAcrescimo.value = 0;
  valorFrete.value = 0;
  tipoValorFrete.value = 0;
  removed.value = [];
  mudancaValorFinal.value = false;

  focusOnSelect();
};

/**
 * Lida com uma venda rejeitada, decidindo se deve reemitir a NFC-e ou pagar novamente.
 * Verifica se os valores da venda e dos itens foram modificados em relação aos valores iniciais.
 * Se não houver alterações, reemite a NFC-e. Caso contrário, faz uma nova venda.
 * @returns {void} Esta função não retorna nenhum valor.
 */
const handleRejectedSale = () => {
  const acrescimoEqual =
    initialValues.venda.acrescimo === formValues.venda.acrescimo;
  const tipoAcrescimoEqual =
    initialValues.venda.tipoValorAcrescimo ===
    formValues.venda.tipoValorAcrescimo;
  const freteEqual = initialValues.venda.frete === formValues.venda.frete;
  const tipoFreteEqual =
    initialValues.venda.tipoValorFrete === formValues.venda.tipoValorFrete;
  const descontoEqual =
    initialValues.venda.desconto === formValues.venda.desconto;
  const tipoDescontoEqual =
    initialValues.venda.tipoValorDesconto ===
    formValues.venda.tipoValorDesconto;
  const valorBruto =
    initialValues.venda.valorBruto === formValues.venda.valorBruto;
  const valorLiquido =
    initialValues.venda.valorLiquido === formValues.venda.valorLiquido;
  const itemsEqual = initialValues.item.length === formValues.item.length;

  const isPaid = formValues.venda?.vendaPaga;

  if (
    acrescimoEqual &&
    tipoAcrescimoEqual &&
    freteEqual &&
    tipoFreteEqual &&
    descontoEqual &&
    tipoDescontoEqual &&
    valorBruto &&
    valorLiquido &&
    itemsEqual &&
    !isDavImportada.value &&
    isPaid
  ) {
    reemitirNfce();
  } else {
    mudancaValorFinal.value = true;
    save(true, true);
  }
};

/**
 * Reemite NFC-e (Nota Fiscal do Consumidor Eletrônica).
 * Esta função obtém os valores do formulário, envia um PUT para editar as mudanças,
 * emite a NFC-e e trata a resposta conforme necessário.
 * @async
 * @returns {Promise<void>} Retorna uma Promise vazia.
 */
const reemitirNfce = async () => {
  const payload = toRaw(formValues);
  loadingModal.value = true;

  // Edita os campos necessários do PDV que não alteram o valor final.
  delete payload.dav;
  delete payload.dfe;
  await editPdv(payload);

  // Emite e imprime a NFC-e.
  await issuePrintNfce(payload.venda.controle);
  reset();
  loadingModal.value = false;
};

const removed = ref([]);

/**
 * Detecta alterações na matriz de itens em comparação com os valores iniciais e envia requisições de exclusão e inserção para atualizá-la.
 * Os valores iniciais dos itens são definidos assim que a página é carregada ou um DAV é importado.
 * @returns {Promise<void>} - Uma Promise que resolve após a atualização dos itens do PDV.
 */
// const handlePdvItems = async () => {
//   const added = items.value.filter((item) => !item.controle);

//   // Se houver itens adicionados ou removidos
//   if (added.length > 0 || removed.value.length > 0) {
//     // Para cada item removido, envia uma requisição DELETE
//     for (const removido of removed.value) {
//       if (removido.codProduto) {
//         await sales.delete(removido.controle, 'item', true);
//       }
//     }

//     // Para cada item adicionado, se válido, envia uma requisição POST
//     for await (const adicionado of added) {
//       if (
//         adicionado.codProduto &&
//         adicionado.qtde > 0 &&
//         adicionado.valorDesconto >= 0
//       ) {
//         await sales.post(adicionado, 'item', true);
//       }
//     }
//   }
// };

// ------------------ Fullscreen ---------------
/**
 * Alterna para o modo de tela cheia.
 */
const switchToFullscreen = (ev) => {
  if (isFullscreen.value || ev.srcKey === 'leave' || ev === 'leave') {
    exitFullscreen();
    isFullscreen.value = false;
    return;
  }

  isFullscreen.value = true;
  enterFullscreen();
};

/**
 * Obtém os elementos necessários para entrar no modo de tela cheia.
 * @returns {Object} - Um objeto contendo os elementos necessários para o modo de tela cheia.
 */
const fetchElements = () => {
  const navBar = document.querySelector('.q-drawer-container');
  const pageCard = document.querySelector('.mainLayout');
  const pageContainer = pageCard.parentElement;
  const header = document.querySelector('.app-header');
  const gridContainer = gridContainerRef.value;

  return { navBar, pageCard, pageContainer, header, gridContainer };
};

/**
 * Entra no modo de tela cheia e atualiza o CSS para apresentar um layout melhor.
 */
const enterFullscreen = async () => {
  // Busca os elementos
  const { navBar, pageCard, pageContainer, header, gridContainer } =
    fetchElements();

  // Verifica a existência dos elementos na DOM.
  if (navBar && pageCard && header && gridContainer) {
    pageContainer.classList.remove('tw-mt-8');
    pageContainer.classList.remove('lg:tw-mx-12');
    pageContainer.classList.add('lg:tw-mx-6');
    pageContainer.classList.add('tw-mt-6');
    pageContainer.classList.add('!tw-pb-4');
    navBar.style.display = 'none';
    header.style.pointerEvents = 'none';
    header.style.opacity = 0.6;
    pageCard.classList.remove('tw-max-w-screen-2xl');
    pageCard.classList.remove('tw-mx-auto');
    pageCard.classList.add('tw-max-w-screen-8xl');
    pageCard.parentElement.classList.add('!tw-pl-0');
  }

  focusOnSelect();
};

/**
 * Sai do modo de tela cheia e restaura o CSS de volta ao normal.
 */
const exitFullscreen = () => {
  const { navBar, pageCard, pageContainer, header, gridContainer } =
    fetchElements();

  if (document.fullscreenElement) {
    document?.exitFullscreen();
  }

  if (navBar && pageCard && header && gridContainer) {
    pageContainer.classList.add('tw-mt-8');
    pageContainer.classList.add('lg:tw-mx-12');
    pageContainer.classList.remove('lg:tw-mx-6');
    pageContainer.classList.remove('tw-mt-6');
    pageContainer.classList.remove('!tw-pb-4');
    navBar.style.display = 'block';
    header.style.pointerEvents = 'auto';
    header.style.opacity = 1;
    pageCard.classList.remove('tw-max-w-screen-8xl');
    pageCard.classList.remove('tw-mx-auto');
    pageCard.classList.add('tw-max-w-screen-2xl');
    pageCard.parentElement.classList.remove('!tw-pl-0');
  }
  focusOnSelect();
};

/**
 * Abre o diálogo de seleção de produtos.
 * Isso permite ao usuário selecionar produtos de uma lista.
 * @returns {void} - Esta função não retorna nada.
 */
const openItemSelection = () => {
  if (document.querySelectorAll('.sgbrDialogs').length > 0) return false;

  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: ProductsList,
      scope: 'selection-products'
    }
  })
    .onOk(async (importedItems) => {
      isTableLoading.value = true;

      // Itera sobre os produtos importados e os adiciona à lista de itens da venda
      for (const item of importedItems) {
        item.qtdeEstoque = item.produtoWithEstoque.qtde;
        item.produtoWithEstoque.qtde = 1;
        await handleAddItem(item);
      }

      isTableLoading.value = false;
      focusOnSelect();
    })
    .onCancel(() => {
      focusOnSelect();
    });
};

// ------------------ Atalhos -------------------

const hotkeyScope = 'pdv';
const atalhos = [
  {
    key: 'f2',
    event: () => itemSearchRef.value.focus()
  },
  {
    key: 'f4',
    event: () => handleImportarDocumento(),
    condition: computed(() => global.roles?.includes('VENDA.PDV:IMPORTAR'))
  },
  {
    key: 'f10',
    event: () => handleAbrirListagem(),
    condition: computed(
      () =>
        global.roles?.includes('VENDA.LISTAGEM-PDV:MENU') && !isFullscreen.value
    )
  },
  {
    key: 'f11',
    event: () => openNfcConfig(),
    condition: computed(
      () => noData.value && global.roles?.includes('VENDA.PDV:CONFIGURACAO')
    )
  },
  {
    key: 'f3',
    event: () => handleFechamentoCaixa(),
    condition: computed(
      () => noData.value && global.roles?.includes('VENDA.PDV:FECHAMENTO-CAIXA')
    )
  },
  {
    key: 'ctrl+.',
    event: () => (showPdvShortkeysModal.value = true)
  },
  {
    key: 'alt+f11',
    event: () => switchToFullscreen('enter')
  },
  {
    key: 'esc',
    event: () => switchToFullscreen('leave')
  },
  {
    key: 'ctrl+p',
    event: () => openItemSelection(),
    condition: computed(() => global.roles?.includes('VENDA.PDV:IMPORTAR'))
  },
  {
    key: 'f7',
    event: () => handleDeletePdv(),
    condition: computed(
      () => !noData.value && global.roles?.includes('VENDA.PDV:EXCLUIR')
    )
  },
  {
    key: 'alt+g',
    event: () => save(),
    condition: computed(
      () => allowSave.value && global.roles?.includes('VENDA.PDV:CADASTRAR')
    )
  },
  {
    key: 'f5',
    event: () => save(true),
    condition: computed(
      () =>
        allowSave.value &&
        !reemissao.value &&
        pdvConfigData.value?.[0]?.permitirFinalizarVendaSemEmissaoNfce &&
        global.roles?.includes('VENDA.PDV:CADASTRAR')
    )
  },
  {
    key: 'f9',
    event: () => {
      if (!reemissao.value) {
        save(true, true);
      } else {
        handleRejectedSale();
      }
    },
    condition: computed(
      () =>
        allowIssueNfce.value && global.roles?.includes('VENDA.PDV:CADASTRAR')
    )
  },
  {
    key: 'num_add,=',
    event: () => {
      priceOrPercentAcrescimoRef.value.focusInput();
    }
  },
  {
    key: 'num_subtract,-',
    event: () => {
      priceOrPercentDescontoRef.value.focusInput();
    }
  }
];

// Definir atalhos

useScopedHotkeys(atalhos, hotkeyScope);

// ------------------ Watchers ------------------

// Observa as mudanças na lista de itens e atualiza os valores do formulário correspondentes.
watch(items.value, (newItems) => {
  setFieldValue('item', newItems);
});

// Aguarda renderização da ref pra focar no select.
watch(
  () => itemSearchRef.value,
  (newValue) => {
    if (newValue) focusOnSelect();
  }
);

// Quando os dados são limpos na tela, as variáveis auxiliares também são resetadas.
watch(
  () => noData.value,
  (newValue, oldValue) => {
    if (newValue && !oldValue) {
      importOS.value = false;
      disableDeleteItem.value = false;
      reset();
    }
  }
);

// Modal de atalhos é fechado
watch(
  () => showPdvShortkeysModal.value,
  (newValue) => {
    if (!newValue) {
      focusOnSelect();
    }
  }
);

// Modal de importação de DAV é fechado
watch(
  () => isDavImportModalOpened.value,
  (newValue) => {
    if (!newValue) {
      focusOnSelect();
    }
  }
);

const isNavigating = ref(false);

// Abre modal de aviso caso o usuário tente sair da página
onBeforeRouteLeave(async () => {
  // Caso exista um elemento em foco, esse foco é retirado
  document.activeElement.blur();

  if (isNavigating.value) return true;
  isNavigating.value = true;

  // Conferir se é pra abrir o modal
  if (formValues.item.length > 0) {
    // Abrir o modal retornando uma promise
    const openModal = new Promise(function (resolve) {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          componentRef: BeforeRouteLeaveModal,
          dataModal: {
            allowSave: allowSave
          },
          hasCancel: false,
          hasCloseIcon: false,
          hasSave: false,
          scope: 'deseja-sair?',
          title: 'Deseja realmente sair?'
        }
      })
        .onOk(async (buttonClicked) => {
          if (buttonClicked === 'Save') {
            // Salvar e sair
            const response = await save();
            if (response) {
              resolve('leaveRoute');
            }
          } else if (buttonClicked === 'Delete') {
            // Deletar e sair
            resolve('leaveRoute');
          } else {
            // Fechar modal
            resolve('stayInRoute');
          }
        })
        .onCancel(() => {
          // Fechar modal
          resolve('stayInRoute');
        });
    });

    const modalPromise = await openModal;
    await sales.resetData();

    // Bloquear saida da página
    if (modalPromise === 'stayInRoute') {
      isNavigating.value = false;
      focusOnSelect();
      return false;
    }

    return true;
  }
});
</script>

<style scoped>
@import './../../css/custom-nfc.css';
</style>
