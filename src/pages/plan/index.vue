<template>
  <SGPage id="plans-page">
    <!----- INÍCIO CARD DO PLANO ----->
    <SGCard
      :title="hasPartner ? 'Plano atual' : 'Planos'"
      id="plans-card"
      title-class="tw-text-2xl"
      :has-circle="false"
    >
      <!----- INÍCIO CARD DO PLANO ----->
      <div
        class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-gap-14 tw-pb-10 sm:tw-items-start"
        :class="hasPartner ? 'tw-mt-8 tw-flex-row sm:tw-flex-row-reverse' : ''"
      >
        <!-- CASO ESTEJA CARREGANDO O PARCEIRO E TENHA PARCEIRO NA EMPRESA -->
        <template
          v-if="
            (loadingPage && hasPartner) ||
            !global.roles?.includes('EMPRESA.PLANO:VISUALIZAR')
          "
        >
          <q-skeleton
            animation="blink"
            type="rect"
            height="192px"
            width="296px"
            class="tw-mt-8 tw-rounded-3xl max-[360px]:!tw-w-[200px]"
          />

          <q-skeleton
            animation="blink"
            type="rect"
            height="580px"
            width="280px"
            class="tw-rounded-3xl max-[360px]:!tw-w-[200px]"
          />
        </template>
        <!-- FIM CASO ESTEJA CARREGANDO O PARCEIRO E TENHA PARCEIRO NA EMPRESA -->

        <!-- CASO NÃO ESTEJA CARREGANDO O PARCEIRO OU NÃO TIVER PARCEIRO -->
        <template
          v-if="
            !loadingPage && global.roles?.includes('EMPRESA.PLANO:VISUALIZAR')
          "
        >
          <!-- CASO CONTER PLANO -->
          <div
            v-if="hasPartner"
            class="tw-mt-8"
            v-authorized="'EMPRESA.PLANO:VISUALIZAR'"
          >
            <h3 class="tw-mb-2 tw-text-base">Dados do plano</h3>

            <!-- CASO CONTER CNPJ CPF DE PARCEIRO E FOR DIFERENTE DA EMPRESA -->
            <div
              class="tw-mb-2 tw-w-fit tw-rounded-md tw-border-2 tw-border-SGBRBlueLighten tw-p-2 tw-px-4 tw-shadow-md"
              v-if="partner.cnpjCpf && companyData.cnpjCpf != partner.cnpjCpf"
            >
              <h3 class="tw-mb-1 tw-text-sm">Parceiro</h3>

              <!-- PARTE NOME -->
              <p class="tw-font-bold tw-text-textsSGBR-gray">
                Nome:
                <span class="tw-font-normal">{{
                  partner?.cliente ?? 'NÃO POSSUI'
                }}</span>
              </p>
              <!-- FIM PARTE NOME -->

              <!-- PARTE CNPJ CPF -->
              <p class="tw-font-bold tw-text-textsSGBR-gray">
                {{ cnpj.isValid(partner?.cnpjCpf) ? 'CNPJ' : 'CPF' }}:
                <span class="tw-font-normal">{{
                  !partner.cnpjCpf
                    ? 'NÃO POSSUI'
                    : cnpj?.isValid(partner?.cnpjCpf)
                    ? cnpj?.format(partner?.cnpjCpf)
                    : cpf?.format(partner?.cnpjCpf)
                }}</span>
              </p>
              <!-- FIM PARTE CNPJ CPF -->

              <!-- PARTE ENDEREÇO -->
              <p class="tw-font-bold tw-text-textsSGBR-gray">
                Endereço:
                <span class="tw-font-normal">{{ addressArray }}</span>
              </p>
              <!-- FIM PARTE CASO ENDEREÇO -->
            </div>
            <!-- FIM CASO CONTER CNPJCPF DE PARCEIRO E FOR DIFERENTE DA EMPRESA -->

            <!-- PARTE PLANO ATUAL -->
            <p class="tw-font-bold tw-text-textsSGBR-gray">
              Plano atual:
              <span
                class="tw-font-normal tw-uppercase"
                :class="atualPlanColor"
                >{{ companyData.empresaWithPlano?.descricao }}</span
              >
            </p>
            <!-- FIM PARTE PLANO ATUAL -->

            <!-- PARTE STATUS -->
            <p class="tw-font-bold tw-text-textsSGBR-gray">
              Status:
              <span
                class="tw-font-normal tw-uppercase"
                :class="
                  planoConfirmado ? 'tw-text-SGBRGreen' : 'tw-text-SGBROrange'
                "
                >{{
                  planoConfirmado
                    ? 'Plano confirmado com parceiro'
                    : 'Aguardando retorno de parceiro'
                }}</span
              >
            </p>
            <!-- FIM PARTE STATUS -->

            <!-- PARTE DATA EXPIRAÇÃO -->
            <p
              v-if="companyData?.expiracao"
              class="tw-font-bold tw-text-textsSGBR-gray"
            >
              Data expiração:
              <span class="tw-font-normal tw-uppercase">{{
                companyData?.expiracao?.split('-').reverse().join('/')
              }}</span>
            </p>
            <!-- FIM PARTE DATA EXPIRAÇÃO -->

            <ModalHistoryPayment v-model="ModelHistoryModal" />

            <q-btn
              color="primary"
              unelevated
              v-authorized="'EMPRESA.PLANO:VISUALIZAR'"
              class="tw-mt-[20px] tw-px-10 tw-text-xs"
              @click="() => (ModelHistoryModal = true)"
              >Histórico</q-btn
            >
            <!-- CASO TENHA PAGAMENTO -->
            <div
              class="tw-mt-5"
              v-if="pagamentoDisponivel || pagamentoAdicionaisDisponivel"
            >
              <div>
                <!-- BOTÃO QUE ABRE O QR-CODE -->
                <q-btn
                  v-if="pagamentoDisponivel"
                  color="primary"
                  v-authorized="'EMPRESA.PLANO:VISUALIZAR'"
                  unelevated
                  :label="
                    !pagamentoAntecipadoDisponivel
                      ? 'Gerar qrcode de pagamento'
                      : 'Antecipar renovação do plano'
                  "
                  class="tw-mb-4 !tw-h-auto"
                  solid
                  @click="
                    () => {
                      renewQrcode();
                      qrcodeModel = true;
                    }
                  "
                  :disable="partnerPay"
                >
                  <TooltipCustom
                    text-tooltip="Para renovar seu plano, entre em contato com seu parceiro!"
                    v-if="partnerPay"
                    :custom-offset="[0, 44]"
                    class="!tw-max-w-[250px] tw-text-wrap tw-break-all"
                  />
                </q-btn>

                <p class="tw-text-SGBRRed" v-if="partnerPay">
                  Para renovar seu plano, entre em contato com seu parceiro!
                </p>

                <!-- FIM BOTÃO QUE ABRE O QR-CODE -->

                <!-- MODAL DE QR-CODE -->
                <q-dialog v-model="qrcodeModel" v-if="hasQrcode" persistent>
                  <q-card
                    style="width: 340px; border-radius: 5px"
                    class="tw-bg-SGBRBlueLighten"
                  >
                    <q-card-section class="tw-relative tw-grid tw-px-6 tw-py-3">
                      <div
                        class="tw-mb-1 tw-flex tw-flex-row tw-flex-wrap-reverse tw-items-center tw-justify-center tw-pb-1 tw-pt-3"
                      >
                        <h2
                          class="tw-text-center tw-font-bold tw-text-SGBRWhite"
                        >
                          Escaneie o QR Code
                        </h2>

                        <q-btn
                          icon="close"
                          v-close-popup="qrcodeModel"
                          no-caps
                          flat
                          class="tw-absolute tw-right-1 tw-top-0 tw-ml-auto tw-min-h-0 tw-p-0 tw-text-SGBRWhite"
                        />
                      </div>

                      <div class="tw-flex tw-flex-col tw-gap-2">
                        <div
                          class="tw-flex tw-flex-col tw-items-center tw-justify-center tw-rounded-[10px] tw-bg-SGBRWhite tw-px-5 tw-pb-3 tw-pt-7"
                        >
                          <p
                            class="tw-mb-4 tw-text-xl tw-font-black tw-text-black"
                          >
                            R$ {{ valorAPagar.replace('.', ',') }}
                          </p>
                          <QrcodeVue
                            v-if="hasQrcode && !isPaymentDone"
                            :value="pixCode"
                            :size="250"
                            level="H"
                          >
                          </QrcodeVue>

                          <!-- CASO SEJA PAGO O PIX -->
                          <div
                            v-if="isPaymentDone"
                            class="tw-flex tw-h-full tw-flex-col tw-items-center tw-justify-center tw-gap-3"
                          >
                            <q-icon
                              class="animate__animated animate__zoomIn"
                              name="img:/icons/greenConfirm.svg"
                              size="170px"
                              color="green"
                            />

                            <p
                              class="tw-mt-4 tw-flex tw-text-center tw-text-base tw-font-bold tw-text-SGBRGreenLighten"
                            >
                              Pagamento concluído
                            </p>
                          </div>

                          <!-- FIM CASO SEJA PAGO O PIX -->

                          <!-- CASO TENHA QRCODE E NÃO ESTEJA PAGO -->
                          <div v-if="hasQrcode && !isPaymentDone">
                            <p
                              class="tw-mt-4 tw-flex tw-text-center tw-text-base"
                            >
                              <q-spinner
                                color="primary"
                                size="1.2em"
                                class="tw-mt-0.5"
                                :thickness="10"
                              />
                              <span class="tw-ml-4 tw-text-base"
                                >Aguardando pagamento</span
                              >
                            </p>

                            <!-- PARTE DE COPIAR CÓDIGO PIX, INPUT E BOTÃO COPIÁVEL -->
                            <div
                              class="tw-my-2 tw-flex tw-flex-row tw-flex-wrap tw-items-end tw-justify-between tw-gap-2"
                            >
                              <q-input
                                dense
                                outlined
                                :model-value="pixCode"
                                type="text"
                                class="tw-flex-grow"
                                label-slot
                                required
                                readonly
                                stack-label
                                ref="inputPixRef"
                                @click="() => inputPixRef.select()"
                              >
                                <TooltipCustom
                                  :text-tooltip="pixCode"
                                  :custom-offset="[0, 94]"
                                  class="!tw-max-w-[250px] tw-text-wrap tw-break-all"
                                />
                              </q-input>
                              <q-btn
                                flat
                                unelevated
                                dense
                                no-caps
                                class="!tw-h-[32px] tw-rounded-md tw-bg-SGBRBlueLighten tw-font-medium tw-text-SGBRWhite"
                                padding="0px 10px"
                                label="Copiar"
                                @click="() => copyPix(pixCode)"
                              />
                            </div>
                          </div>
                          <!-- FIM CASO TENHA QRCODE E NÃO ESTEJA PAGO -->
                        </div>

                        <q-btn
                          flat
                          unelevated
                          dense
                          no-caps
                          class="tw-mx-auto tw-w-[120px] tw-rounded-md tw-bg-SGBRGrayBG tw-font-medium tw-text-textsSGBR-gray"
                          padding="5px"
                          v-close-popup="qrcodeModel"
                          label="Cancelar"
                        />
                      </div>
                    </q-card-section>
                  </q-card>
                </q-dialog>
                <!-- FIM MODAL DE QR-CODE -->
              </div>
            </div>
            <!-- FIM CASO TENHA PAGAMENTO -->

            <!-- ADICIONAIS -->
            <div class="tw-mt-8 tw-flex tw-flex-col" v-if="canShowAdditionals">
              <div class="tw-flex tw-flex-row tw-gap-2">
                <h3
                  v-if="
                    (optionsAdditionals.filter(
                      (option) => option.statusLabel !== 'Disponível'
                    ).length > 0 &&
                      isPlanTeste) ||
                    planoPossuiAdicional
                  "
                  class="tw-mb-4 tw-text-xl tw-font-bold tw-text-SGBRBlueLighten"
                >
                  Adicionais
                </h3>

                <q-btn
                  v-if="
                    optionsAdditionals.filter(
                      (option) => option.statusLabel === 'Pagamento pendente'
                    ).length > 0
                  "
                  color="primary"
                  v-authorized="'EMPRESA.PLANO:VISUALIZAR'"
                  unelevated
                  label="Gerar pagamento de adicionais"
                  class="tw-mb-4 !tw-h-auto"
                  solid
                  @click="paymentAdditionals"
                >
                </q-btn>

                <q-btn
                  v-if="canShowAddAdditionalsBtn"
                  @click="entrarNaTelaSummary"
                  class="!tw-h-[25px] !tw-max-h-[25px] !tw-min-h-[25px] !tw-min-w-[25px]"
                  round
                  size="12px"
                  color="primary"
                  icon="add"
                />
              </div>

              <div
                class="tw-flex !tw-w-full tw-flex-wrap tw-justify-start tw-gap-8 tw-text-center"
                v-if="hasSomeAdditionalWithDifferentThanAvailable"
              >
                <template v-for="(option, keyOption) in optionsAdditionals">
                  <div
                    v-if="option?.statusLabel !== 'Disponível'"
                    class="tw-relative"
                    :key="keyOption"
                    :class="[
                      'tw-relative !tw-h-[190px] tw-max-w-[190px] tw-rounded-xl tw-border-2 tw-border-solid tw-px-2 tw-py-8',
                      option?.statusLabel === 'Contratado'
                        ? 'tw-border-SGBRBlueLighten'
                        : option?.statusLabel === 'Pendente'
                        ? 'tw-border-SGBROrange'
                        : 'tw-border-SGBRGray'
                    ]"
                  >
                    <!--FIM TAG-->

                    <div
                      class="tw-flex tw-h-full tw-w-full tw-flex-col tw-items-center tw-justify-center"
                    >
                      <!--TAG-->
                      <div
                        :class="
                          option?.statusLabel === 'Contratado'
                            ? 'tw-bg-blue-500'
                            : 'tw-flex tw-justify-center tw-bg-orange-500'
                        "
                        class="tw-center tw-absolute tw-top-[-6px] tw-z-[2] tw-w-max tw-transform tw-rounded-md tw-px-3 tw-py-0 tw-text-xs tw-text-white"
                      >
                        {{ option?.statusLabel }}
                      </div>

                      <h3
                        :class="[
                          '!tw-text-SGBRBlueLighten',
                          'tw-text-SGBRGray',
                          option.uppercaseTitle && '!tw-uppercase'
                        ]"
                        class="tw-mb-4 tw-font-medium tw-normal-case"
                      >
                        {{ option.label }}
                      </h3>
                      <div class="tw-h-full">
                        <ul
                          class="tw-flex tw-list-inside tw-list-disc tw-flex-col tw-gap-2 tw-px-2 tw-text-start"
                        >
                          <li
                            v-for="(info, keyInfo) in option.infos"
                            :key="keyInfo"
                            class="tw-w-fit tw-font-normal tw-normal-case"
                          >
                            {{ info }}
                          </li>
                        </ul>
                      </div>

                      <!-- BOTÃO REMOVER-->
                      <q-btn
                        dense
                        flat
                        class="tw-absolute tw-right-0 tw-top-0 tw-mt-2 tw-w-fit tw-cursor-pointer !tw-rounded-full tw-px-1"
                        v-if="option.statusLabel === 'Pendente' && !isPlanTeste"
                        @mouseover="openAdditionalCardMenuByState(keyOption)"
                        @mouseleave="closeAdditionalCardMenuByState(keyOption)"
                      >
                        <q-icon
                          fill="none"
                          color="primary"
                          size="1.2rem"
                          name="more_vert"
                          flat
                        >
                          <q-menu
                            :delay="300"
                            anchor="top start"
                            self="top start"
                            :offset="[-23, 10]"
                            v-model="option.menuState"
                            class="tw-flex tw-min-w-[50px] tw-max-w-[40rem] tw-items-center tw-justify-center !tw-uppercase !tw-shadow-[0_3px_10px_rgb(0,0,0,0.2)]"
                            @mouseover="
                              openAdditionalCardMenuByState(keyOption)
                            "
                            @mouseleave="
                              closeAdditionalCardMenuByState(keyOption)
                            "
                          >
                            <q-list>
                              <q-item
                                clickable
                                v-close-popup
                                @click="() => handleDeletarSolicitacao(option)"
                                class="tw-px-2"
                              >
                                <i
                                  class="q-icon text-negative tw-h-[15px] tw-w-4 tw-cursor-pointer tw-rounded-[4px] tw-bg-[#fd9999] tw-text-sm tw-text-SGBRGray"
                                  aria-hidden="true"
                                  role="presentation"
                                >
                                  <img src="/icons/trash-closed-red.svg" />
                                </i>
                                <p
                                  class="tw-pl-2 tw-text-center !tw-text-[12px] tw-normal-case tw-text-SGBRBlueDarken"
                                >
                                  Remover
                                </p>
                              </q-item>
                            </q-list>
                          </q-menu>
                        </q-icon>
                      </q-btn>
                    </div>
                  </div>
                </template>
              </div>
            </div>
            <!-- FIM ADICIONAIS -->
          </div>
          <!-- FIM CASO CONTER PLANO -->

          <!-- CASO NÃO CONTER PLANO -->
          <p class="tw-text-md tw-w-full" v-else>
            Imagine o tempo e esforço que você economizará ao automatizar
            tarefas tediosas com nosso sistema de gestão web. Não perca seu
            tempo e renove sua assinatura agora mesmo!
          </p>
          <!-- FIM CASO NÃO CONTER PLANO -->

          <!-- CONTAINER DOS CARTÕES -->
          <div
            class="tw-flex tw-flex-grow tw-flex-col tw-items-center tw-justify-center tw-gap-8"
            :class="hasPartner ? 'tw-max-w-fit' : 'tw-w-full'"
          >
            <div
              class="tw-mb-10 tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center tw-gap-14 tw-gap-y-20 md:tw-flex-row"
              :class="hasPartner ? '!tw-mt-4 sm:tw-ml-4' : 'tw-mt-8'"
            >
              <!-- COMPONENTE DE CARTÃO -->
              <PlanCard
                v-for="plan in plans"
                :key="plan?.id"
                :choose="!hasPartner"
                :plan-obj="plan"
                :accept-status="planStatus"
              />
              <!-- FIM COMPONENTE DE CARTÃO -->
            </div>
          </div>
          <!-- FIM CONTAINER DOS CARTÕES -->
        </template>
        <!-- FIM CASO NÃO ESTEJA CARREGANDO O PARCEIRO OU NÃO TIVER PARCEIRO -->
      </div>
    </SGCard>
    <!----- FIM CARD DO PLANO ----->
  </SGPage>
</template>

<script setup>
import SGCard from 'components/generic/SGCard.vue';
import PlanCard from 'components/plan/Card.vue';
import { useGlobal } from 'stores/global';
import { storeToRefs } from 'pinia';
import { ref, watch, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'src/boot/axios';
import { cnpj, cpf } from 'cpf-cnpj-validator';
import SGPage from 'components/generic/SGPage.vue';
import QrcodeVue from 'qrcode.vue';
import { useCompanyData } from 'stores/api/company/index';
import 'animate.css';
import { Notify } from 'quasar';
import TooltipCustom from 'src/components/generic/tooltip/TooltipCustom.vue';
import handleApiErrors from 'src/services/handleApiErrors';
import ModalHistoryPayment from 'src/pages/plan/ModalHistoryPayment.vue';
import { fetchRolesData } from 'src/boot/router-guard.js';
import { getAdditionals } from 'src/pages/plan/utils/getAdditionals';
import { useSolicitacaoStore } from 'src/pages/plan/useSolicitacaoStore';
import { useAdicionalStore } from 'src/pages/plan/useAdicionalStore';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { Dialog } from 'quasar';

const $q = useQuasar();

const partner = ref({});

const router = useRouter();

const global = useGlobal();

const isPlanTeste = computed(() => global.company.codPlano == 1);

const isPlanMaster = computed(() => global.company.codPlano == 3);

const canShowAdditionals = computed(
  () =>
    isPlanMaster.value || global.company.empresaWithSolicitacao.codPlano == '3'
);

const solicitacaoStore = useSolicitacaoStore();
const adicionalStore = useAdicionalStore();

const {
  company: companyData,
  planoConfirmado,
  pagamentoAntecipadoDisponivel,
  pagamentoDisponivel
} = storeToRefs(global);

const pagamentoAdicionaisDisponivel = ref(false);

const loadingPage = ref(true);

const company = useCompanyData();
const { data: companyData2 } = storeToRefs(company);
company.get();

const ModelHistoryModal = ref(false);

// ADICIONAIS

const planoPossuiAdicional = computed(() => {
  return global.company.codPlano != 1 && global.company.codPlano != 2;
});

const optionsAdditionals = ref([]);

function entrarNaTelaSummary() {
  if (isPlanMaster.value) {
    global.selectedPlan = plans.value[0];

    router.push(`/plano/3/adicionais?atual`);
  }
}

const canShowAddAdditionalsBtn = computed(
  () => !hasSomeAdditionalWithDifferentThanAvailable.value && !isPlanTeste.value
);

const hasSomeAdditionalWithDifferentThanAvailable = computed(() =>
  optionsAdditionals.value.some(
    (additional) => additional.differentThanAvailable
  )
);

async function handleDeletarSolicitacao(option) {
  let description = '';
  const teste = option.solicitacao
    .map((item) => item.planoAdicional.descricao)
    .join(', ');
  if (option.solicitacao.length > 1) {
    description = `Ao prosseguir as solicitações referente aos adicionais ${teste} serão canceladas. Deseja continuar?`;
  } else {
    description = `Ao prosseguir a solicitação referente ao adicional ${teste} será cancelada. Deseja continuar?`;
  }
  const response = await new Promise((resolve) => {
    Dialog.create({
      component: MiniModalLayout,
      componentProps: {
        componentRef: '',
        scope: 'deletar-solicitacao',
        title: 'Deletar solicitação',
        description: description,
        classCardSection: 'lg:tw-w-[400px]',
        dataModal: {}
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });

  if (response) {
    await solicitacaoStore.delete({
      controle: option.solicitacao[0].codsolicitacao
    });

    optionsAdditionals.value = await getAdditionals(
      adicionalStore,
      solicitacaoStore,
      global
    );
  }
}

onMounted(async () => {
  optionsAdditionals.value = await getAdditionals(
    adicionalStore,
    solicitacaoStore,
    global
  );
});

function paymentAdditionals() {
  const option = optionsAdditionals.value.filter(
    (option) => option.statusLabel === 'Pagamento pendente'
  );

  const codsolicitacao = option?.[0]?.solicitacao?.[0]?.codsolicitacao;

  qrcodeModel.value = true;
  pagamentoAdicionaisDisponivel.value = true;

  renewQrcode(codsolicitacao);
}

function closeAdditionalCardMenuByState(keyOption) {
  if (optionsAdditionals.value?.[keyOption]) {
    optionsAdditionals.value[keyOption].menuState = false;
  }
}

function openAdditionalCardMenuByState(keyOption) {
  if (optionsAdditionals.value?.[keyOption]) {
    optionsAdditionals.value[keyOption].menuState = true;
  }
}

// FIM ADICIONAIS

const pixCode = ref('');
const hasQrcode = ref('');
const valorAPagar = ref('');
const qrcodeModel = ref(false);
const isPaymentDone = ref(false);
const inputPixRef = ref('');

/* FUNÇÕES DO PIX (QRCODE) */

/* FUNÇÃO QUE CUIDA DE MOSTRAR O CÓDIGO PIX */
const handleQrcode = async (newQrCodeData) => {
  /* FAZ UM GET PARA A API DE PAGAMENTO */
  const qrCodeData = await api.get(
    `/api/empresa/pagamento/${newQrCodeData.data?.controle}`
  );

  /* CASO TENHA CÓDIGO */
  if (newQrCodeData.data?.brCode) {
    hasQrcode.value = true;
    pixCode.value = qrCodeData.data?.brCode;
    valorAPagar.value = qrCodeData.data?.valor;
  }

  /* CASO STATUS QRCODE SEJA 1 E O MODAL ESTEJA ATIVO */
  if (qrCodeData.data.status == 1 && qrcodeModel.value) {
    // Se o status for 1 e o modelo de QR Code estiver definido, aguarda 7 segundos e chama novamente a função.
    setTimeout(() => {
      handleQrcode(qrCodeData);
    }, 7000);
  } else if (qrCodeData.data.status == 2) {
    /* CASO STATUS QRCODE SEJA 2 */
    await company.get();
    global.company = companyData2.value;
    isPaymentDone.value = true;
    pixCode.value = false;

    setTimeout(async () => {
      qrcodeModel.value = false;
      hasQrcode.value = false;
      pagamentoAdicionaisDisponivel.value = false;

      // Obtém os papéis permitidos da empresa e atualiza as variáveis globais correspondentes.
      const rolesData = await fetchRolesData(router);

      global.empresaExpirada = rolesData.empresaExpirada;
      global.tempoParaExpirar = rolesData.tempoParaExpirar;
      global.deveAlertarSobreExpiracao = rolesData.deveAlertarSobreExpiracao;
      global.pagamentoAntecipadoDisponivel =
        rolesData.pagamentoAntecipadoDisponivel;
      global.pagamentoDisponivel = rolesData.pagamentoDisponivel;
      global.planoConfirmado = rolesData.planoConfirmado;
      global.perfil = rolesData.usuarioAutenticado?.perfil?.descricao ?? '';
      global.roles = rolesData.clientRoles;
      global.status = true;

      // Verifica se os dados da empresa estão preenchidos e redireciona, se necessário.
      const firstStepsData = await api.get(
        `/api/primeiro-passo/${global.company.controle}`
      );

      /* CASO PRIMEIROS PASSOS DA EMPRESA EXISTA */
      if (firstStepsData.data.empresa) {
        global.isCompanyDataFilled = true;
      } else {
        router.push('/dados-empresa');
      }

      isPaymentDone.value = false;
    }, 2500);
  }
};

/* FUNÇÃO DE RECRIAR CÓDIGO PIX */
const renewQrcode = async (codsolicitacao) => {
  let route;
  if (codsolicitacao) {
    route = `/api/empresa/solicitacao/pagamento/${codsolicitacao}`;
  } else {
    route = `/api/empresa/${companyData.value.controle}/cobranca`;
  }
  try {
    const newQrCodeData = await api.post(route);
    await handleQrcode(newQrCodeData);
  } catch (error) {
    handleApiErrors(error);
  }
};

/* FUNÇÃO DE COPIA O CÓDIGO PIX */
const copyPix = async (link) => {
  inputPixRef.value.select();
  await navigator.clipboard.writeText(link);
  Notify.create({
    position: 'top',
    message: 'Código pix copiado',
    type: 'positive',
    timeout: 2500
  });
};

/* FIM FUNÇÕES DO PIX (QRCODE) */

/* FUNÇÃO QUE JUNTA O ENDEREÇO DO PARCEIRO E VERIFICA SE CONTÊM ALGUM */
const addressArray = computed(() => {
  let arrayInfo = [];
  const address = partner.value?.endereco;
  const neighborhood = partner.value?.bairro;
  const number = partner.value?.numero;

  /* CASO ENDEREÇO EXISTA */
  if (address !== '' && address !== null) {
    arrayInfo.push(address);
  }

  /* CASO BAIRRO EXISTA */
  if (neighborhood !== '' && neighborhood !== null) {
    arrayInfo.push(neighborhood);
  }

  /* CASO NÚMERO EXISTA */
  if (number !== '' && number !== null) {
    arrayInfo.push(number);
  }

  return arrayInfo[0] != null ? arrayInfo.join(', ') : 'NÃO POSSUI';
});

/* FUNÇÃO QUE CONFERE A COR PARA O TIPO DE PLANO */
const atualPlanColor = computed(() => {
  // Obtém a descrição do plano atual da empresa.
  switch (companyData.value.empresaWithPlano.descricao) {
    // Caso o plano seja PLUS, retorna a classe para a cor roxa.
    case 'PLUS':
      return 'tw-text-SGBRPurple';
    // Caso o plano seja MASTER, retorna a classe para a cor azul clara.
    case 'MASTER':
      return 'tw-text-SGBRBlueLighten';
    // Caso o plano seja BÁSICO, retorna a classe para a cor azul céu.
    case 'BÁSICO':
      return 'tw-text-SGBRBlueSky';
    // Se não for nenhum dos planos especificados, retorna a classe padrão para uma cor de link.
    default:
      return 'tw-text-navSGBR-blueSelectedLink';
  }
});

/* FUNÇÃO QUE CONFERE O STATUS DO PLANO */
const planStatus = computed(() => {
  let status = null;

  // Obtém o controle do parceiro.
  const planPartner = companyData.value.empresaWithParceiro?.[0]?.controle;
  // Obtém o código do plano da solicitação.
  const planSolicitation = companyData.value.empresaWithSolicitacao?.codPlano;

  // Verifica se há um plano da solicitação.
  if (planSolicitation != null) {
    // Verifica se o código do plano da empresa corresponde ao código do plano da solicitação.
    status = companyData.value.codPlano == planSolicitation;
  } else if (planPartner != null) {
    // Se não houver plano da solicitação, verifica se é um plano do parceiro.
    status = true;
  }
  return status;
});

const partnerPay = computed(
  () =>
    companyData.value.empresaWithEmpresaLicencaValor?.responsavelPagamento ==
    '2'
);

/* ARRAY DOS PLANOS */
const plans = ref([
  {
    customClasses: 'tw-bg-SGBRBlueLighten',
    planName: 'Master',
    controle: 3,
    id: 'master',
    finalUrlSendCustom: '/adicionais',
    recommended: true,
    customBtnClass: '!tw-bg-SGBRBlueSky',
    benefits: [
      { text: 'Dashboard', contains: true, info: '' },
      { text: 'Controle de estoque', contains: true, info: '' },
      {
        text: 'Controle de pessoas',
        contains: true,
        info: 'clientes, fornecedores, transportadoras e motoristas'
      },
      {
        text: 'Controle financeiro ',
        contains: true,
        info: 'contas a pagar, contas a receber, bancos e caixa'
      },
      {
        text: 'Emissão de documentos fiscais ',
        contains: true,
        info: 'NFC-e, NF-e e MDF-e'
      },
      {
        text: 'Documento auxiliar de venda ',
        contains: true,
        info: 'orçamento, pedido de venda e condicional '
      },
      { text: 'Compras ', contains: true, info: 'manual e MD-e' },
      { text: 'Devolução de vendas', contains: true, info: '' },
      { text: 'Etiquetas', contains: true, info: '' },
      { text: 'Convênios', contains: true, info: '' },
      { text: 'Documentos fiscais', contains: true, info: 'SPED e SINTEGRA' },
      { text: 'Pagamento integrado', contains: true, info: 'PIX e POS' },
      { text: 'Regras de tributação', contains: true, info: '' },
      { text: 'Controle de permissões e usuários', contains: true, info: '' },
      { text: 'Relatórios', contains: true, info: '' },
      { text: 'Cadastros em geral', contains: true, info: '' },
      { text: 'Suporte 24/7 ', contains: true, info: '' },
      { text: 'E muito mais!', contains: true, info: '' }
    ]
  },
  {
    customClasses: 'tw-bg-SGBRBlueSky',
    planName: 'Básico',
    controle: 2,
    id: 'basico',
    finalUrlSendCustom: '/notas-fiscais',
    recommended: false,
    customBtnClass: '!tw-bg-SGBRBlueLighten',
    benefits: [
      {
        text: 'Controle de pessoas',
        contains: true,
        info: 'fornecedores, clientes, transportadoras e motoristas '
      },
      {
        text: 'Emissão de documentos fiscais',
        contains: true,
        info: 'NFC-e ou NF-e'
      },

      { text: 'Pagamento integrado', contains: true, info: 'PIX e POS' },
      { text: 'Regras de tributação', contains: true, info: '' },
      { text: 'Cadastros em geral', contains: true, info: '' },
      { text: 'Suporte 24/7', contains: true, info: '' },
      { text: 'E muito mais!', contains: true, info: '' },
      { text: 'Documentos fiscais', contains: false, info: 'SPED e SINTEGRA' },
      {
        text: 'Controle financeiro',
        contains: false,
        info: 'contas a pagar, contas a receber, bancos e caixa'
      },
      {
        text: 'Documento auxiliar de venda ',
        contains: false,
        info: 'orçamento, pedido de venda e condicional '
      },
      {
        text: 'Dashboard',
        contains: false,
        info: ''
      },
      {
        text: 'Controle de estoque',
        contains: false,
        info: ''
      },
      { text: 'Compras ', contains: false, info: 'manual e MD-e' },
      { text: 'Devolução de vendas', contains: false, info: '' },
      { text: 'Etiquetas', contains: false, info: '' },
      { text: 'Convênios', contains: false, info: '' },
      { text: 'Controle de permissões e usuários', contains: false, info: '' },
      { text: 'Relatórios', contains: false, info: '' }
    ]
  }
]);
/* FIM ARRAY DOS PLANOS */

/**
 * Watcher que monitora as alterações nos dados da empresa.
 * Renova o QR Code e define o modelo de QR Code como verdadeiro se o tempo de expiração das credenciais globais for menor ou igual a zero e o código do plano da empresa for diferente de '1'.
 * Verifica e carrega as informações do parceiro se o CNPJ/CPF do parceiro estiver disponível nos dados da empresa.
 */
watch(
  () => companyData.value,
  () => {
    /* CASO O PLANO ESTIVER EXPIRADO E O PLANO FOR DIFERENTE DE TESTE (1) */
    if (global.empresaExpirada && companyData.value?.codPlano != '1') {
      /* ABRE O MODAL DE QRCODE PARA PAGAR */
      renewQrcode();
      qrcodeModel.value = true;
    }

    /* CASO TENHA PARCEIRO JUNTO A EMPRESA */
    if (companyData.value.empresaWithParceiro?.[0]?.cnpjCpf) {
      (async function checkParceiro() {
        // Exibe uma notificação de carregamento.
        const loadingNotify = $q.notify({
          message: 'Carregando...',
          position: 'top',
          color: 'gray',
          spinner: true,
          timeout: 0,
          group: false
        });
        try {
          /* BUSCA OS DADOS DO PARCEIRO NA API DE RESTRITO/PARCEIRO */
          const { data: response } = await api.get(
            '/api/restrito/parceiro/' +
              companyData.value.empresaWithParceiro?.[0]?.cnpjCpf
          );
          partner.value = response.data;

          loadingNotify();
        } catch (error) {
          // Em caso de erro, fecha a notificação de carregamento e exibe uma notificação de erro.
          loadingNotify();

          $q.notify({
            position: 'top',
            color: 'error',
            timeout: 2500,
            message: error.response.data.message,
            icon: 'error'
          });
        } finally {
          loadingPage.value = false;
        }
      })();
    } else {
      loadingPage.value = false;
    }
  },
  { immediate: true }
);

/* CONFERE SE POSSUI PARCEIRO OU SOLICITAÇÃO */
const hasPartner = computed(
  () =>
    (companyData.value.empresaWithParceiro?.[0]?.controle != null &&
      partner.value != null) ||
    companyData.value.empresaWithSolicitacao?.controle != null
);

/**
 * Observador que monitora as alterações na existência de um plano associado à empresa.
 * Atualiza a lista de planos se houver um plano associado.
 */
watch(
  () => hasPartner.value,
  () => {
    /* CASO POSSUI PARCEIRO OU SOLICITAÇÃO */
    if (hasPartner?.value == true) {
      /* PEGA O PLANO E RETORNA O CARTÃO A MOSTRAR NA TELA */

      let planValue;

      /* CASO O PLANO DA EMPRESA SEJA DIFERENTE DE TESTE (1) */
      if (companyData.value?.codPlano != 1) {
        planValue = companyData.value?.codPlano;
      } else if (
        /* CASO O PLANO DA SOLICITAÇÃO SEJA DIFERENTE DE NULO */
        companyData.value.empresaWithSolicitacao?.codPlano != null
      ) {
        planValue = companyData.value.empresaWithSolicitacao?.codPlano;
      }

      /* RETORNA O PLANO QUE É IGUAL AO ATUAL */
      const newPlan = [
        plans.value.find((el) => {
          return el.controle == planValue;
        })
      ];

      // Atualiza a lista de planos com o novo plano encontrado.
      plans.value = newPlan;

      /* FIM RETORNA O PLANO QUE É IGUAL AO ATUAL */
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.animate__animated.animate__fadeInUp {
  --animate-duration: 0.35s;
}
</style>
