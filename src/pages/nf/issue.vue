<template>
  <SGRegisterPage
    id="sessao-emissao-nf"
    :buttons="actionBarButtons"
    :disable-warning-animation="
      readonlyOrDisable ? true : isWarningAnimationDisabled
    "
    :modal="modal"
    :title="readonlyOrDisable ? 'Visualizar NF-e' : 'Emissão de NF-e'"
    desc="Nota fiscal eletrônica"
    :action-text="
      readonlyOrDisable ? 'Visualizando nota fiscal eletrônica' : null
    "
  >
    <MiniModal
      v-if="loadingModal"
      v-model="loadingModal"
      scope="loading"
      :has-cancel="false"
      :has-close-icon="false"
      :has-save="false"
    >
      <div
        class="tw-flex tw-w-full tw-flex-col tw-items-center tw-justify-center"
      >
        <q-spinner
          color="primary"
          size="6em"
          class="tw-mb-18 tw-m-24"
          :thickness="5"
        />
        <p class="tw-pb-4 tw-text-sm tw-text-inherit">
          Aguarde, reemitindo nota fiscal...
        </p>
      </div>
    </MiniModal>
    <!----- INICIO MODAL CONTRATO PIX  ----->
    <MiniModal
      v-if="isPixContractModalOpened"
      v-model="isPixContractModalOpened"
      scope="contrato-pix"
      :has-cancel="false"
      :has-close-icon="false"
      :has-save="false"
    >
      <PixContract
        dont-show-again-button
        @on-save="() => (isPixContractModalOpened = false)"
        @on-cancel="() => (isPixContractModalOpened = false)"
        scope="contrato-pix"
      />
    </MiniModal>
    <SGCard
      :has-circle="!readonlyOrDisable"
      title="Dados cadastrais"
      id="sessao-emissao-nf-dados-cadastrais"
      :key="formValues.venda.tipoVenda"
      :cols="8"
      remove-gap
      class="tw-relative"
    >
      <SelectInput
        ref="operationNatureRef"
        class="tw-col-span-4"
        label-select="Natureza de operação"
        :required="true"
        @value="handleOperatioNatureChange"
        :value="operationNatureEditValue"
        :store="natureOperation"
        :filters="operationNatureFilters"
        store-key="descricaoOperacao"
        :readonly="readonlyOrDisable"
      />

      <SelectDefault
        label="Tipo de venda"
        required
        v-bind="forms.venda.tipoVenda"
        @update:model-value="
          (value) => {
            // 1 - Operação presencial
            if (value == 1) {
              intermediadorEditValue = '';
              setFieldValue('venda.codIntermediador', '');
              nextTick(() => {
                peopleInputRef.resetValidation();
              });
            }
          }
        "
        :options="[
          {
            label: '1 - Operação presencial',
            value: '1'
          },
          {
            label: '2 - Operação não presencial, pela Internet',
            value: '2'
          },
          {
            label: '3 - Operação não presencial, Teleatendimento',
            value: '3'
          },
          // {
          //   label:
          //     '5 - Operação presencial, fora do estabelecimento (incluído NT2016.002)',
          //   value: '5'
          // },
          {
            label: '9 - Operação não presencial, outros',
            value: '9'
          }
        ]"
        :readonly="readonlyOrDisable"
      >
        <template #label
          >Tipo de venda <span class="require">*</span>
        </template>
      </SelectDefault>

      <SelectInput
        class="tw-col-span-4"
        label-select="Funcionário"
        ref="employeeInputRef"
        @value="
          (value) => {
            setFieldValue(
              'venda.codFuncionarioResponsavel',
              value?.controle || null
            );
            employeeEditValue = value;
          }
        "
        :value="employeeEditValue"
        :store="selectEmployeeStore"
        :filters="selectEmployeeFiltersV2"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'nome']"
        :order-by="{ controle: 'asc' }"
        button-add
        required
        @on-click-add="() => openRegisterEmployee()"
        role-prefix="FUNCIONARIO"
        :readonly="readonlyOrDisable"
      />

      <SelectDefault
        v-bind="forms.transporte.codFrete"
        class="tw-col-span-4"
        label="Frete por conta"
        required
        @update:model-value="
          (value) => {
            if (value == 9) {
              setFieldValue('transporte.placaVeiculo', '');
              setFieldValue('transporte.ufPlaca', '');
            }
          }
        "
        :options="[
          {
            label: '0 - REMETENTE',
            value: '0'
          },
          {
            label: '1 - DESTINATÁRIO',
            value: '1'
          },
          {
            label: '2 - TERCEIROS',
            value: '2'
          },
          {
            label: '3 - PRÓPRIO (REMETENTE)',
            value: '3'
          },
          {
            label: '4 - PRÓPRIO (DESTINATÁRIO)',
            value: '4'
          },
          {
            label: '9 - SEM FRETE',
            value: '9'
          }
        ]"
        :readonly="readonlyOrDisable"
      >
        <template #label
          >Frete por conta <span class="require">*</span>
        </template>
      </SelectDefault>

      <SelectInput
        :class="`tw-col-span-4 `"
        label-select="Intermediador"
        :store="people"
        :filters="intermediadorFilters"
        ref="peopleInputRef"
        editable
        :block-editable="['1']"
        @editable="(option) => openRegisterPeople(option?.controle)"
        button-add
        @on-click-add="() => openRegisterPeople()"
        role-prefix="PESSOA"
        @value="
          (value) => {
            setFieldValue('venda.codIntermediador', value?.controle || null);
            intermediadorEditValue = value;
          }
        "
        store-key="descricaoCodigo"
        :filter-or="['controle', 'razaosocial', 'cnpjcpf']"
        :value="intermediadorEditValue"
        :readonly="readonlyOrDisable"
      />

      <q-checkbox
        :model-value="consumidorFinalCheckbox"
        @update:model-value="
          (value) => {
            setFieldValue('venda.consumidorFinal', value);
            consumidorFinalCheckbox = value;
          }
        "
        label="Venda para consumidor final"
        class="tw-col-span-4 tw-mt-3 tw-w-fit"
        :disable="readonlyOrDisable"
        dense
      />
    </SGCard>

    <SGCard
      v-if="showNotasReferenciadas"
      id="nota-referenciada"
      title="Nota referenciada"
      :cols="12"
      has-title-separator
      :has-circle="false"
      remove-gap
      container-class="!tw-grid !tw-grid-cols-12"
    >
      <NotaReferenciada
        v-model="notaReferenciada"
        :is-devolucao="isDevolucao"
        :cliente="clientEditValue"
        @importar-itens="
          async ({ itens, cliente, dadosDaNota }) => {
            let isCompra;

            if (!clientEditValue) {
              await handleClientChange(cliente);
            }

            const possuiCstCsosnInvalido = itens.some((item) => {
              const tempItem = {
                fiscal: {}
              };

              tempItem.fiscal['codCstCsosn'] =
                item?.vendaItemWithFiscal?.codCstCsosn ||
                item?.vendaItemWithProduto?.produtoWithFiscal?.codCstCsosn ||
                item?.cstCsosn;

              if (!validateCstCsosnProduct(tempItem)) return true;

              return false;
            });

            if (possuiCstCsosnInvalido) {
              const crt =
                companyData.value?.empresaWithEmpresaFiscal.crt == 1 ||
                companyData.value?.empresaWithEmpresaFiscal.crt == 5
                  ? 'Cód. CST'
                  : 'Cód. CSOSN';

              notify(
                `Alguns dos produtos importados estavam com ${crt} inválido, confirme se os dados estão corretos.`
              );
            }

            for (const item of itens) {
              if (item.codCompra) {
                isCompra = true;
              }

              const comecarQuantidadeEValorUninatioComValorZero =
                naturezaOperacaoComplementar || naturezaOperacaoDeAjuste;

              const tempItem = {
                index: nfeItems?.length,
                itemData: { ...item },
                controle: null,
                codProduto: item.codProduto,
                xPed: item?.xPed,
                nItemPed: item?.nItemPed,
                codVenda: item.codVenda,
                qtde: comecarQuantidadeEValorUninatioComValorZero
                  ? 0
                  : Number(item.qtde), // Manipulável pelo usuário.
                qtdeEstoque: Number(
                  item.vendaItemWithProduto?.produtoWithEstoque?.qtde
                ), // Em estoque no cadastro de produtos.
                qtdeReal: Number(
                  item.vendaItemWithProduto?.produtoWithEstoque?.qtdeReal
                ), // Considerando reservas em vendas abertas.
                qtdeInicial: Number(item.qtde), // Inicial não manipulável.
                valorUnitario: comecarQuantidadeEValorUninatioComValorZero
                  ? 0
                  : parseFloat(item.valorUnitario),
                valorDesconto: parseFloat(item.valorDesconto),
                tipoValorDesconto: Number(item.tipoValorDesconto) || '0',
                valorAcrescimo: parseFloat(item.valorAcrescimo) || 0,
                tipoValorAcrescimo: Number(item.tipoValorAcrescimo) || '0',
                fiscal: isCompra
                  ? {
                      aliqIcms: item.percIcms,
                      percReducaoBcIcms: item.percRedIcms,
                      bcIcms: item.baseCalcIcms,
                      valorIcms: item.valorIcms,
                      aliqPis: item.percPis,
                      bcPis: item.baseCalcPis,
                      valorPis: item.valorPis,
                      aliqIcmsSt: item.percIcmsSt,
                      bcIcmsSt: item.baseCalcIcmsSt,
                      valorIcmsSt: item.valorIcmsSt,
                      aliqCofins: item.percCofins,
                      bcCofins: item.baseCalcCofins,
                      valorCofins: item.valorCofins,
                      aliqIpi: item.percIpi,
                      bcIpi: item.baseCalcIpi,
                      valorIpi: item.valorIpi,
                      // aliqFcp: 0;,
                      // bcFcp: 0;,
                      // valorFcp: 0;,
                      aliqFcpSt: item.aliquotaFcpSt,
                      bcFcpSt: item.valorBcFcpSt,
                      valorFcpSt: item.valorFcpSt
                    }
                  : item.vendaItemWithFiscal,
                selectData:
                  item?.vendaItemWithProduto || item?.compraItemWithProduto,
                usaGrade: item.usaGrade,
                grade: item?.vendaItemWithGrade,
                usaLote: false,
                usaSerial: false
              };

              if (!tempItem.fiscal) tempItem.fiscal = {};

              tempItem.fiscal['codCstCsosn'] =
                item?.vendaItemWithFiscal?.codCstCsosn ||
                item?.vendaItemWithProduto?.produtoWithFiscal?.codCstCsosn ||
                item?.cstCsosn;

              tempItem['cstCsosnEditValue'] =
                item?.compraItemWithCsosn ||
                item?.compraItemWithCst ||
                item?.vendaItemWithFiscal?.vendaItemFiscalWithCsosn ||
                item?.vendaItemWithFiscal?.vendaItemFiscalWithCst;

              tempItem.fiscal['codCfop'] =
                fetchOperationNatureCfop(tempItem)?.controle;
              tempItem.fiscal['cfop'] =
                fetchOperationNatureCfop(tempItem)?.codCfop;
              tempItem['cfopEditValue'] = fetchOperationNatureCfop(tempItem);

              if (validateCstCsosnProduct(tempItem)) {
                nfeItems.push(tempItem);
              } else {
                await productInputRefs.handleAddItem(tempItem.selectData);
              }
            }

            // itens.forEach(async (item) => {

            // });

            if (isCompra) {
              setFieldValue(
                'venda.desconto',
                parseFloat(formValues.venda.desconto) +
                  parseFloat(dadosDaNota.valorDesconto)
              );

              setFieldValue(
                'venda.acrescimo',
                parseFloat(formValues.venda.acrescimo) +
                  parseFloat(dadosDaNota.valorAcrescimo) +
                  parseFloat(dadosDaNota.valorFrete) +
                  parseFloat(dadosDaNota.valorSeguro)
              );
            } else {
              if (dadosDaNota.tipoValorDesconto == '1') {
                setFieldValue('venda.tipoValorDesconto', 0);
                setFieldValue(
                  'venda.desconto',
                  parseFloat(formValues.venda.desconto) +
                    parseFloat(dadosDaNota.valorBruto) *
                      (parseFloat(dadosDaNota.desconto) / 100)
                );
              } else {
                setFieldValue(
                  'venda.desconto',
                  parseFloat(formValues.venda.desconto) +
                    parseFloat(dadosDaNota.desconto)
                );
              }
              if (dadosDaNota.tipoValorAcrescimo == '1') {
                setFieldValue('venda.tipoValorAcrescimo', 0);
                setFieldValue(
                  'venda.acrescimo',
                  parseFloat(formValues.venda.acrescimo) +
                    parseFloat(dadosDaNota.valorBruto) *
                      (parseFloat(dadosDaNota.acrescimo) / 100)
                );
              } else {
                setFieldValue(
                  'venda.acrescimo',
                  parseFloat(formValues.venda.acrescimo) +
                    parseFloat(dadosDaNota.acrescimo)
                );
              }
            }

            // nfeItems = [...nfeItems, ...itensImportados];
          }
        "
        :natureza-operacao="operationNatureEditValue"
        :readonly-or-disable="readonlyOrDisable"
      />
    </SGCard>

    <SGCard
      :has-circle="!readonlyOrDisable"
      title="Transportadora"
      id="sessao-emissao-nf-transportadora"
      remove-gap
      :cols="12"
      :key="formValues.transporte.codFrete"
      v-if="['0', '1', '2', '3', '4'].includes(formValues.transporte.codFrete)"
    >
      <SelectInput
        class="tw-col-span-6"
        label-select="Transportadora"
        required
        :disable="
          forms.transporte.codFrete.modelValue == 9 ||
          forms.transporte.codFrete.modelValue?.value == 9
        "
        :filters="transporterFilters"
        :store="people"
        ref="peopleInputRef"
        button-add
        @on-click-add="() => openRegisterPeople()"
        role-prefix="PESSOA"
        @value="
          (value) => {
            setFieldValue(
              'transporte.codTransportadora',
              value?.controle || null
            );

            setFieldValue(
              'transporte.codigoAntt',
              value?.pessoaWithFiscal?.codigoAntt || null
            );

            transporterEditData = value;

            autoFillForm.transportadora.indIe =
              value?.pessoaWithFiscal?.indicadorIe?.descricao;
            autoFillForm.transportadora.logradouro =
              value?.pessoaWithEndereco?.endereco;
            autoFillForm.transportadora.estado = value?.pessoaWithEndereco?.uf;
            autoFillForm.transportadora.documento = value?.cnpjCpf;
            autoFillForm.transportadora.ie = value?.pessoaWithFiscal?.ie;
            autoFillForm.transportadora.codigoAntt =
              value?.pessoaWithFiscal?.codigoAntt;
          }
        "
        store-key="descricaoCodigo"
        :filter-or="['controle', 'razaosocial', 'cnpjcpf']"
        :value="transporterEditData"
        :readonly="readonlyOrDisable"
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.documento"
        label="Documento"
        stack-label
        bottom-slots
        class="tw-col-span-3"
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.ie"
        label="IE"
        class="tw-col-span-3"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.indIe"
        label="Ind. IE"
        class="tw-col-span-3"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.codigoAntt"
        label="Código ANTT"
        class="tw-col-span-3"
        @update:model-value="
          (value) => {
            setFieldValue('transporte.codigoAntt', value);
          }
        "
        maxlength="44"
        bottom-slots
        stack-label
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.logradouro"
        label="Logradouro"
        class="tw-col-span-3"
        maxlength="44"
        bottom-slots
        stack-label
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.transportadora.estado"
        label="Estado"
        class="tw-col-span-3"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        :disable="
          readonlyOrDisable ||
          forms.transporte.codFrete.modelValue == 9 ||
          forms.transporte.codFrete.modelValue?.value == 9
        "
        maxlength="20"
        stack-label
        v-bind="forms.transporte.placaVeiculo"
        label="Placa veículo"
        class="tw-col-span-3"
        bottom-slots
      >
      </q-input>

      <SelectDefault
        :disable="
          forms.transporte.codFrete.modelValue == 9 ||
          forms.transporte.codFrete.modelValue?.value == 9
        "
        v-bind="forms.transporte.ufPlaca"
        label-slot
        class="tw-col-span-3"
        stack-label
        label="UF placa"
        type="text"
        @keydown="(target) => preventNumber(target)"
        :options="type_uf"
        :readonly="readonlyOrDisable"
      />

      <Money
        stack-label
        :decimals-quantity="0"
        v-bind="forms.transporte.qtdeTransportada"
        label="Qtde de volumes"
        class="tw-col-span-3"
        :disabled="readonlyOrDisable"
      />
      <q-input
        stack-label
        v-bind="forms.transporte.especieVolume"
        label="Espécie"
        class="tw-col-span-3"
        maxlength="100"
        bottom-slots
        :readonly="readonlyOrDisable"
      />
      <q-input
        stack-label
        v-bind="forms.transporte.marcaVolume"
        label="Marca"
        class="tw-col-span-3"
        maxlength="100"
        bottom-slots
        :readonly="readonlyOrDisable"
      />
      <q-input
        stack-label
        v-bind="forms.transporte.numeroVolume"
        label="Numeração dos volumes"
        class="tw-col-span-3"
        maxlength="8"
        bottom-slots
        :readonly="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.transporte.pesoBruto"
        label="Peso bruto"
        class="tw-col-span-3"
        bottom-slots
        :decimals-quantity="3"
        :disabled="readonlyOrDisable"
      />

      <Money
        stack-label
        v-bind="forms.transporte.pesoLiquido"
        label="Peso liquído"
        :decimals-quantity="3"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
    </SGCard>

    <SGCard
      :has-circle="!readonlyOrDisable"
      title="Destinatário/remetente"
      id="sessao-emissao-nf-destinatario-remetente"
      remove-gap
      :cols="12"
    >
      <SelectInput
        :class="`tw-col-span-6 `"
        label-select="Destinatário/remetente"
        :relationships="['pessoaWithEndereco', 'pessoaWithFiscal']"
        :store="people"
        :required="true"
        ref="peopleInputRef"
        editable
        :block-editable="['1']"
        @editable="(option) => openRegisterPeople(option?.controle)"
        button-add
        @on-click-add="() => openRegisterPeople()"
        role-prefix="PESSOA"
        @value="handleClientChange"
        store-key="descricaoCodigo"
        :filter-or="['controle', 'razaosocial', 'cnpjcpf']"
        :value="clientEditValue"
        :readonly="readonlyOrDisable"
      />

      <!-- <SelectInput
        :class="`tw-col-span-4 ${!tipoData ? 'tw-opacity-50' : ''}`"
        :key="tipoData"
        :label-select="_.capitalize(tipoPessoaLabel)"
        :filters="getTipoPessoaFilter[tipoPessoaLabel]"
        :readonly="!tipoData"
        :store="people"
        :required="true"
        ref="peopleInputRef"
        button-add
        @on-click-add="() => openRegisterPeople()"
        role-prefix="PESSOA"
        @value="
          (value) => {
            setFieldValue('codPessoa', value?.controle || null);
            clientEditValue = value;

            autoFillForm.pessoa.indIe =
              value?.pessoaWithFiscal?.indicadorIe?.descricao;
            autoFillForm.pessoa.logradouro =
              value?.pessoaWithEndereco?.endereco;
            autoFillForm.pessoa.estado = value?.pessoaWithEndereco?.uf;
            autoFillForm.pessoa.complemento =
              value?.pessoaWithEndereco?.complemento;
            autoFillForm.pessoa.bairro = value?.pessoaWithEndereco?.bairro;
            autoFillForm.pessoa.cep = value?.pessoaWithEndereco?.cep;
            autoFillForm.pessoa.numero = value?.pessoaWithEndereco?.numero;
            autoFillForm.pessoa.ie = value?.pessoaWithFiscal?.ie;
            autoFillForm.pessoa.documento = value?.cnpjCpf;
          }
        "
        store-key="descricaoCodigo"
        :filter-or="['controle', 'razaosocial', 'cnpjcpf']"
        :value="clientEditValue"
      /> -->
      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.documento"
        label="Documento"
        stack-label
        bottom-slots
        class="tw-col-span-3"
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.estado"
        label="Estado"
        class="tw-col-span-3"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />

      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.logradouro"
        label="Logradouro"
        class="tw-col-span-8"
        maxlength="44"
        bottom-slots
        stack-label
        type="text"
        label-slot
        disable
      />

      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.numero"
        label="Número"
        class="tw-col-span-4"
        maxlength="44"
        bottom-slots
        stack-label
        type="text"
        label-slot
        disable
      />

      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.cidade"
        label="Cidade"
        class="tw-col-span-4"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />

      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.bairro"
        label="Bairro"
        class="tw-col-span-4"
        maxlength="44"
        bottom-slots
        stack-label
        type="text"
        label-slot
        disable
      />

      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.complemento"
        label="Complemento"
        class="tw-col-span-4"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.cep"
        label="CEP"
        aria-label="Código endereçamento postal"
        bottom-slots
        stack-label
        class="tw-col-span-4"
        maxlength="44"
        type="text"
        label-slot
        disable
        data-form-type="other"
        autocomplete="off"
        name="codigo"
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.ie"
        label="IE"
        class="tw-col-span-4"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
      <q-input
        dense
        outlined
        v-model="autoFillForm.pessoa.indIe"
        label="Ind. IE"
        class="tw-col-span-4"
        bottom-slots
        stack-label
        maxlength="44"
        type="text"
        label-slot
        disable
      />
    </SGCard>
    <SGCard
      :props-done="propsDoneDesconto && itemsPropsDoneTwo"
      container-class="tw-pb-4"
      remove-gap
      title="Itens da NF-e"
      :desc="
        isDevolucao
          ? 'não é possível alterar itens de uma devolução importada'
          : null
      "
      id="sessao-emissao-nf-itens-nf"
      :cols="12"
    >
      <NfItems
        class="tw-col-span-12"
        ref="productInputRefs"
        v-model="nfeItems"
        @update:model-value="
          (value) => {
            if (Object.values(value).length > 0) {
              setFieldValue('item', value);
            } else {
              setFieldValue('item', null);
            }
          }
        "
        :loading="false"
        :is-devolucao="isDevolucao"
        :operation-nature="operationNatureEditValue"
        :client="clientEditValue"
        :cfop-key="cfopKey"
        :readonly="readonlyOrDisable"
        :get-payload="getPayload"
        role-prefix="PRODUTO"
        :disable-delete-item="disableDeleteItem"
      >
      </NfItems>
      <div
        class="tw-col-span-full tw-mt-8 tw-flex tw-grid-cols-12 tw-flex-col tw-items-end tw-gap-4 lg:tw-grid"
        v-if="hasItems && values?.dav?.codTipoDav != 2"
      >
        <InputPriceOrPercent
          class="no-label tw-col-span-3"
          ref="freteInputRef"
          title-text="Frete"
          stack-label
          v-bind="forms.venda.frete"
          :toggle-type="forms.venda.tipoValorFrete.modelValue"
          @on-update-toggle="
            (toggleType) => {
              freteType = toggleType.label;
              setFieldValue('venda.tipoValorFrete', toggleType.value);
            }
          "
          :readonly="isDevolucao || readonlyOrDisable"
        />
        <InputPriceOrPercent
          class="no-label tw-col-span-3"
          ref="seguroInputRef"
          title-text="Seguro"
          stack-label
          v-bind="forms.venda.seguro"
          :toggle-type="forms.venda.tipoValorSeguro.modelValue"
          @on-update-toggle="
            (toggleType) => {
              seguroType = toggleType.label;
              setFieldValue('venda.tipoValorSeguro', toggleType.value);
            }
          "
          :readonly="isDevolucao || readonlyOrDisable"
        />
        <InputPriceOrPercent
          class="no-label tw-col-span-3"
          ref="acrescimoInputRef"
          title-text="Acréscimo"
          stack-label
          v-bind="forms.venda.acrescimo"
          :toggle-type="forms.venda.tipoValorAcrescimo.modelValue"
          @on-update-toggle="
            (toggleType) => {
              acrescimoType = toggleType.label;
              setFieldValue('venda.tipoValorAcrescimo', toggleType.value);
            }
          "
          :readonly="isDevolucao || readonlyOrDisable"
        />

        <InputPriceOrPercent
          class="no-label tw-col-span-3"
          title-text="Desconto"
          stack-label
          ref="descontoInputRef"
          :percentage-rules="descontoPercentageRules"
          :money-rules="descontoMoneyRules"
          v-bind="forms.venda.desconto"
          :toggle-type="forms.venda.tipoValorDesconto.modelValue"
          @on-update-toggle="
            (toggleType) => {
              descontoType = toggleType.label;
              setFieldValue('venda.tipoValorDesconto', toggleType.value);
            }
          "
          :readonly="isDevolucao || readonlyOrDisable || disableDeleteItem"
        />
      </div>

      <ResultTable
        v-if="hasItems"
        :principal-text="pedidoPriceTable?.principalText"
        :labels-and-prices="pedidoPriceTable?.labelsAndPrices"
      />
    </SGCard>

    <SGCard
      :has-circle="!readonlyOrDisable"
      remove-gap
      title="Informações adicionais"
      id="sessao-emissao-nf-informacoes-adicionais"
      :cols="8"
    >
      <q-input
        v-bind="forms.venda.infosAdicionais"
        autogrow
        class="tw-col-span-full"
        maxlength="2500"
        input-style="min-height: 100px"
        type="textarea"
        @focus="
          (e) => {
            hotkeys.filter = function (e) {
              var tagName = e.target.tagName;

              if (global.isPlatformMobile) return false;

              return !(tagName == 'TEXTAREA');
            };
          }
        "
        @blur="
          () => {
            hotkeys.filter = () => (global.isPlatformMobile ? false : true);
          }
        "
        :readonly="readonlyOrDisable"
      />
    </SGCard>

    <!-- <SGCard
      :has-circle="!readonlyOrDisable"
      title="Impostos"
      remove-gap
      id="sessao-emissao-nf-impostos-totais-nota"
      :cols="12"
    >
      <Money
        stack-label
        v-bind="forms.dfeFiscal.bcIcms"
        label="B.C ICMS"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.dfeFiscal.percIcms"
        label="Aliq. ICMS"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.dfeFiscal.valorIcms"
        label="Valor ICMS"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.dfeFiscal.bcIpi"
        label="B.C IPI"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.dfeFiscal.percIpi"
        label="Aliq. IPI"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.dfeFiscal.valorIpi"
        label="Valor IPI"
        class="tw-col-span-4"
        bottom-slots
        :disabled="readonlyOrDisable"
      />

     <Money
        stack-label
        v-bind="forms.valorIcms"
        label="Valor ICMS"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.bcIcmsSt"
        label="B.C ICMS S.T"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.valorIcmsSt"
        label="Valor ICMS S.T"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.valorIpi"
        label="Valor IPI"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      /> -->
    <!--
      <Money
        stack-label
        v-bind="forms.valorSeguro"
        label="Valor Seguro"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.acrescimoNota"
        label="Acréscimo nota"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.descontoNota"
        label="Desconto nota"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
      <Money
        stack-label
        v-bind="forms.outrasDespesas"
        label="Outras despesas"
        class="tw-col-span-3"
        bottom-slots
        :disabled="readonlyOrDisable"
      />
    </SGCard> -->

    <!-- <SGCard
      :has-circle="!readonlyOrDisable"
      title="ISSQN"
      remove-gap
      id="sessao-emissao-nf-notas-issqn"
      :cols="12"
    >
      <q-input
        stack-label
        v-bind="forms.inscricaoMunicipal"
        label="Inscrição municipal"
        class="tw-col-span-3"
        disable
        bottom-slots
        input-class="tw-text-right"
        mask="############"
      />
      <Money
        stack-label
        v-bind="forms.totalServicos"
        label="Total dos serviços"
        class="tw-col-span-3"
        disable
        bottom-slots
      />
      <Money
        stack-label
        v-bind="forms.bcISSQN"
        label="Base de cálc. ISSQN"
        class="tw-col-span-3"
        disable
        bottom-slots
      />
      <Money
        stack-label
        v-bind="forms.valorISSQN"
        label="Valor ISSQN"
        class="tw-col-span-3"
        disable
        bottom-slots
      />
    </SGCard> -->
    <!-- <SGCard
      :has-circle="!readonlyOrDisable"
      remove-gap
      title="Informações para venda fora do país"
      id="sessao-emissao-nf-venda-estrangeira"
      :cols="12"
    >
      <q-input
        stack-label
        v-bind="forms.idEstrangeiro"
        label="ID estrangeiro"
        class="tw-col-span-4"
        maxlength="20"
        bottom-slots
        :readonly="readonlyOrDisable"
      />
      <SelectDefault
        v-bind="forms.ufEmbarque"
        label-slot
        class="tw-col-span-4"
        stack-label
        maxlength="2"
        type="text"
        @keydown="(target) => preventNumber(target)"
        :options="type_uf"
        :popup-content-style="[
          type_uf.length > 3 ? 'height: 150px' : 'height: fit-content',
          'width: 50px'
        ]"
        :readonly="readonlyOrDisable"
      >
        <template #label> UF embarque</template>
      </SelectDefault>
      <q-input
        stack-label
        v-bind="forms.localEmbarque"
        label="Local embarque"
        maxlength="255"
        class="tw-col-span-4"
        bottom-slots
        :readonly="readonlyOrDisable"
      />
    </SGCard>
    <SGCard
      :has-circle="!readonlyOrDisable"
      title="Faturas / duplicatas"
      remove-gap
      id="sessao-emissao-nf-faturas-duplicatas"
      :cols="8"
    >
      <q-input
        outlined
        dense
        disable
        autogrow
        class="tw-col-span-full tw-mb-4"
        input-style="min-height: 100px"
        type="textarea"
        :readonly="readonlyOrDisable"
      />
    </SGCard>
    <SGCard
      :has-circle="!readonlyOrDisable"
      remove-gap
      title="Informações Adicionais"
      id="sessao-emissao-nf-informacoes-adicionais"
      :cols="8"
    >
      <q-input
        outlined
        dense
        disable
        autogrow
        class="tw-col-span-full tw-mb-4"
        input-style="min-height: 100px"
        type="textarea"
        :readonly="readonlyOrDisable"
      />
    </SGCard>
    <SGCard
      :has-circle="!readonlyOrDisable"
      remove-gap
      title="Informações Fiscais"
      id="sessao-emissao-nf-informacoes-fiscais"
      :cols="8"
    >
      <q-input
        outlined
        dense
        disable
        autogrow
        class="tw-col-span-full tw-mb-4"
        input-style="min-height: 100px"
        type="textarea"
        :readonly="readonlyOrDisable"
      />
    </SGCard> -->
  </SGRegisterPage>
</template>

<script setup>
import Money from 'components/generic/input/Money.vue';
import InputPriceOrPercent from 'components/generic/input/PriceOrPercent.vue';
import SelectDefault from 'components/generic/input/Select/default.vue';
import SelectInput from 'components/generic/input/Select/index.vue';
import NfItems from 'components/generic/items/Nf.vue';
import ResultTable from 'components/generic/items/Total.vue';
import SGCard from 'components/generic/SGCard.vue';
import SGRegisterPage from 'components/generic/SGRegister.vue';
import RegisterUndo from 'components/modal/global/RegisterUndo.vue';
import MiniModal from 'components/modal/MiniModal.vue';
import TotalTaxes from 'components/modal/nfe/TotalTaxes.vue';
import hotkeys from 'hotkeys-js';
import DialogModalLayout from 'layouts/DialogModalLayout.vue';
import _ from 'lodash';
import { default as RegisterEmployee } from 'src/modules/funcionarios/views/register.vue';

import { storeToRefs } from 'pinia';
import { uid, useQuasar } from 'quasar';
import useRegisterNfe from 'src/components/actionBar/composables/useRegisterNfe';
import PixContract from 'src/components/modal/pdv/pixContract/PixContract.vue';
import notify, { notifyLoading } from 'src/components/utils/notify';
import { preventNumber } from 'src/components/utils/preventNumber';
import round from 'src/components/utils/round';
import { useScopedHotkeys } from 'src/core/composables/useScopedHotkeys.js';
import MiniModalLayout from 'src/layouts/MiniModalLayout.vue';
import { default as RegisterPeople } from 'src/modules/pessoa/views/register.vue';
import { arrayBufferToBase64, toFloat } from 'src/services/utils';
import { issuePrintNfe } from 'src/stores/api/sales/emitirNfe';
import { useEspelhoNFe } from 'src/stores/api/sales/espelhoNfe';
import { useCompanyData } from 'stores/api/company/index';
import { useNatureOperation } from 'stores/api/natureOperation';
import { useNfe } from 'stores/api/nfe';
import { usePeople } from 'stores/api/people/index';
import { useSales } from 'stores/api/sales';
import { useGlobal } from 'stores/global';
import { computed, nextTick, onMounted, ref, toRaw, toRefs, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NotaReferenciada from './components/notaReferenciada/NotaReferenciada.vue';
import { useFormSchema } from './useFormSchema';
import { useNfeConfigStore } from 'src/modules/vendas/nfe/store/useNfeConfigStore';
import PaymentMethod from 'components/modal/sales/PaymentMethod.vue';
import { isCstCsosn } from 'src/components/utils/checkCstCsosn';
import { printBase64Pdf } from 'src/components/utils/pdf';
import NoStockItems from 'src/components/modal/pdv/NoStockItems.vue';

const readonlyOrDisable = ref(false);

// Definir props
const props = defineProps({
  modal: {
    type: Boolean,
    default: false
  }
});

const nfe = useNfe();
const espelho = useEspelhoNFe();
nfe.resetData();
const { data: nfeData } = storeToRefs(nfe);

const nfeConfigStore = useNfeConfigStore();
const { data: nfeConfigData } = storeToRefs(nfeConfigStore);

const configEmissaoPadrao = computed(() => {
  if (nfeData.value?.producao?.padrao) {
    return 'producao';
  } else if (nfeData.value?.homologacao?.padrao) {
    return 'homologacao';
  }

  return false;
});

const { modal } = toRefs(props);
// Definição de emits
const emit = defineEmits(['cancel', 'ok']);

const OPERATION_NATURE_DEFAULT = {
  controle: '2',
  descricaoOperacao: 'VENDA USUARIO FINAL',
  tipoOperacao: '1',
  cfopDentro: '5102',
  cfopFora: '6102',
  cfopDentroSt: '5405',
  cfopForaSt: '6404',
  movimentaEstoque: 'SIM',
  movimentaFinanceiro: 'SIM',
  finalidadeOperacao: '1',
  descricaoCodigo: '2 - VENDA USUARIO FINAL',
  naturezaOperacaoWithCfopDentro: {
    controle: '287',
    codCfop: '5102',
    descricao: 'VENDA DE MERCADORIA ADQUIRIDA OU RECEBIDA DE TERCEIROS',
    movimentaEstoque: 'SIM',
    usacredito: null,
    tipoOperacao: '1',
    createdBy: null,
    updatedBy: null,
    deletedBy: null,
    createdAt: '2024-09-10 10:10:20',
    updatedAt: '2024-09-10 10:10:20',
    deletedAt: null,
    descricaoCodigo:
      '5102 - VENDA DE MERCADORIA ADQUIRIDA OU RECEBIDA DE TERCEIROS'
  },
  naturezaOperacaoWithCfopFora: {
    controle: '439',
    codCfop: '6102',
    descricao: 'VENDA DE MERCADORIA ADQUIRIDA OU RECEBIDA DE TERCEIROS',
    movimentaEstoque: 'SIM',
    usacredito: null,
    tipoOperacao: '1',
    createdBy: null,
    updatedBy: null,
    deletedBy: null,
    createdAt: '2024-09-10 10:10:20',
    updatedAt: '2024-09-10 10:10:20',
    deletedAt: null,
    descricaoCodigo:
      '6102 - VENDA DE MERCADORIA ADQUIRIDA OU RECEBIDA DE TERCEIROS'
  },
  naturezaOperacaoWithCfopDentroSt: {
    controle: '353',
    codCfop: '5405',
    descricao:
      'VENDA DE MERCADORIA, ADQUIRIDA OU RECEBIDA DE TERCEIROS, SUJEITA AO REGIME DE SUBSTITUIÇÃO TRIB.',
    movimentaEstoque: 'SIM',
    usacredito: null,
    tipoOperacao: '1',
    createdBy: null,
    updatedBy: null,
    deletedBy: null,
    createdAt: '2024-09-10 10:10:20',
    updatedAt: '2024-09-10 10:10:20',
    deletedAt: null,
    descricaoCodigo:
      '5405 - VENDA DE MERCADORIA, ADQUIRIDA OU RECEBIDA DE TERCEIROS, SUJEITA AO REGIME DE SUBSTITUIÇÃO TRIB.'
  },
  naturezaOperacaoWithCfopForaSt: {
    controle: '507',
    codCfop: '6404',
    descricao:
      'VENDA DE MERCADORIA SUJEITA AO REGIME DE SUBSTITUIÇÃO TRIBUTÁRIA ',
    movimentaEstoque: 'SIM',
    usacredito: null,
    tipoOperacao: '1',
    createdBy: null,
    updatedBy: null,
    deletedBy: null,
    createdAt: '2024-09-10 10:10:20',
    updatedAt: '2024-09-10 10:10:20',
    deletedAt: null,
    descricaoCodigo:
      '6404 - VENDA DE MERCADORIA SUJEITA AO REGIME DE SUBSTITUIÇÃO TRIBUTÁRIA '
  }
};
const EMPLOYEE_DEFAULT_VALUE = {
  controle: '1',
  nome: 'CADASTRO PADRÃO',
  descricaoCodigo: '1 - CADASTRO PADRÃO'
};

const consumidorFinalCheckbox = ref(false);

// Ref para controlar animação do desfazer alterações
const isWarningAnimationDisabled = ref(true);

// Instâncias do Quasar, Router e Route
const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const { params } = route;
const global = useGlobal();

const isDevolucao = ref(false);
const isCloning = ref(false);

// const gradesRemovidas = ref([]);

const { hasPixContract } = storeToRefs(global);
const isPixContractModalOpened = ref(false);

const sales = useSales();
const { data: salesData } = storeToRefs(sales);

const disableDeleteItem = computed(
  () => salesData?.value?.valorPago !== '0.00'
);

const company = useCompanyData();
const { data: companyData } = storeToRefs(company);
company.get();

const autoFillForm = ref({
  pessoa: {},
  transportadora: {}
});

const notaReferenciada = ref([]);
const nfeItems = ref([]);
const cfopKey = ref(0);

const validateCstCsosnProduct = (produto) => {
  const codCstCsosn = produto.fiscal.codCstCsosn;
  const crt = companyData.value.empresaWithEmpresaFiscal.crt;

  if (crt == 1 || crt == 5) {
    if (!codCstCsosn || !isCstCsosn(codCstCsosn, 'csosn')) {
      return false;
    }
  } else {
    if (!codCstCsosn || !isCstCsosn(codCstCsosn, 'cst')) {
      return false;
    }
  }

  return true;
};

const uf_options = [
  'AC',
  'AL',
  'AP',
  'AM',
  'BA',
  'CE',
  'DF',
  'ES',
  'GO',
  'MA',
  'MT',
  'MS',
  'MG',
  'PA',
  'PB',
  'PR',
  'PE',
  'PI',
  'RJ',
  'RN',
  'RS',
  'RO',
  'RR',
  'SC',
  'SP',
  'SE',
  'TO',
  'EX'
];
const type_uf = ref([...uf_options]);

// Regras de validação para desconto em Dinheiro e Porcentagem
const descontoPercentageRules = [
  (val) => {
    return toFloat(val) < 100 || 'Desconto não pode ser maior que 99,9%';
  }
];

const descontoMoneyRules = [
  (val) => {
    return (
      toFloat(val) < pedidoPriceTable.value.itemsTotal ||
      !toFloat(val) ||
      'Desconto não pode ser maior ou igual ao total dos produtos/serviços'
    );
  }
];

const handleSetInfosAdicionais = () => {
  if (nfeData.value?.homologacao || nfeData.value?.producao) {
    if (configEmissaoPadrao.value) {
      setFieldValue(
        'venda.infosAdicionais',
        nfeData.value?.[configEmissaoPadrao.value]?.descricao
      );
    }
  }
};

// Retorna true caso a venda esteja bloqueada para estoque negativo ou zerado.
const bloquearVendaEstoqueNegativoOuZerado = ref(false);

// instância do useFormSchema
const { forms, formValues, resetForm, setFieldValue } = useFormSchema();

let initialValues;

if (
  (route.path.includes('editar') || route.path.includes('visualizar')) &&
  params.controle &&
  !props.modal
) {
  readonlyOrDisable.value = route.path.includes('visualizar');
  sales.get(params.controle).then(async () => {
    if (salesData.value?.modulo === '4') {
      isDevolucao.value = true;
      // notaReferenciada.value.push({
      //   chaveAcesso:
      //     salesData.value.vendaWithDevolucao.vendaReferenciada.vendaWithDfe
      //       .chaveAcesso
      // });
      const { pessoa } = salesData.value;
      if (pessoa?.controle == '1') {
        notify(
          'Não é possível emitir NF-e com cadastro padrão selecionado como "Cliente". Altere para prosseguir com a emissão',
          'warning',
          'top',
          4000
        );
        salesData.value.pessoa = null;
      }
    }

    await sales.handleEditLoading(setEditData);
  });
} else if ($q.sessionStorage.getItem('cloneId')) {
  isCloning.value = true;
  const davControle = $q.sessionStorage.getItem('cloneId');
  const importarProdutoServicoOuApenasProduto = $q.sessionStorage.getItem(
    'importarProdutoServicoOuApenasProduto'
  );
  $q.sessionStorage.remove('cloneId');
  $q.sessionStorage.remove('importarProdutoServicoOuApenasProduto');

  nfe.get(null, false, false);

  sales.get(davControle).then(async () => {
    nextTick(() => {
      if (peopleInputRef.value && salesData.value?.pessoa?.controle == '1') {
        peopleInputRef.value.reset();
        setFieldValue('venda.codCliente', null);
        clientEditValue.value = null;
        peopleInputRef.value.validate();
        autoFillForm.value.pessoa = {};

        notify(
          'Não é possível emitir NF-e com cadastro padrão selecionado como "Cliente". Altere para prosseguir com a emissão',
          'warning',
          'top',
          4000
        );
      }
    });

    salesData.value.vendaWithDfe = [];
    salesData.value.vendaWithPagamento = [];
    salesData.value.vendaWithFinanceiroMovimento = [];
    salesData.value.vendaWithFinanceiro = [];
    salesData.value.vendaPaga = false;
    salesData.value.confirmada = false;
    salesData.value.cancelada = false;
    if (salesData.value.vendaWithDav) {
      salesData.value.davImportada = salesData.value?.controle;
    }
    salesData.value.vendaWithDav = null;

    if (salesData.value?.modulo === '4') {
      isDevolucao.value = true;
      const { pessoa } = salesData.value;
      if (pessoa?.controle == '1') {
        notify(
          'Não é possível emitir NF-e com cadastro padrão selecionado como "Cliente". Altere para prosseguir com a emissão',
          'warning',
          'top',
          4000
        );
        salesData.value.pessoa = null;
      }
    }

    salesData.value.vendaWithItem = salesData.value.vendaWithItem
      .map((item) => {
        const estoqueZeradoOuNegativo =
          Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtde) <= 0;

        const quantidadeVendaZeradoOuNegativo = Number(item.qtde ?? 0) <= 0;

        if (
          bloquearVendaEstoqueNegativoOuZerado.value &&
          estoqueZeradoOuNegativo
        ) {
          notify('Venda bloqueada para estoque negativo ou zerado');
          router.push('/vendas/nf');
        }

        if (quantidadeVendaZeradoOuNegativo) return null;

        item.controle = null;
        return item;
      })
      .filter((i) => i);

    if (importarProdutoServicoOuApenasProduto === 'produtos') {
      salesData.value.vendaWithItem = salesData.value.vendaWithItem.filter(
        (item) =>
          item.vendaItemWithProduto?.produtoWithCaracteristica?.codTipoUso !=
          '10'
      );
    }

    await sales.handleEditLoading(setEditData);
  });
} else {
  await nfe.get(null, false, false);

  handleSetInfosAdicionais();

  initialValues = _.cloneDeep(formValues);
  initialValues.venda.consumidorFinal = false;
}

const setEditData = async () => {
  const raw = toRaw(salesData.value);

  intermediadorEditValue.value = raw.intermediador;
  clientEditValue.value = raw.pessoa;
  employeeEditValue.value = raw.vendaWithFuncionarioResponsavel;
  operationNatureEditValue.value =
    raw.vendaWithNaturezaOperacao || OPERATION_NATURE_DEFAULT;
  consumidorFinalCheckbox.value = raw.consumidorFinal || false;
  transporterEditData.value = raw?.vendaWithTransporte?.transportadora;

  // TODO: Sempre que alterar algo do setEditData, verificar geracao de espelho na listagem
  nfeItems.value = await raw.vendaWithItem.map((item, index) => {
    const cstCsosn = item?.vendaItemWithProduto?.produtoWithFiscal?.codCstCsosn;

    let itemFiscal;

    //TODO: Ajustar essa validação, se continuar colocando números não vai escalar bem
    if (
      cstCsosn == '900' ||
      cstCsosn == '90' ||
      cstCsosn == '20' ||
      naturezaOperacaoComplementar.value ||
      naturezaOperacaoDeAjuste.value
    ) {
      itemFiscal = item.vendaItemWithFiscal;
    } else {
      itemFiscal = {
        ...item.vendaItemWithFiscal,
        aliqIcms: null,
        // Informacao necessária caso cstCsosn for igual a 51 por causa do codBeneficioRbc
        percReducaoBcIcms:
          cstCsosn == '51' ? item.vendaItemWithFiscal.percReducaoBcIcms : null,
        bcIcms: null,
        valorIcms: null,
        aliqPis: null,
        bcPis: null,
        valorPis: null,
        aliqIcmsSt: null,
        bcIcmsSt: null,
        valorIcmsSt: null,
        percReducaoBcIcmsSt: null,
        percMva: null,
        aliqCofins: null,
        bcCofins: null,
        valorCofins: null,
        aliqIpi: null,
        bcIpi: null,
        valorIpi: null,
        // aliqFcp: nullp
        // bcFcp: nullp
        // valorFcp: nullp
        aliqFcpSt: null,
        bcFcpSt: null,
        valorFcpSt: null
      };
    }

    let tempItem = {
      itemData: { ...item },
      index,
      controle: item.controle,
      nome: item?.vendaItemWithProduto?.nome,
      codProduto: item.codProduto,
      xPed: item?.xPed,
      nItemPed: item?.nItemPed,
      codVenda: item.codVenda,
      qtde: Number(item.qtde), // Manipulável pelo usuário.
      qtdeEstoque: Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtde), // Em estoque no cadastro de produtos.
      qtdeReal: Number(item.vendaItemWithProduto?.produtoWithEstoque?.qtdeReal), // Considerando reservas em vendas abertas.
      qtdeInicial: Number(item.qtde), // Inicial não manipulável.
      valorUnitario: parseFloat(item.valorUnitario),
      valorDesconto: parseFloat(item.valorDesconto),
      tipoValorDesconto: Number(item.tipoValorDesconto),
      valorAcrescimo: parseFloat(item.valorAcrescimo),
      tipoValorAcrescimo: Number(item.tipoValorAcrescimo),
      fiscal: {
        ...itemFiscal,
        aliqIcmsDeson:
          item?.vendaItemWithProduto?.produtoWithFiscal?.aliqIcmsDeson || 0
      },
      cstCsosn,
      selectData: item.vendaItemWithProduto,
      usaGrade: item.usaGrade,
      grade: item?.vendaItemWithGrade,
      usaLote: false,
      usaSerial: false
    };

    notaReferenciada.value =
      raw?.vendaWithDevolucao?.vendaReferenciada &&
      raw?.vendaWithReferenciadas.length < 1
        ? [
            {
              id: uid(),
              ...raw?.vendaWithDevolucao?.vendaReferenciada,
              modeloReferencia: raw?.vendaWithDevolucao?.tipoVenda,
              modelo: '02', // modelo 02 padrao no caso de devolucao segundo Erminio
              chaveAcesso:
                raw?.vendaWithDevolucao?.vendaReferenciada?.vendaWithDfe
                  ?.chaveAcesso
            }
          ]
        : raw?.vendaWithReferenciadas?.map((item) => ({
            id: uid(),
            ...item
          }));

    if (!tempItem.fiscal) tempItem.fiscal = {};

    tempItem.fiscal['codCfop'] =
      item?.vendaItemWithFiscal?.codCfop ??
      item?.vendaItemWithProduto?.produtoWithFiscal?.codCfop;

    tempItem.fiscal['cfop'] =
      item?.vendaItemWithFiscal?.vendaItemFiscalWithCfop?.codCfop ??
      item?.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCfop
        ?.codCfop;

    tempItem['cfopEditValue'] =
      item?.vendaItemWithFiscal?.vendaItemFiscalWithCfop ??
      item?.vendaItemWithProduto?.produtoWithFiscal?.produtoFiscalWithCfop;

    tempItem.fiscal['codNcmNew'] = item?.vendaItemWithFiscal?.codNcmNew ?? null;

    tempItem.fiscal['codCestNew'] =
      item?.vendaItemWithFiscal?.codCestNew ?? null;

    tempItem.fiscal['codCstCsosn'] =
      item?.vendaItemWithFiscal?.codCstCsosn ??
      item?.vendaItemWithProduto?.produtoWithFiscal?.codCstCsosn;

    tempItem['cstCsosnEditValue'] =
      item?.vendaItemWithFiscal?.vendaItemFiscalWithCsosn ||
      item?.vendaItemWithFiscal?.vendaItemFiscalWithCst;

    if (isDevolucao.value || isCloning.value || !tempItem.cfopEditValue) {
      tempItem.fiscal['codCfop'] = fetchOperationNatureCfop(tempItem)?.controle;
      tempItem.fiscal['cfop'] = fetchOperationNatureCfop(tempItem)?.codCfop;
      tempItem.cfopEditValue = fetchOperationNatureCfop(tempItem);
    }

    if (!tempItem['cstCsosnEditValue'] && tempItem.fiscal['codCstCsosn']) {
      const codCstCsosn = tempItem.fiscal['codCstCsosn'];
      tempItem['cstCsosnEditValue'] = {
        codigo: codCstCsosn,
        descricaoCodigo: codCstCsosn
      };
    }

    return tempItem;
  });

  autoFillForm.value.transportadora.indIe =
    raw?.vendaWithTransporte?.transportadora?.pessoaWithFiscal?.indicadorIe?.descricao;
  autoFillForm.value.transportadora.logradouro =
    raw?.vendaWithTransporte?.transportadora?.pessoaWithEndereco?.endereco;
  autoFillForm.value.transportadora.estado =
    raw?.vendaWithTransporte?.transportadora?.pessoaWithEndereco?.uf;
  autoFillForm.value.transportadora.documento =
    raw?.vendaWithTransporte?.transportadora?.cnpjCpf;
  autoFillForm.value.transportadora.ie =
    raw?.vendaWithTransporte?.transportadora?.pessoaWithFiscal?.ie;
  autoFillForm.value.transportadora.codigoAntt =
    raw?.vendaWithTransporte?.transportadora?.pessoaWithFiscal?.codigoAntt;

  autoFillForm.value.pessoa.indIe =
    raw?.pessoa?.pessoaWithFiscal?.indicadorIe?.descricao;
  autoFillForm.value.pessoa.logradouro =
    raw?.pessoa?.pessoaWithEndereco?.endereco;
  autoFillForm.value.pessoa.estado = raw?.pessoa?.pessoaWithEndereco?.uf;
  autoFillForm.value.pessoa.cidade = raw?.pessoa?.pessoaWithEndereco?.cidade;
  autoFillForm.value.pessoa.complemento =
    raw?.pessoa?.pessoaWithEndereco?.complemento;
  autoFillForm.value.pessoa.bairro = raw?.pessoa?.pessoaWithEndereco?.bairro;
  autoFillForm.value.pessoa.cep = raw?.pessoa?.pessoaWithEndereco?.cep;
  autoFillForm.value.pessoa.numero = raw?.pessoa?.pessoaWithEndereco?.numero;
  autoFillForm.value.pessoa.ie = raw?.pessoa?.pessoaWithFiscal?.ie;
  autoFillForm.value.pessoa.documento = raw?.pessoa?.cnpjCpf;

  resetForm({
    values: {
      venda: {
        davImportada: raw?.davImportada,
        controle: raw?.controle,
        codCliente: raw.codCliente,
        cliente: raw.pessoa,
        funcionario: raw.vendaWithFuncionarioResponsavel,
        codNaturezaOperacao: raw.codNaturezaOperacao || '2',
        natureza: raw.vendaWithNaturezaOperacao || OPERATION_NATURE_DEFAULT,
        codFuncionarioResponsavel: raw.codFuncionarioResponsavel,
        codIntermediador: raw.codIntermediador,
        intermediador: raw.intermediador,
        tipoVenda: raw.tipoVenda || '1',
        consumidorFinal: raw.consumidorFinal || false,

        vendaPaga: raw.vendaPaga,

        observacoes: raw.observacoes,
        infosAdicionais: raw.infosAdicionais,
        frete: parseFloat(raw.frete),
        seguro: parseFloat(raw.seguro),
        acrescimo: parseFloat(raw.acrescimo),
        desconto: parseFloat(raw.desconto),

        valorBruto: parseFloat(raw.valorBruto),
        valorLiquido: raw.valorLiquido,
        valorProdutos: Number(raw.valorProdutos),
        valorServicos: Number(raw.valorServicos),
        tipoValorFrete: Number(raw.tipoValorFrete),
        tipoValorSeguro: Number(raw.tipoValorSeguro),
        tipoValorAcrescimo: Number(raw.tipoValorAcrescimo),
        tipoValorDesconto: Number(raw.tipoValorDesconto),
        modulo: isDevolucao.value ? 4 : 2
      },
      item: _.cloneDeep(nfeItems.value),
      transporte: {
        codFrete: raw?.vendaWithTransporte?.codFrete || '9',
        codTransportadora: raw?.vendaWithTransporte?.codTransportadora,
        codVenda: raw?.controle,
        codigoAntt: raw?.vendaWithTransporte?.codigoAntt,
        especieVolume: raw?.vendaWithTransporte?.especieVolume,
        marcaVolume: raw?.vendaWithTransporte?.marcaVolume,
        numeroVolume: raw?.vendaWithTransporte?.numeroVolume,
        pesoBruto: raw?.vendaWithTransporte?.pesoBruto,
        pesoLiquido: raw?.vendaWithTransporte?.pesoLiquido,
        placaVeiculo: raw?.vendaWithTransporte?.placaVeiculo,
        qtdeTransportada: raw?.vendaWithTransporte?.qtdeTransportada,
        ufPlaca: raw?.vendaWithTransporte?.ufPlaca
      }
    }
  });

  handleSetInfosAdicionais();

  initialValues = _.cloneDeep(formValues);

  isCloning.value = false;
};

const people = usePeople();
const clientEditValue = ref();
const intermediadorEditValue = ref();
const transporterEditData = ref();
const peopleInputRef = ref();
const updatePeopleOptions = () => {
  peopleInputRef.value?.mountSelectOptions();
};
const openRegisterPeople = (controle = null) => {
  let props = {};

  if (controle) {
    props = {
      data: {
        isEditing: true,
        controle
      },
      modal: true
    };
  }
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterPeople,
      scope: 'register-people',
      ...props
    }
  }).onDismiss(() => {
    updatePeopleOptions();
  });
};

/**
 * Altera o campo no schema e verifica necessidade de troca de CFOP dos itens da venda.
 * @param value Cliente selecionado.
 */
const handleClientChange = async (value) => {
  const enderecoCliente = value
    ? await enderecoDoClientePreenchido(value)
    : null;

  if (!enderecoCliente) {
    peopleInputRef.value.reset();
    setFieldValue('venda.codCliente', null);
    clientEditValue.value = null;

    peopleInputRef.value.validate();

    autoFillForm.value.pessoa = {};

    return;
  }

  value.endereco = value?.pessoaWithEndereco || value?.fornecedorWithEndereco;
  value.fiscal = value?.pessoaWithFiscal || value?.fornecedorWithFiscal;

  const previousClient = clientEditValue.value;
  setFieldValue('venda.codCliente', value?.controle || null);
  clientEditValue.value = value;

  autoFillForm.value.pessoa.indIe =
    value?.pessoaWithFiscal?.indicadorIe?.descricao;
  autoFillForm.value.pessoa.logradouro = value?.endereco?.endereco;
  autoFillForm.value.pessoa.estado = value?.endereco?.uf;
  autoFillForm.value.pessoa.cidade = value?.endereco?.cidade;
  autoFillForm.value.pessoa.complemento = value?.endereco?.complemento;
  autoFillForm.value.pessoa.bairro = value?.endereco?.bairro;
  autoFillForm.value.pessoa.cep = value?.endereco?.cep;
  autoFillForm.value.pessoa.numero = value?.endereco?.numero;
  autoFillForm.value.pessoa.ie = value?.fiscal?.ie;
  autoFillForm.value.pessoa.documento = value?.cnpjCpf;

  const confirm = await openClientChangeConfirmModal(previousClient, value);
  if (!confirm) {
    return;
  }

  nfeItems.value.forEach((item) => {
    item.fiscal.codCfop = fetchOperationNatureCfop(item)?.controle;
    item.fiscal.cfop = fetchOperationNatureCfop(item)?.codCfop;
    item.cfopEditValue = fetchOperationNatureCfop(item);
  });
  cfopKey.value++;
};

/**
 * Busca o CFOP da natureza de operação baseado em:
 * - Estado do cliente da venda (dentro e fora)
 * - CST/CSOSN do produto ou do item da venda quando devolução. (com ST. ou sem)
 * @param product Produto adicionado
 */
const fetchOperationNatureCfop = (product) => {
  if (!operationNatureEditValue.value) return false;

  // CST e CSOSN com substituição
  const CST_CSOSN_COM_ST = ['201', '202', '203', '500', '10', '30', '70', '60'];

  const clientState =
    clientEditValue.value?.pessoaWithEndereco?.uf ||
    clientEditValue.value?.fornecedorWithEndereco?.uf;

  const companyState = companyData.value?.empresaWithEmpresaEndereco?.uf;
  const cfopOriginalDoProduto =
    product?.selectData?.produtoWithFiscal?.produtoFiscalWithCfop;

  if (
    operationNatureEditValue.value?.finalidadeOperacao == '1' &&
    (clientState ? clientState == companyState : true) &&
    cfopOriginalDoProduto
  ) {
    return cfopOriginalDoProduto;
  }

  if (clientState && clientState != companyState) {
    return CST_CSOSN_COM_ST.includes(product?.fiscal?.codCstCsosn)
      ? operationNatureEditValue.value.naturezaOperacaoWithCfopForaSt
      : operationNatureEditValue.value.naturezaOperacaoWithCfopFora;
  }

  return CST_CSOSN_COM_ST.includes(product?.fiscal?.codCstCsosn)
    ? operationNatureEditValue.value.naturezaOperacaoWithCfopDentroSt
    : operationNatureEditValue.value.naturezaOperacaoWithCfopDentro;
};

const naturezaOperacaoComplementar = computed(
  () => operationNatureEditValue.value?.finalidadeOperacao === '2'
);

const naturezaOperacaoDeAjuste = computed(
  () => operationNatureEditValue.value?.finalidadeOperacao === '3'
);

const naturezaOperacaoDeDevolucaoOuNormalReferenciada = computed(
  () =>
    operationNatureEditValue.value?.finalidadeOperacao === '4' ||
    operationNatureEditValue.value?.finalidadeOperacao === '5'
);

const showNotasReferenciadas = computed(
  () =>
    naturezaOperacaoDeDevolucaoOuNormalReferenciada.value ||
    naturezaOperacaoComplementar.value ||
    naturezaOperacaoDeAjuste.value
);

/**
 * Se há itens na NF-e, abre modal e solicita confirmação da alteração de natureza de operação, conforme condições específicadas na função.
 * @param previousClient Cliente anterior.
 * @param currentClient Cliente atual.
 */
const openClientChangeConfirmModal = async (previousClient, currentClient) => {
  if (!nfeItems.value?.length) return true;

  const previousClientState = previousClient?.pessoaWithEndereco?.uf;
  const currentClientState = currentClient?.pessoaWithEndereco?.uf;
  const companyState = companyData.value?.empresaWithEmpresaEndereco?.uf;

  /* Não solicita troca de CFOP se e somente se:
    - Cliente anterior e atual tiverem o mesmo estado
    - Não há cliente anterior, e o atual possui o mesmo estado da empresa.
    - Há cliente anterior de estado diferente, porém não há atual.
    - Cliente anterior e atual são de estados diferentes.
  */
  if (
    (previousClientState &&
      currentClientState &&
      previousClientState == currentClientState) ||
    (!previousClientState &&
      currentClientState &&
      companyState == currentClientState) ||
    (previousClientState &&
      !currentClientState &&
      previousClientState == companyState) ||
    (previousClientState &&
      currentClientState &&
      previousClientState != companyState &&
      currentClientState != companyState)
  ) {
    return false;
  }

  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        scope: 'client-change',
        title: 'Alteração de cliente',
        classCardSection: 'lg:tw-w-[500px]',
        confirmLabel: 'SIM',
        cancelLabel: 'NÃO',
        description:
          'O cliente selecionado é de outro estado, deseja ajustar o CFOP dos itens da venda de acordo com os configurados na natureza de operação?'
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};

//---------------------------------------------------------
// Select de funcionário
//---------------------------------------------------------
import { useSelectEmployeeStore } from 'src/stores/api/selectEmployee';
const updateEmployeeOptions = async () => {
  employeeInputRef.value?.mountSelectOptions();
  sellerinputRef.value?.mountSelectOptions();
};

const selectEmployeeStore = useSelectEmployeeStore();
selectEmployeeStore.resetData();
const employeeInputRef = ref();
const sellerinputRef = ref();
const employeeEditValue = ref();

const selectEmployeeFiltersV2 = {
  filtersV2: [
    {
      not_deleted: true,
      field: 'codUsuario',
      filterType: 'FIXED',
      filterValue: global?.user?.controle
    }
  ]
};

// FIXED MANUAL (O SELECT ANTIGO NAO TEM TRATAMENTO)
// (NA PROXIMA REFATORACAO NAO DEVE SER USADO, POIS O FIXED IRÁ FUNCIONAR)
// MAS O FILTRO DIFERENTE DEVE SER USADO NA PROXIMA REFATORACAO
watch(
  () => selectEmployeeStore?.fixed,
  () => {
    if (params.controle == null) {
      employeeEditValue.value = selectEmployeeStore?.fixed || {
        controle: '1',
        nome: 'CADASTRO PADRÃO',
        descricaoCodigo: '1 - CADASTRO PADRÃO'
      };

      if (selectEmployeeStore?.fixed) {
        resetForm({
          values: {
            ...formValues,
            venda: {
              ...formValues.venda,
              codFuncionarioResponsavel:
                selectEmployeeStore?.fixed?.controle || '1 '
            }
          }
        });
      }
    }
  },
  { immediate: true }
);

const openRegisterEmployee = () => {
  $q.dialog({
    component: DialogModalLayout,
    componentProps: {
      componentRef: RegisterEmployee,
      scope: 'register-employee'
    }
  }).onOk(async () => {
    await updateEmployeeOptions();
  });
};

// Ref para os selects de produto
const productInputRefs = ref();

const acrescimoInputRef = ref();
const freteInputRef = ref();
const seguroInputRef = ref();
const descontoInputRef = ref();

const intermediadorFilters = {
  cnpjCpf: {
    filterType: 'LENGTH_EQUALS',
    filterValue: '14'
  }
};

const transporterFilters = {
  'pessoaWithPessoaTipoCadastro.tipoCadastro.descricao': {
    filterType: 'ILIKE',
    filterValue: 'Transportadora'
  }
};

onMounted(async () => {
  const showPixContractWarning = localStorage.getItem('showPixContractWarning');
  if (
    !hasPixContract.value &&
    (showPixContractWarning === 'true' || !showPixContractWarning)
  ) {
    isPixContractModalOpened.value = true;
  }

  await nfeConfigStore.get({ deletedAt: false });

  bloquearVendaEstoqueNegativoOuZerado.value =
    nfeConfigData.value[0]?.bloquearVendaEstoqueNegativoOuZerado;
});

// Instanciar refs de frete, acréscimo e desconto
const freteType = ref('money');
const seguroType = ref('money');
const acrescimoType = ref('money');
const descontoType = ref('money');

function calcValorTotalItem(item) {
  const bruto = Number(item.qtde) * Number(item.valorUnitario);

  let desconto = 0;
  let acrescimo = 0;

  if (item.tipoValorDesconto === 0) {
    desconto = round(item.valorDesconto ?? 0, 2);
  } else {
    desconto = round((bruto * item.valorDesconto) / 100, 2);
  }

  if (item.tipoValorAcrescimo === 0) {
    acrescimo = round(item.valorAcrescimo, 2);
  } else {
    acrescimo = round((bruto * item.valorAcrescimo) / 100, 2);
  }

  return round(bruto - desconto + acrescimo, 2);
}

// Computed que retorna informações de valores dos itens do pedido de venda
const pedidoPriceTable = computed(() => {
  let productTotal = [];
  let serviceTotal = [];
  let grossTotal = [];

  // tipoValor Desconto/Acrescimo/Frete/Seguro = { 0: Dinheiro, 1: Porcentagem}
  nfeItems.value?.forEach((item) => {
    if (item?.deletar) return;

    let calc = 0;

    if (item?.codProduto) {
      calc = calcValorTotalItem(item);
    }

    const calcGross = item.codProduto
      ? round(Number(item.qtde) * Number(item.valorUnitario), 2)
      : 0;

    // Verificar se é um serviço, cujo controle é igual a 10
    if (item?.selectData?.produtoWithCaracteristica?.codTipoUso === '10') {
      serviceTotal.push(calc);

      grossTotal.push(calcGross);
    } else {
      productTotal.push(calc);

      grossTotal.push(calcGross);
    }
  });

  const productTotalSum = productTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );
  const serviceTotalSum = serviceTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  const grossTotalSum = grossTotal.reduce(
    (ac, current) => ac + parseFloat(current),
    0
  );

  setFieldValue('venda.valorBruto', grossTotalSum);
  setFieldValue('venda.valorProdutos', productTotalSum);
  setFieldValue('venda.valorServicos', serviceTotalSum);

  const itemsTotal = Number(round(productTotalSum + serviceTotalSum, 2));

  const acrescimo = ref(0);
  const desconto = ref(0);
  const frete = ref(0);
  const seguro = ref(0);

  // Calcula o valor do acréscimo
  if (acrescimoType.value === 'percentage' && formValues.venda.acrescimo > 0) {
    const percentage = formValues.venda.acrescimo / 100;
    acrescimo.value = Number(round(percentage * itemsTotal, 2));
  } else if (
    acrescimoType.value === 'money' &&
    formValues.venda.acrescimo > 0
  ) {
    acrescimo.value = formValues.venda.acrescimo;
  }

  // Calcula o valor do desconto
  if (descontoType.value === 'percentage' && formValues.venda.desconto > 0) {
    const percentage = formValues.venda.desconto / 100;
    desconto.value = Number(round(percentage * itemsTotal, 2));
  } else if (descontoType.value === 'money' && formValues.venda.desconto > 0) {
    desconto.value = formValues.venda.desconto;
  }

  // Calcula o valor do frete
  if (freteType.value === 'percentage' && formValues.venda.frete > 0) {
    const percentage = formValues.venda.frete / 100;
    frete.value = Number(round(percentage * itemsTotal, 2));
  } else if (freteType.value === 'money' && formValues.venda.frete > 0) {
    frete.value = formValues.venda.frete;
  }

  // Calcula o valor do seguro
  if (seguroType.value === 'percentage' && formValues.venda.seguro > 0) {
    const percentage = formValues.venda.seguro / 100;
    seguro.value = Number(round(percentage * itemsTotal, 2));
  } else if (seguroType.value === 'money' && formValues.venda.seguro > 0) {
    seguro.value = formValues.venda.seguro;
  }

  return {
    itemsTotal,
    grossTotalSum,
    productTotalSum,
    serviceTotalSum,
    total:
      itemsTotal +
        frete.value +
        seguro.value +
        acrescimo.value -
        desconto.value || 0,
    principalText: 'Totais da nota',
    labelsAndPrices: [
      {
        label: 'Valor dos produtos',
        value: productTotalSum || 0,
        isMoney: true
      },
      {
        label: 'Valor dos serviços',
        value: serviceTotalSum || 0,
        isMoney: true
      },
      { label: 'Valor do frete', value: frete.value, isMoney: true },
      { label: 'Valor do seguro', value: seguro.value, isMoney: true },
      { label: 'Acréscimo', value: acrescimo.value, isMoney: true },
      { label: 'Desconto', value: desconto.value, isMoney: true },
      {
        label: 'Total',
        value:
          itemsTotal +
            frete.value +
            seguro.value +
            acrescimo.value -
            desconto.value || 0,
        isMoney: true
      }
    ]
  };
});

const natureOperation = useNatureOperation();
const operationNatureEditValue = ref(OPERATION_NATURE_DEFAULT);
const operationNatureRef = ref();
const operationNatureFilters = computed(() =>
  isDevolucao.value
    ? {
        finalidadeOperacao: {
          filterType: 'EQUALS',
          filterValue: '4'
        }
      }
    : {}
);

/**
 * Solicita mudança de CFOP dos itens ao mudar natureza de operação
 * @param value Natureza de operação selecionada.
 */
const handleOperatioNatureChange = async (value) => {
  const previousOperationNature = operationNatureEditValue.value;

  operationNatureEditValue.value = value;
  if (!value) return;

  const deletarNotasReferenciadas =
    await openOperationNatureChangeFinalidadeModal(previousOperationNature);
  if (!deletarNotasReferenciadas) {
    operationNatureEditValue.value = previousOperationNature;

    return;
  }

  setFieldValue('venda.codNaturezaOperacao', value?.controle || null);

  const confirm = await openOperationNatureConfirmModal(
    previousOperationNature
  );
  if (!confirm) return;

  nfeItems.value.forEach((item) => {
    item.fiscal.codCfop = fetchOperationNatureCfop(item)?.controle;
    item.fiscal.cfop = fetchOperationNatureCfop(item)?.codCfop;
    item.cfopEditValue = fetchOperationNatureCfop(item);
  });
  cfopKey.value++;
};

/**
 * Se há itens na NF-e, abre modal e solicita confirmação da alteração de natureza de operação.
 */
const openOperationNatureConfirmModal = async (previousOperationNature) => {
  if (
    !nfeItems.value?.length ||
    (operationNatureEditValue.value?.finalidadeOperacao == '1' &&
      previousOperationNature?.finalidadeOperacao == '1')
  ) {
    return true;
  }

  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        scope: 'operation-nature-change',
        title: 'Alteração de natureza de operação',
        classCardSection: 'lg:tw-w-[500px]',
        description:
          'Alterando a natureza de operação, deseja confirmar a troca da CFOP dos itens de acordo com a configuração da nova natureza de operação? Ao cancelar, a natureza de operação será trocada, mas os CFOP continuarão os mesmos da natureza anterior.'
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });
};
/**
 * Caso troque uma natureza com nota referenciada para uma sem
 */
const openOperationNatureChangeFinalidadeModal = async (
  previousOperationNature
) => {
  const naturezaAnteriorPossuiNotaReferenciada =
    previousOperationNature?.finalidadeOperacao == '4' ||
    previousOperationNature?.finalidadeOperacao == '5' ||
    previousOperationNature?.finalidadeOperacao == '3' ||
    previousOperationNature?.finalidadeOperacao == '2';

  const naturezaPossuiNotaReferenciada =
    operationNatureEditValue.value?.finalidadeOperacao == '4' ||
    operationNatureEditValue.value?.finalidadeOperacao == '5' ||
    operationNatureEditValue.value?.finalidadeOperacao == '3' ||
    operationNatureEditValue.value?.finalidadeOperacao == '2';

  if (
    !notaReferenciada.value?.length ||
    (naturezaPossuiNotaReferenciada &&
      naturezaAnteriorPossuiNotaReferenciada) ||
    !naturezaAnteriorPossuiNotaReferenciada
  ) {
    return true;
  }

  return await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        scope: 'operation-nature-change',
        title: 'Alteração de natureza de operação',
        classCardSection: 'lg:tw-w-[500px]',
        cancelLabel: 'Não',
        confirmLabel: 'Sim',
        description:
          'Está natureza de operação não possui notas referenciadas, as notas referenciadas atuais serão excluídas. Deseja continuar?'
      }
    })
      .onOk(() => {
        notaReferenciada.value = [];
        return resolve(true);
      })
      .onCancel(() => resolve(false));
  });
};

const cancel = () => {
  if (isDevolucao.value) {
    router.push('/vendas/devolucao');
  } else {
    router.push('/vendas/nf');
  }
};

// const stateTexts = reactive({
//   one: 'MD5 : C212618357429D0628DA57D8957DAB5C; DOCUMENTO EMITIDO POR ME OU EPP OPTANTE PELO SIMPLES NACIONAL.;NÃO GERA DIREITO A CRÉDITO FISCAL DE IPI;',
//   two: 'MD9 : C456845094809G54G45J8B5GB948; DOCUMENTO EMITIDO POR ME OU EPP OPTANTE PELO SIMPLES NACIONAL.;NÃO GERA DIREITO A CRÉDITO FISCAL DE IPI;'
// });

// Ref para saber se os items foram preenchidos
const itemsPropsDoneTwo = computed(
  () => nfeItems.value.filter((row) => !row?.deletar).length > 0
);

// Computed para verificar se o campo de desconto possui erros
const propsDoneDesconto = computed(() => {
  if (formValues.venda.tipoValorDesconto === 1) {
    return formValues.venda.desconto < 100;
  } else {
    return (
      formValues.venda.desconto < pedidoPriceTable.value.grossTotalSum ||
      !formValues.venda.desconto
    );
  }
});

const hasItems = computed(() => {
  return nfeItems.value.filter((item) => !item.deletar).length > 0;
});

//Ref para saber se o botão de salvar está liberado
const completed = ref(false);

// Verificar se o botão de salvar pode ser liberado
const handleCompletedChanged = (isCompleted) => {
  completed.value =
    isCompleted && itemsPropsDoneTwo.value && propsDoneDesconto.value;
  completed.value = isCompleted;
};

const openTotalTaxesModal = async (controle) => {
  const modalPromise = new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: TotalTaxes,
        scope: 'totais-impostos',
        hasCancel: false,
        hasSave: false,
        hasCloseIcon: true,
        title: 'Totais e impostos da nota',
        dataModal: {
          controle
        }
      }
    })
      .onOk((payload) => resolve(payload))
      .onCancel(() => resolve(false));
  });

  return await modalPromise;
};

/**
 * Executa o fluxo de salvamento confirmado.
 * @param {Object} newSale Novo objeto da venda ou DAV não alterada.
 * @param {Boolean} isEditing Sinalizador booleano para salvar uma nova venda ou editar uma existente.
 */
const handlePaymentFlow = async (newSale, emitir, total) => {
  const { data } = newSale;
  const { vNF } = toRefs(total);

  const modalPromise = new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: PaymentMethod,
        scope: 'payment-method',
        hasCancel: false,
        hasSave: false,
        dataModal: {
          totalProp: vNF.value,
          valorPago: vNF.value,
          vendaProp: data.venda,
          emitir
        }
      }
    })
      .onOk(() => resolve(true))
      .onCancel(() => resolve(false));
  });

  return await modalPromise;
};

watch(
  () => route.path,
  (newRoute) => {
    if (newRoute.includes('editar')) {
      const controle = newRoute.split('/').pop();

      sales.get(controle).then(async () => {
        if (salesData.value?.modulo === '4') {
          isDevolucao.value = true;
        }

        await sales.handleEditLoading(setEditData);
      });
    }
  }
);

const verificarMudancaMonetaria = async () => {
  const valorBruto =
    Number(initialValues.venda.valorBruto) ===
    Number(formValues.venda.valorBruto);
  const valorLiquido =
    Number(initialValues.venda.valorLiquido) ===
    Number(formValues.venda.valorLiquido);

  if (valorBruto && valorLiquido) {
    mudancaValorFinal.value = false;
  } else {
    mudancaValorFinal.value = true;
  }

  const raw = toRaw(formValues);

  if (mudancaValorFinal.value && raw?.venda?.vendaPaga) {
    const openModal = new Promise(function (resolve) {
      $q.dialog({
        component: MiniModalLayout,
        componentProps: {
          classesTopBox: '!tw-justify-start !tw-mb-2 md:!tw-w-[400px]',
          scope: 'pagamento-ja-realizado',
          title: 'Pagamento já realizado',
          descriptionClass: 'tw-mt-2',
          description: `Alterações no valor da venda. Ao confirmar, o pagamento vigente será cancelado e um novo pagamento deverá ser realizado.`
        }
      })
        .onOk(() => {
          resolve(true);
        })
        .onCancel(() => {
          resolve(false);
        });
    });
    return await openModal;
  } else {
    return true;
  }
};

const loadingModal = ref(false);
const mudancaValorFinal = ref(false);

const enderecoDoClientePreenchido = async (cliente) => {
  cliente.endereco =
    cliente?.pessoaWithEndereco || cliente?.fornecedorWithEndereco;

  const modalPromise = new Promise((resolve) => {
    if (
      !cliente?.endereco ||
      cliente?.endereco.length === 0 ||
      !cliente?.endereco.bairro ||
      !cliente?.endereco.numero ||
      !cliente?.endereco.endereco
    ) {
      notify(
        'Os campos: Bairro, Logradouro e Número são obrigatórios no cadastro do destinatário/remetente para emitir NF-e'
      );

      resolve(false);
    } else {
      resolve(true);
    }
  });

  return await modalPromise;
};

const getPayload = () => {
  const payload = toRaw(formValues);

  payload.venda.valorBruto = round(pedidoPriceTable.value.grossTotalSum, 2);
  payload.venda.valorLiquido = round(pedidoPriceTable.value.total, 2);
  payload.venda.valorProdutos = round(
    pedidoPriceTable.value.productTotalSum,
    2
  );
  payload.venda.valorServicos = round(
    pedidoPriceTable.value.serviceTotalSum,
    2
  );
  payload.item = nfeItems.value;
  payload.referenciadas = notaReferenciada.value;

  return payload;
};

const validarProdutosComEstoqueZeradoOuNegativo = async (payload) => {
  // Verificar estoque dos itens antes de enviar para o back.
  if (bloquearVendaEstoqueNegativoOuZerado.value) {
    // Realiza o somatório de cada produto da tabela e verifica se tem estoque suficiente.
    const groupedItems = payload?.item?.reduce((acc, item) => {
      const codProduto = item.codProduto;
      const qtdeEstoque = Number(item?.qtdeEstoque);

      if (item.deletar) return {};

      if (!acc[codProduto]) {
        acc[codProduto] = { qtde: 0 };
      }

      const quantidadeItem = item.controle
        ? Number(item.qtde) - Number(item.qtdeInicial ?? 0)
        : Number(item.qtde);

      acc[codProduto].qtde += quantidadeItem;
      acc[codProduto].item = item;
      acc[codProduto].estoqueSuficiente = acc[codProduto].qtde <= qtdeEstoque;
      return acc;
    }, {});

    let itensEstoqueZerado = Object.values(groupedItems).filter(
      (item) => !item.estoqueSuficiente
    );

    if (itensEstoqueZerado.length > 0) {
      return itensEstoqueZerado.map((item) => item.item);
    }

    return [];
  }

  return [];
};

const handleModalProdutoComEstoqueZeradoOuNegativo = async (
  produtosComEstoqueZeradoOuNegativo
) => {
  await new Promise((resolve) => {
    $q.dialog({
      component: MiniModalLayout,
      componentProps: {
        componentRef: NoStockItems,
        scope: 'sem-estoque',
        title: 'Sem estoque!',
        hasCancel: false,
        confirmLabel: 'Ok',
        dataModal: {
          items: produtosComEstoqueZeradoOuNegativo,
          reservaEstoque: false
        }
      }
    })
      .onOk(() => {
        resolve(false);
      })
      .onCancel(() => {
        resolve(false);
      });
  });
};

const fetchNfeEditData = async (controle) => {
  await sales.get(controle).then(async () => {
    if (salesData.value?.modulo === '4') {
      isDevolucao.value = true;
    }

    await sales.handleEditLoading(setEditData);
  });
};

/* FUNÇÃO DE SALVAR OS DADOS E EMITIR NFE */
async function issueNfe() {
  let payload = toRaw(formValues);

  payload.item = nfeItems.value;

  const produtosComEstoqueZeradoOuNegativo =
    await validarProdutosComEstoqueZeradoOuNegativo(payload);

  if (produtosComEstoqueZeradoOuNegativo.length > 0) {
    return await handleModalProdutoComEstoqueZeradoOuNegativo(
      produtosComEstoqueZeradoOuNegativo
    );
  }

  if (!isDevolucao.value) {
    const mudancaMonetaria = await verificarMudancaMonetaria();

    if (!mudancaMonetaria) return;
  }

  completed.value = false;
  notifyLoading();

  payload.venda.valorBruto = round(pedidoPriceTable.value.grossTotalSum, 2);
  payload.venda.valorLiquido = round(pedidoPriceTable.value.total, 2);
  payload.venda.valorProdutos = round(
    pedidoPriceTable.value.productTotalSum,
    2
  );
  payload.venda.valorServicos = round(
    pedidoPriceTable.value.serviceTotalSum,
    2
  );

  // Em emitir nf-e de devolucao, nao se deve enviar as referenciadas no payload
  payload.referenciadas = isDevolucao.value ? [] : notaReferenciada.value;

  let response;

  /* CASO SEJA ROTA EDITAR */
  if (route.path.includes('editar') && !props.modal) {
    response = await sales.put(payload, payload.controle, true);
  } else {
    /* CASO NÃO SEJA EDITAR */

    response = await sales.post(payload, false, true);
  }

  completed.value = true;
  notifyLoading({ done: true });

  /* SE DEU SUCESSO */
  if (response?.success) {
    const { controle } = response.data.venda;
    isWarningAnimationDisabled.value = true;

    const totalTaxes = await openTotalTaxesModal(controle);
    if (!totalTaxes) {
      router.push(`/vendas/nf/editar/${controle}`);
      return await fetchNfeEditData(controle);
    }

    const { venda_paga: vendaPaga, valorfinal: valorFinal } = totalTaxes.venda;

    const vendaUsandoNaturezaDeAjusteOuComplementarComValorZerado =
      naturezaOperacaoDeAjuste.value ||
      (naturezaOperacaoComplementar.value && valorFinal <= 0);

    if (
      isDevolucao.value ||
      (vendaPaga && valorFinal && valorFinal == totalTaxes.vNF) ||
      vendaUsandoNaturezaDeAjusteOuComplementarComValorZerado
    ) {
      // Verifica se o valor final da venda JÁ paga foi alterado.
      // e mostra modal para confirmação

      loadingModal.value = true;

      // Emite e imprime a NF-e.
      const response = await issuePrintNfe(controle, { modelo: '55' });

      if (response) {
        router.push('/vendas/nf');
      } else {
        sales.get(controle).then(async () => {
          if (salesData.value?.modulo === '4') {
            isDevolucao.value = true;
          }

          await sales.handleEditLoading(setEditData);
        });

        loadingModal.value = false;
      }
    } else {
      const paymentFlowResponse = await handlePaymentFlow(
        response,
        true,
        totalTaxes
      );

      if (!paymentFlowResponse && !route.path.includes('editar')) {
        router.push(`/vendas/nf/editar/${controle}`);
      } else if (!paymentFlowResponse && route.path.includes('editar')) {
        fetchNfeEditData(controle);
      }
    }
  }
}

const handleGerarEspelho = async () => {
  const payload = await getPayload();

  const responseEspelho = await espelho.post({
    modelo: '55',
    payload
  });

  if (responseEspelho.success) {
    const base64String = await arrayBufferToBase64(responseEspelho.data);
    printBase64Pdf(base64String);
  }
};

/* FUNÇÃO DE SALVAR OS DADOS */
async function save() {
  completed.value = false;
  notifyLoading();

  let payload = toRaw(formValues);

  payload.venda.valorBruto = round(pedidoPriceTable.value.grossTotalSum, 2);
  payload.venda.valorLiquido = round(pedidoPriceTable.value.total, 2);
  payload.venda.valorProdutos = round(
    pedidoPriceTable.value.productTotalSum,
    2
  );
  payload.venda.valorServicos = round(
    pedidoPriceTable.value.serviceTotalSum,
    2
  );
  payload.item = nfeItems.value;

  // Em emitir nf-e de devolucao, nao se deve enviar as referenciadas no payload
  payload.referenciadas = isDevolucao.value ? [] : notaReferenciada.value;

  let response;

  /* CASO SEJA ROTA EDITAR */
  if (route.path.includes('editar') && !props.modal) {
    response = await sales.put(payload, payload.controle);
  } else {
    /* CASO NÃO SEJA EDITAR */
    response = await sales.post(payload);
  }

  completed.value = true;
  notifyLoading({ done: true });

  /* SE DEU SUCESSO */
  if (response?.success) {
    isWarningAnimationDisabled.value = true;
    if (modal.value) {
      emit('ok');
    } else {
      router.push('/vendas/nf');
    }
  }
}

function reset() {
  $q.dialog({
    component: RegisterUndo
  }).onOk(async () => {
    resetForm();
    // tipoData.value = initialValues.salesWithtipoCadastro;

    if (route.path.includes('editar') && params.controle) {
      employeeEditValue.value = initialValues.venda.funcionario;
      operationNatureEditValue.value = initialValues.venda.natureza;
    } else {
      operationNatureEditValue.value = OPERATION_NATURE_DEFAULT;
      employeeEditValue.value = EMPLOYEE_DEFAULT_VALUE;
      productInputRefs.value?.reset();
    }

    nfeItems.value = initialValues.item ? _.cloneDeep(initialValues.item) : [];

    mudancaValorFinal.value = false;

    consumidorFinalCheckbox.value = initialValues.venda.consumidorFinal;
    intermediadorEditValue.value = initialValues.venda.intermediador;
    clientEditValue.value = initialValues.venda.cliente;

    autoFillForm.value.pessoa.indIe =
      initialValues.venda.cliente?.pessoaWithFiscal?.indicadorIe?.descricao;
    autoFillForm.value.pessoa.logradouro =
      initialValues.venda.cliente?.pessoaWithEndereco?.endereco;
    autoFillForm.value.pessoa.estado =
      initialValues.venda.cliente?.pessoaWithEndereco?.uf;
    autoFillForm.value.pessoa.cidade =
      initialValues.venda.cliente?.pessoaWithEndereco?.cidade;
    autoFillForm.value.pessoa.complemento =
      initialValues.venda.cliente?.pessoaWithEndereco?.complemento;
    autoFillForm.value.pessoa.bairro =
      initialValues.venda.cliente?.pessoaWithEndereco?.bairro;
    autoFillForm.value.pessoa.cep =
      initialValues.venda.cliente?.pessoaWithEndereco?.cep;
    autoFillForm.value.pessoa.numero =
      initialValues.venda.cliente?.pessoaWithEndereco?.numero;
    autoFillForm.value.pessoa.ie =
      initialValues.venda.cliente?.pessoaWithFiscal?.ie;
    autoFillForm.value.pessoa.documento = initialValues.venda.cliente?.cnpjCpf;
  });
}

// Watcher do desfazer alterações
watch(
  formValues,
  (/*newValue*/) => {
    if (initialValues) {
      if (
        (Number(localStorage?.disableUndoEdit) &&
          route.path.includes('editar')) ||
        (Number(localStorage?.disableUndoCreate) &&
          !route.path.includes('editar'))
      ) {
        isWarningAnimationDisabled.value = true;
      } else {
        isWarningAnimationDisabled.value = true;
        // isWarningAnimationDisabled.value = _.isEqual(initialValues, newValue);
      }
    }
  },
  {}
);

const atalhos = [
  {
    key: 'num_add,=',
    event: () => {
      acrescimoInputRef.value.focusInput();
    }
  },
  {
    key: 'num_subtract,-',
    event: () => {
      descontoInputRef.value.focusInput();
    }
  }
];

// Definir atalhos
useScopedHotkeys(atalhos, 'register-sessao-emissao-nf');

// Definir ações da action bar
const actionBarEvents = {
  issueNfe: {
    callback: issueNfe
  },
  espelhoPadrao: {
    callback: handleGerarEspelho
  },
  save: {
    callback: save
  },
  cancel: {
    callback: cancel
  },
  reset: {
    callback: reset
  },
  'completed-changed': {
    callback: handleCompletedChanged
  }
};

const actionBarEventsReadOnly = {
  goBack: {
    callback: cancel
  }
};

// Definir eventos da action bar
const actionBarButtons = useRegisterNfe({
  params: { modal: props.modal, isWarningAnimationDisabled, completed },
  readonly: readonlyOrDisable.value,
  events: readonlyOrDisable.value ? actionBarEventsReadOnly : actionBarEvents,
  isDevolucao
});
</script>
